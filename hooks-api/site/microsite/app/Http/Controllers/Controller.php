<?php

namespace App\Http\Controllers;

use App\Models\RequestsCache;

abstract class Controller
{

    /**
     * Reads data from cache.
     *
     * @param string $cacheRequest The cache key.
     * @param string $type The cache type.
     * @return RequestsCache|null The cached response, or null if not found.
     */
    protected function readFromCache(string $cacheRequest, string $type): ?RequestsCache
    {
        return RequestsCache::where('request', $cacheRequest)->where('type', $type)->first();
    }

    /**
     * Writes data to cache.
     *
     * @param string $cacheRequest The cache key.
     * @param string $cacheResponse The response to cache.
     * @param string $type The cache type.
     * @return RequestsCache The created cache record.
     */
    protected function writeToCache(string $cacheRequest, string $cacheResponse, string $type): RequestsCache
    {
        return RequestsCache::create([
            'request' => $cacheRequest,
            'response' => $cacheResponse,
            'type' => $type,
        ]);
    }



}
