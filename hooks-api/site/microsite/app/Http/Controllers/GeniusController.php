<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\RequestsCache;
use App\Models\Project;
use App\Models\Record;

class GeniusController extends Controller
{
    /**
     * Retrieves and displays project and record data.
     *
     * @param Request $request The incoming HTTP request.
     * @return void
     */
    public function index(Request $request)
    {
        $project = [];
        $records = [];
        $data = $request->all(); // Get all request parameters.

        // Attempt to retrieve project data from the database.
        $projectFromDb = Project::with('records')->find(env('GENIUS_PROJECT_ID'));

        if ($projectFromDb) {
            echo '<div>FROM_DB</div>' . "\n";
            $project = $projectFromDb->toArray();
            $records = $project['records'];
            unset($project['records']); // Remove records from the project array.
        } else {
            echo '<div>FROM_GENIUS</div>' . "\n";
            $token = $this->getToken(); // Retrieve authentication token.

            if ($token) {
                $data = $this->adjustPagination($data); // Adjust pagination parameters.

                $requestData = [
                    'url' => env('GENIUS_PROJECT_URL'),
                    'body' => $data,
                ];

                $cacheKey = json_encode($requestData); // Create a cache key.

                // Make an API call to Genius.
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token,
                ])->post($requestData['url'], $requestData['body']);

                $responseJson = $response->json(); // Decode the JSON response.

                if (isset($responseJson['data'])) {
                    $project = $responseJson['data']['project'];
                    $records = $responseJson['data']['records']['data'];
                    unset($project['records']); // Remove records from the project array.

                    // Merge pagination data into the request data.
                    $data = array_merge($data, $responseJson['data']['records']);

                    // Insert or update project and record data in the database.
                    $this->insertOrUpdateProject($project);
                    $this->insertOrUpdateRecords($records);
                }
            }
        }

        $this->debugOutput($project, $records); // Output debug information.

        // return view('genius.index', compact('project', 'records', 'data'));
    }

    /**
     * Adjusts pagination parameters based on user input.
     *
     * @param array $data The request data.
     * @return array The updated request data with adjusted pagination.
     */
    private function adjustPagination(array $data): array
    {
        if (isset($data['page'])) {
            if ($data['page'] === 'Next') {
                $data['page'] = ($data['current_page'] ?? 0) + 1; // Increment page number.
            } elseif ($data['page'] === 'Previous') {
                $data['page'] = ($data['current_page'] ?? 1) - 1; // Decrement page number.
            }
        }
        return $data;
    }

    /**
     * Retrieves an authentication token from the Genius API.
     *
     * @return string|null The authentication token, or null if retrieval fails.
     */
    private function getToken(): ?string
    {
        $response = Http::withHeaders(['Accept' => 'application/json'])
            ->post(env('GENIUS_LOGIN_URL'), [
                'email' => env('GENIUS_EMAIL'),
                'password' => env('GENIUS_PASSSWORD'),
            ]);

        return $response->json('data.access_token'); // Extract the access token.
    }

    /**
     * Inserts or updates project data in the database.
     *
     * @param array $project The project data.
     * @return void
     */
    private function insertOrUpdateProject(array $project): void
    {
        Project::updateOrCreate(['id' => $project['id']], $project); // Use updateOrCreate for efficiency.
    }

    /**
     * Inserts or updates record data in the database.
     *
     * @param array $records The record data.
     * @return void
     */
    private function insertOrUpdateRecords(array $records): void
    {
        foreach ($records as $record) {
            Record::updateOrCreate(['id' => $record['id']], $record); // Use updateOrCreate for efficiency.
        }
    }

    /**
     * Outputs debug information (project and records) to the browser.
     *
     * @param array|null $project The project data.
     * @param array $records The record data.
     * @return void
     */
    private function debugOutput(?array $project, array $records): void
    {
        echo '<div>$project:' . "\n";
        echo '<pre><code>';
        print_r($project);
        echo '</code></pre></div>' . "\n";

        echo '<div>$records:' . "\n";
        echo '<pre><code>';
        print_r($records);
        echo '</code></pre></div>' . "\n";
    }



    /**
     * Retrieves and displays project and record data with caching.
     *
     * @param Request $request The incoming HTTP request.
     * @return void
     */
    public function indexWithCache(Request $request)
    {
        $project = [];
        $records = [];
        $data = $request->all(); // Get all request parameters.

        $token = $this->getToken(); // Retrieve authentication token.

        if ($token) {
            $data = $this->adjustPagination($data); // Adjust pagination parameters.

            $requestData = [
                'url' => env('GENIUS_PROJECT_URL'),
                'body' => $data,
            ];

            $cacheKey = json_encode($requestData); // Create a cache key.
            $cachedResponse = $this->readFromCache($cacheKey, 'genius'); // Attempt to read from cache.

            if ($cachedResponse) {
                $responseJson = json_decode($cachedResponse->response, true); // Decode cached JSON.
            } else {
                // Make an API call to Genius.
                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token,
                ])->post($requestData['url'], $requestData['body']);

                $responseJson = $response->json(); // Decode the JSON response.
                $this->writeToCache($cacheKey, json_encode($responseJson), 'genius'); // Write to cache.
            }

            if (isset($responseJson['data'])) {
                $project = $responseJson['data']['project'];
                $records = $responseJson['data']['records']['data'];
                unset($project['records']); // Remove records from the project array.

                // Merge pagination data into the request data.
                $data = array_merge($data, $responseJson['data']['records']);
            }
        }

        $this->debugOutput($project, $records); // Output debug information.

        // return view('genius.index', compact('project', 'records', 'data'));
    }

}
