<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use App\Models\RequestsCache;
use App\Models\Project;
use App\Models\Record;


class GeniusController extends Controller
{

    public function index() {

        $project = [];
        $records = [];
        $data = request()->all();

        $project = Project::where('id', env('GENIUS_PROJECT_ID'))
                    ->with('records')
                    ->first();

        if( !empty($project) ) {

echo '<div>';
echo "FROM_DB";
echo '</div>';
echo "\n";
            $records = $project->records->toArray();
            $project = $project->toArray();
            unset($project['records']);


        } else {

echo '<div>';
echo "FROM_GENIUS";
echo '</div>';
echo "\n";

            $token = $this->getToken();

            if( !empty($token) ) {

                if( isset($data['page']) && $data['page'] == 'Next') {
                    $data['page'] = $data['current_page'] + 1;
                } else if( isset($data['page']) && $data['page'] == 'Next') {
                    $data['page'] = $data['current_page'] - 1;
                }

                $requestUrl = env('GENIUS_PROJECT_URL');
                $requestBody = $data;
                $cacheRequest = json_encode([
                                'url' => $requestUrl,
                                'body' => $requestBody
                            ]);

                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token
                ])->post($requestUrl, $requestBody);

                $responseJson = $response->json();

                if( isset($responseJson['data']) ) {
                    $project = $responseJson['data']['project'];
                    unset($project['records']);
                    $records = $responseJson['data']['records']['data'];

                    $data['current_page'] = $responseJson['data']['records']['current_page'];
                    $data['from'] = $responseJson['data']['records']['from'];
                    $data['last_page'] = $responseJson['data']['records']['last_page'];
                    $data['next_page_url'] = $responseJson['data']['records']['next_page_url'];
                    $data['path'] = $responseJson['data']['records']['path'];
                    $data['per_page'] = $responseJson['data']['records']['per_page'];
                    $data['prev_page_url'] = $responseJson['data']['records']['prev_page_url'];
                    $data['to'] = $responseJson['data']['records']['to'];
                    $data['total'] = $responseJson['data']['records']['total'];

                    $this->insertOrUpdateProject($project);
                    $this->insertOrUpdateRecords($records);

                }

            }

        }

echo '<div>';
echo "\$project:\n";
echo '<pre><code>';
print_r( $project );
echo '</code></pre>';
echo '</div>';
echo "\n";

echo '<div>';
echo "\$records:\n";
echo '<pre><code>';
print_r( $records );
echo '</code></pre>';
echo '</div>';
echo "\n";

        // return view('genius.index', compact('project', 'records', 'data'));


    }




    public function indexWithCache() {

        $project = [];
        $records = [];
        $data = request()->all();

        $token = $this->getToken();

        if( !empty($token) ) {

            if( isset($data['page']) && $data['page'] == 'Next') {
                $data['page'] = $data['current_page'] + 1;
            } else if( isset($data['page']) && $data['page'] == 'Next') {
                $data['page'] = $data['current_page'] - 1;
            }

            $requestUrl = env('GENIUS_PROJECT_URL');
            $requestBody = $data;
            $cacheRequest = json_encode([
                            'url' => $requestUrl,
                            'body' => $requestBody
                        ]);

            $cached = $this->readFromCache($cacheRequest, 'genius');

            if( !empty($cached) ) {

                $responseJson = json_decode( $cached->response, true );

            } else {

                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer ' . $token
                ])->post($requestUrl, $requestBody);

                $responseJson = $response->json();

                $this->writeToCache($cacheRequest, json_encode($responseJson), 'genius');

            }



            if( isset($responseJson['data']) ) {
                $project = $responseJson['data']['project'];
                unset($project['records']);
                $records = $responseJson['data']['records']['data'];

                $data['current_page'] = $responseJson['data']['records']['current_page'];
                $data['from'] = $responseJson['data']['records']['from'];
                $data['last_page'] = $responseJson['data']['records']['last_page'];
                $data['next_page_url'] = $responseJson['data']['records']['next_page_url'];
                $data['path'] = $responseJson['data']['records']['path'];
                $data['per_page'] = $responseJson['data']['records']['per_page'];
                $data['prev_page_url'] = $responseJson['data']['records']['prev_page_url'];
                $data['to'] = $responseJson['data']['records']['to'];
                $data['total'] = $responseJson['data']['records']['total'];
            }

        }

echo '<div>';
echo "\$project:\n";
echo '<pre><code>';
print_r( $project );
echo '</code></pre>';
echo '</div>';
echo "\n";

echo '<div>';
echo "\$records:\n";
echo '<pre><code>';
print_r( $records );
echo '</code></pre>';
echo '</div>';
echo "\n";

        // return view('genius.index', compact('project', 'records', 'data'));
    }

    public function getToken()
    {

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])->post(env('GENIUS_LOGIN_URL'), [
            "email" => env('GENIUS_EMAIL'),
            "password" => env('GENIUS_PASSSWORD'),
        ]);

        $responseJson = $response->json();

        return isset($responseJson['data']['access_token']) ? $responseJson['data']['access_token'] : null;

    }


    public function insertOrUpdateProject($project) {

        $exists = Project::where('id', $project['id'])->first();

        if( !$exists ) {
            Project::create($project);
        } else {
            $exists->update($project);
        }

    }

    public function insertOrUpdateRecord($record) {

        $exists = Project::where('id', $record['id'])->first();

        if( !$exists ) {
            Record::create($record);
        } else {
            $exists->update($record);
        }

    }

    public function insertOrUpdateRecords($records) {

        foreach($records as $record) {
            $this->insertOrUpdateRecord($record);
        }

    }


}
