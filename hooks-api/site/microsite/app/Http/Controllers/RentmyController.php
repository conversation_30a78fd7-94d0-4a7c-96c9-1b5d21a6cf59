<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\RequestsCache;
use App\Models\Project;
use App\Models\Catalog;
use App\Models\Product;
use App\Models\Category;
use App\Models\Setting;
use App\Models\Navigation;
use App\Models\Related;
use App\Models\Page;
use App\Models\Content;


class RentmyController extends Controller
{

    private $token;

    /**
     * Retrieves and displays catalog, categories, navigation, and settings data for the main index page.
     *
     * @param Request $request The incoming HTTP request.
     * @return \Illuminate\Contracts\View\View The rendered view with data.
     */
    public function index(Request $request)
    {
        // Retrieve catalog data, either from the database or the Rentmy API.
        $responseJson = $this->getCatalog();

        // Retrieve categories data, either from the database or the Rentmy API.
        $categoriesJson = $this->getCategories();

        // Retrieve navigation data, either from the database or the Rentmy API.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data from the database.
        $settings = Setting::first();

        // Return the view with the retrieved data.
        return view('rentmy.index', [
            'products' => $responseJson['result']['data'] ?? [],
            'categories' => $categoriesJson['result']['data'] ?? [],
            'settings' => $settings,
            'navigation' => $navigationJson['result']['data'] ?? [],
        ]);
    }

    public function getSiteContents()
    {

        $contents = Content::all();

        if( $contents->count() > 0 ) {
            return [
                'status' => 'OK',
                'result' => [
                    'data' => $contents->toArray(),
                ]
            ];
        }

        $setting = Setting::first();
        // Retrieve the authentication token.
        $token = $setting->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => $setting->api_base_url . 'pages/contents'
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $contents = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $contentsJson = json_decode($contents, true);

            if(isset($contentsJson['result']['data'])) {
                foreach($contentsJson['result']['data'] as $content) {
                    Content::create($content);
                }
            }

            // Return the JSON response from the API.
            return $contentsJson;
        }
    }
    

/**
     * Retrieves and displays detailed product information based on the provided slug.
     *
     * This method first attempts to retrieve the product from the local database
     * using the provided slug. If the product is not found in the database, it
     * makes an API call to the Rentmy service to fetch the product details.
     * It then retrieves navigation, related products, and settings data to pass
     * to the view.
     *
     * @param string|null $slug The slug of the product to retrieve.
     * @param Request $request The incoming HTTP request.
     * @return \Illuminate\Contracts\View\View The rendered view with product details.
     */
    public function detail($slug = null, Request $request)
    {
        // Attempt to retrieve the product from the local database using the slug.
        $product = Product::where('slug', $slug)->first();

        // Check if the product was found in the database.
        if (false && !empty($product)) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "PRODUCT_FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array with the product details.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => $product->toArray(), // Convert the product model to an array.
                ]
            ];

            // Assign the prepared data to the response JSON.
            $responseJson = $data;
        } else {
            // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
            // echo '<div>';
            // echo "PRODUCT_FROM_RENTMY";
            // echo '</div>';
            // echo "\n";

            // Retrieve the authentication token.
            $token = $this->getToken();

            // Check if a token was successfully retrieved.
            if ($token) {
                // Prepare the API request data.
                $requestData = [
                    'url' => env('RENTMY_PRODUCT_URL') // URL from environment configuration.
                ];

                // Replace the placeholder '{slug}' in the URL with the actual slug.
                $requestData['url'] = str_replace('{slug}', $slug, $requestData['url']);

                // Prepare the API request headers.
                $headers = [
                    'accept: application/json',
                    'authorization: Bearer ' . $token, // Include the authentication token.
                    'Content-Type: application/json'
                ];

                // Make the API call using the curlGet method and retrieve the response.
                $response = $this->curlGet($requestData['url'], $headers);

                // Decode the JSON response from the API.
                $responseJson = json_decode($response, true);

                // Add the slug to the response data if it exists.
                if (!empty($responseJson['result']['data'])) {
                    $responseJson['result']['data']['slug'] = $slug;
                }

                // Write the retrieved product data to the database.
                //$this->writeToProduct($responseJson);
            }
        }

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve related products data using the product ID.
        $relatedJson = $this->getRelated($responseJson['result']['data']['id']);
        //$relatedJson = [];
        if( isset($relatedJson['result']['data']['id']) ) {
            $relatedJson = $this->getRelated($responseJson['result']['data']['id']);
        }

        // Retrieve settings data.
        $settings = Setting::first();

        $contents = $this->getContents();

        if( @$product['available'] > 0 && ( @$product['buy_price'] > 0 || isset($product['prices'][0]['base']['price']) ) ) {
            $detailView = 'detailbuy';
        } else {
            $detailView = 'detail';
        }

        if( isset($responseJson['result']['data']['seo']['meta_title']) ) {
            $responseJson['result']['data']['seo']['meta_title'] = str_replace('{{site_name}}', $settings->store_slug, $responseJson['result']['data']['seo']['meta_title']);
        }



        // Return the view with the retrieved data.
        return view('rentmy.'.$detailView, [
            'product' => $responseJson['result']['data'] ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'related' => $relatedJson['result']['data'] ?? [],
            'settings' => $settings,
            'contents' => $contents,
            'cart' => session('rentmy_cart') ?? [],
        ]);
    }

/**
     * Retrieves and displays products filtered by category ID.
     *
     * This method fetches products from the Rentmy API based on the provided
     * category ID. It also retrieves categories, navigation, and settings data
     * to pass to the view.
     *
     * @param int|null $id The category ID to filter products by.
     * @param Request $request The incoming HTTP request.
     * @return \Illuminate\Contracts\View\View The rendered view with filtered products.
     */
    public function category($id = null, Request $request)
    {
        // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
        // echo '<div>';
        // echo "CATALOG FROM_RENTMY";
        // echo '</div>';
        // echo "\n";

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Prepare the API request data.
        $requestData = [
            'url' => env('RENTMY_PRODUCTS_FILTERED_URL'), // URL from environment configuration.
            'body' => '{"limit":21,"page_no":1}', // Request body as JSON.
        ];

        // Replace the placeholder '{id}' in the URL with the actual category ID.
        $requestData['url'] = str_replace('{id}', $id, $requestData['url']);

        // Prepare the API request headers.
        $headers = [
            'accept: application/json',
            'authorization: Bearer ' . $token, // Include the authentication token.
            'Content-Type: application/json'
        ];

        // Make the API call using the curlPost method and retrieve the response.
        $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

        // Decode the JSON response from the API.
        $responseJson = json_decode($response, true);

        // Retrieve categories data.
        $categoriesJson = $this->getCategories();

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        // Return the view with the retrieved data.
        return view('rentmy.index', [
            'products' => $responseJson['result']['data'] ?? [],
            'categories' => $categoriesJson['result']['data'] ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);
    }

    /**
     * Retrieves navigation data, prioritizing data from the database.
     *
     * This method first attempts to retrieve navigation items from the local
     * database. If no navigation items are found, it fetches them from the
     * Rentmy API.
     *
     * @return array|false The navigation data as an array, or false if retrieval fails.
     */
    public function getNavigation()
    {
        // Attempt to retrieve all navigation items from the database.
        $navigation = Navigation::all();

        // Check if any navigation items were retrieved from the database.
        if ($navigation->count()) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "NAVIGATION_FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array to be returned, formatted to match the API response structure.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => [], // Initialize an empty array for navigation data.
                ],
            ];

            // Iterate through the retrieved navigation items and convert them to arrays.
            foreach ($navigation as $item) {
                $data['result']['data'][] = $item->toArray(); // Add each item's array representation to the data array.
            }

            // Return the formatted navigation data.
            return $data;
        } else {
            // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
            // echo '<div>';
            // echo "NAVIGATION_FROM_RENTMY";
            // echo '</div>';
            // echo "\n";

            // Retrieve the authentication token.
            $token = $this->getToken();

            // Check if a token was successfully retrieved.
            if ($token) {
                // Prepare the API request data.
                $requestData = [
                    'url' => env('RENTMY_NAVIGATION_URL') // URL from environment configuration.
                ];

                // Prepare the API request headers.
                $headers = [
                    'accept: application/json',
                    'authorization: Bearer ' . $token, // Include the authentication token.
                    'Content-Type: application/json'
                ];

                // Make the API call using the curlGet method and retrieve the response.
                $navigation = $this->curlGet($requestData['url'], $headers);

                // Decode the JSON response from the API.
                $navigationJson = json_decode($navigation, true);

                // Write the retrieved navigation data to the database.
                $this->writeToNavigation($navigationJson);

                // Return the JSON response from the API.
                return $navigationJson;
            }
        }

        // Return false if retrieval fails.
        return false;
    }

/**
     * Retrieves and displays project and record data using caching.
     *
     * This method attempts to retrieve data from a cache. If the data is not
     * found in the cache, it fetches the data from the Rentmy API and stores
     * it in the cache for future use.
     *
     * @param Request $request The incoming HTTP request.
     * @return void
     */
    public function indexWithCache(Request $request)
    {
        // Retrieve authentication token from the Rentmy API.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Define the API request parameters.
            $requestData = [
                'url' => env('RENTMY_PRODUCTS_URL'),
                'body' => '{"limit":21,"page_no":1,"all":true}',
            ];

            // Create a unique cache key based on the request data.
            $cacheKey = json_encode($requestData);

            // Attempt to retrieve the cached response using the cache key.
            $cachedResponse = $this->readFromCache($cacheKey, 'rentmy_products');

            // Check if a cached response was found.
            if ($cachedResponse) {
                // Output a message indicating data was retrieved from the cache.
                // echo '<div>';
                // echo "FROM_DB";
                // echo '</div>';
                // echo "\n";

                // Decode the cached JSON response.
                $responseJson = json_decode($cachedResponse->response, true);
            } else {
                // Output a message indicating data was retrieved from the API.
                // echo '<div>';
                // echo "FROM_RENTMY";
                // echo '</div>';
                // echo "\n";

                // Prepare the headers for the API request.
                $headers = [
                    'accept: application/json',
                    'authorization: Bearer ' . $token,
                    'Content-Type: application/json'
                ];

                // Make the API request using curlPost.
                $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

                // Decode the API response JSON.
                $responseJson = json_decode($response, true);

                // Store the API response in the cache.
                $this->writeToCache($cacheKey, json_encode($responseJson), 'rentmy_products');
            }
        }

        // Output the JSON response for debugging purposes.
        // echo '<div>';
        // echo "\$responseJson:\n";
        // echo '<pre><code>';
        // print_r($responseJson);
        // echo '</code></pre>';
        // echo '</div>';
        // echo "\n";

        // Terminate script execution after outputting the response.
        die();

        // Commented out: intended for view rendering, but currently disabled.
        // return view('genius.index', compact('project', 'records', 'data'));
    }


/**
     * Retrieves catalog data, prioritizing data from the database. If not available, fetches from the Rentmy API.
     *
     * @return array The catalog data as an array.
     */
    public function getCatalog()
    {
        // Attempt to retrieve all catalog items from the database.
        $catalog = Catalog::all();

        // Check if any catalog items were retrieved from the database.
        if ($catalog->count()) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "CATALOG FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array to be returned, formatted to match the API response structure.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => [], // Initialize an empty array for catalog data.
                ],
                'total' => null, // Placeholder for total count.
                'limit' => 21,    // Default limit (likely from API).
                'page' => 1,     // Default page number.
            ];

            // Iterate through the retrieved catalog items and convert them to arrays.
            foreach ($catalog as $item) {
                $data['result']['data'][] = $item->toArray(); // Add each item's array representation to the data array.
            }

            // Set the total count of catalog items.
            $data['total'] = count($catalog);

            // Return the formatted catalog data.
            return $data;
        } else {
            // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
            // echo '<div>';
            // echo "CATALOG FROM_RENTMY";
            // echo '</div>';
            // echo "\n";

            // Check if the authentication token is already set; if not, retrieve it.
            if (empty($this->token)) {
                $this->token = $this->getToken(); // Retrieve the token.
                if (empty($this->token)) {
                    // If the token retrieval fails, terminate the script.
                    die("Can't get token");
                }
            }

            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_PRODUCTS_URL'), // URL from environment configuration.
                'body' => '{"limit":21,"page_no":1,"all":true}', // Request body as JSON.
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $this->token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlPost method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);
            $responseJson = json_decode($response, true); // Decode the JSON response.

            // Write the retrieved catalog data to the database.
            $this->writeToCatalog($responseJson);

            // Return the JSON response from the API.
            return $responseJson;
        }
    }

    /**
     * Retrieves categories data, prioritizing data from the database. If not available, fetches from the Rentmy API.
     *
     * @return array The categories data as an array.
     */
    public function getCategories()
    {
        // Attempt to retrieve all categories from the database.
        $categories = Category::all();

        // Check if any categories were retrieved from the database.
        if ($categories->count()) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "CATEGORIES FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array to be returned, formatted to match the API response structure.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => [], // Initialize an empty array for categories data.
                ]
            ];

            // Iterate through the retrieved categories and convert them to arrays.
            foreach ($categories as $item) {
                $data['result']['data'][] = $item->toArray(); // Add each item's array representation to the data array.
            }

            // Return the formatted categories data.
            return $data;
        } else {
            // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
            // echo '<div>';
            // echo "CATEGORIES FROM_RENTMY";
            // echo '</div>';
            // echo "\n";

            // Check if the authentication token is already set; if not, retrieve it.
            if (empty($this->token)) {
                $this->token = $this->getToken(); // Retrieve the token.
                if (empty($this->token)) {
                    // If the token retrieval fails, terminate the script.
                    die("Can't get token");
                }
            }

            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_PRODUCTS_URL') // URL from environment configuration.
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $this->token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $categories = $this->curlGet(env('RENTMY_CATEGORIES_URL'), $headers);
            $categoriesJson = json_decode($categories, true); // Decode the JSON response.

            // Write the retrieved categories data to the database.
            $this->writeToCategories($categoriesJson);

            // Return the JSON response from the API.
            return $categoriesJson;
        }
    }


/**
     * Retrieves an authentication token from the Rentmy API.
     *
     * This method attempts to retrieve the authentication token. If the token is
     * already stored in the class property `$this->token`, it returns the stored
     * token. Otherwise, it retrieves the store name from the settings, constructs
     * the login URL, makes an HTTP request to the Rentmy API, and extracts the token
     * from the response.
     *
     * @return string|null The authentication token, or null if retrieval fails.
     */
    private function getToken(): ?string
    {
        // Check if the token is already stored in the class property.
        if (!empty($this->token)) {
            // If the token exists, return it immediately.
            return $this->token;
        }

        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();

        // Check if a setting was found.
        if (!empty($setting)) {
            // If a setting exists, retrieve the store name from it.
            $store_name = $setting->store_name;
        } else {
            // If no setting exists, use a default store name.
            $store_name = 'modern-rentals';
        }

        // Retrieve the login URL from the environment variables.
        $loginUrl = env('RENTMY_LOGIN_URL');

        // Replace the placeholder '{store_name}' in the login URL with the actual store name.
        $loginUrl = str_replace('{store_name}', $store_name, $loginUrl);

        // Make an HTTP GET request to the Rentmy API to retrieve the authentication token.
        $response = Http::withHeaders(['Accept' => 'application/json'])->get($loginUrl);

        // Extract the authentication token from the JSON response.
        $this->token = $response->json('result.store.token');

        // Return the retrieved authentication token.
        return $this->token;
    }


/**
     * Retrieves related products based on the provided product ID.
     *
     * This method first attempts to retrieve related products from the local
     * database using the provided product ID. If no related products are found,
     * it fetches them from the Rentmy API.
     *
     * @param int|null $id The product ID to retrieve related products for.
     * @return array|false The related products data as an array, or false if retrieval fails.
     */
    public function getRelated($id = null)
    {
        // Attempt to retrieve related products from the database based on the product ID.
        $related = Related::where('product_id', $id)->get();

        // Check if any related products were retrieved from the database.
        if (false && $related->count()) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "RELATED_FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array to be returned, formatted to match the API response structure.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => [], // Initialize an empty array for related products data.
                ],
            ];

            // Iterate through the retrieved related products and convert them to arrays.
            foreach ($related as $item) {
                $data['result']['data'][] = $item->toArray(); // Add each item's array representation to the data array.
            }

            // Return the formatted related products data.
            return $data;
        } else {
            // Output a message indicating data will be fetched from the Rentmy API. (For debugging purposes)
            // echo '<div>';
            // echo "RELATED_FROM_RENTMY";
            // echo '</div>';
            // echo "\n";

            // Retrieve the authentication token.
            $token = $this->getToken();

            // Check if a token was successfully retrieved.
            if ($token) {
                // Prepare the API request data.
                $requestData = [
                    'url' => env('RENTMY_RELATED_PRODUCTS_URL') // URL from environment configuration.
                ];

                // Replace the placeholder '{id}' in the URL with the actual product ID.
                $requestData['url'] = str_replace('{id}', $id, $requestData['url']);

                // Prepare the API request headers.
                $headers = [
                    'accept: application/json',
                    'authorization: Bearer ' . $token, // Include the authentication token.
                    'Content-Type: application/json'
                ];

                // Make the API call using the curlGet method and retrieve the response.
                $related = $this->curlGet($requestData['url'], $headers);

                // Decode the JSON response from the API.
                $relatedJson = json_decode($related, true);

                // Write the retrieved related products data to the database.
                $this->writeToRelated($relatedJson, $id);

                // Return the JSON response from the API.
                return $relatedJson;
            }

            // Return false if token retrieval fails.
            return false;
        }
    }


/**
     * Sends a POST request using cURL.
     *
     * This method initializes a cURL session, sets various options including
     * the URL, request method, headers, and body, executes the request, and
     * returns the response.
     *
     * @param string $url The URL to send the POST request to.
     * @param array $body The request body (optional, defaults to an empty array).
     * @param array $headers The request headers (optional, defaults to an empty array).
     * @return string The response from the cURL request.
     */
    public function curlPost($url, $body = [], $headers = [])
    {
        // Initialize a new cURL session.
        $curl = curl_init();

        // Set cURL options using an associative array.
        curl_setopt_array($curl, [
            CURLOPT_URL => $url, // Set the URL to send the request to.
            CURLOPT_RETURNTRANSFER => true, // Return the response as a string instead of outputting it.
            CURLOPT_ENCODING => '', // Accept all supported encodings.
            CURLOPT_MAXREDIRS => 10, // Follow up to 10 redirects.
            CURLOPT_TIMEOUT => 0, // Set no timeout (wait indefinitely).
            CURLOPT_FOLLOWLOCATION => true, // Follow HTTP redirects.
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, // Use HTTP 1.1.
            CURLOPT_CUSTOMREQUEST => 'POST', // Set the request method to POST.
            CURLOPT_POSTFIELDS => $body, // Set the request body.
            CURLOPT_HTTPHEADER => $headers, // Set the request headers.
        ]);

        // Execute the cURL request and store the response.
        $response = curl_exec($curl);

        // Close the cURL session to free up resources.
        curl_close($curl);

        // Return the response from the cURL request.
        return $response;
    }

    /**
     * Sends a GET request using cURL.
     *
     * This method initializes a cURL session, sets various options including
     * the URL, request method, and headers, executes the request, and
     * returns the response.
     *
     * @param string $url The URL to send the GET request to.
     * @param array $headers The request headers (optional, defaults to an empty array).
     * @return string The response from the cURL request.
     */
    public function curlGet($url, $headers = [])
    {
        // Initialize a new cURL session.
        $curl = curl_init();

        // Set cURL options using an associative array.
        curl_setopt_array($curl, [
            CURLOPT_URL => $url, // Set the URL to send the request to.
            CURLOPT_RETURNTRANSFER => true, // Return the response as a string instead of outputting it.
            CURLOPT_ENCODING => '', // Accept all supported encodings.
            CURLOPT_MAXREDIRS => 10, // Follow up to 10 redirects.
            CURLOPT_TIMEOUT => 0, // Set no timeout (wait indefinitely).
            CURLOPT_FOLLOWLOCATION => true, // Follow HTTP redirects.
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, // Use HTTP 1.1.
            CURLOPT_CUSTOMREQUEST => 'GET', // Set the request method to GET.
            CURLOPT_HTTPHEADER => $headers, // Set the request headers.
        ]);

        // Execute the cURL request and store the response.
        $response = curl_exec($curl);

        // Close the cURL session to free up resources.
        curl_close($curl);

        // Return the response from the cURL request.
        return $response;
    }


/**
     * Writes catalog data to the database.
     *
     * This method iterates through the catalog items in the provided data array
     * and calls the writeToCatalogItem method to store each item in the database.
     *
     * @param array $data The catalog data array.
     * @return void
     */
    public function writeToCatalog($data = [])
    {
        // Check if the data array contains catalog items.
        if (isset($data['result']['data']) && !empty($data['result']['data'])) {
            // Iterate through each catalog item in the data array.
            foreach ($data['result']['data'] as $item) {
                // Call writeToCatalogItem to store the item in the database.
                $this->writeToCatalogItem($item);
            }
        }
    }

    /**
     * Writes a single catalog item to the database.
     *
     * This method uses the updateOrCreate method of the Catalog model to either
     * create a new catalog item or update an existing one based on the item's ID.
     *
     * @param array $item The catalog item data.
     * @return void
     */
    public function writeToCatalogItem($item = [])
    {
        // Check if the item array is not empty.
        if (!empty($item)) {
            // Use updateOrCreate to either create or update the catalog item.
            Catalog::updateOrCreate(['id' => $item['id']], $item);
        }
    }

    /**
     * Writes product data to the database.
     *
     * This method extracts the product data from the provided data array and
     * uses the updateOrCreate method of the Product model to either create a
     * new product or update an existing one based on the product's ID.
     *
     * @param array $data The product data array.
     * @return void
     */
    public function writeToProduct($data = [])
    {
        // Check if the data array contains product data.
        if (!empty($data['result']['data'])) {
            // Extract the product data from the data array.
            $product = $data['result']['data'];
            // Use updateOrCreate to either create or update the product.
            Product::updateOrCreate(['id' => $product['id']], $product);
        }
    }

    /**
     * Writes categories data to the database.
     *
     * This method iterates through the categories in the provided data array
     * and calls the writeToCategoriesItem method to store each category in the database.
     *
     * @param array $data The categories data array.
     * @return void
     */
    public function writeToCategories($data = [])
    {
        // Check if the data array contains categories.
        if (isset($data['result']['data']) && !empty($data['result']['data'])) {
            // Iterate through each category in the data array.
            foreach ($data['result']['data'] as $item) {
                // Call writeToCategoriesItem to store the category in the database.
                $this->writeToCategoriesItem($item);
            }
        }
    }

    /**
     * Writes a single category item to the database.
     *
     * This method uses the updateOrCreate method of the Category model to either
     * create a new category or update an existing one based on the item's ID.
     *
     * @param array $item The category item data.
     * @return void
     */
    public function writeToCategoriesItem($item = [])
    {
        // Check if the item array is not empty.
        if (!empty($item)) {
            // Use updateOrCreate to either create or update the category.
            Category::updateOrCreate(['id' => $item['id']], $item);
        }
    }

    /**
     * Writes navigation data to the database.
     *
     * This method iterates through the navigation items in the provided data array
     * and calls the writeToNavigationItem method to store each item in the database.
     *
     * @param array $data The navigation data array.
     * @return void
     */
    public function writeToNavigation($data = [])
    {
        // Check if the data array contains navigation items.
        if (isset($data['result']['data']) && !empty($data['result']['data'])) {
            // Iterate through each navigation item in the data array.
            foreach ($data['result']['data'] as $item) {
                // Call writeToNavigationItem to store the item in the database.
                $this->writeToNavigationItem($item);
            }
        }
    }

    /**
     * Writes a single navigation item to the database.
     *
     * This method uses the updateOrCreate method of the Navigation model to either
     * create a new navigation item or update an existing one based on the item's ID.
     *
     * @param array $item The navigation item data.
     * @return void
     */
    public function writeToNavigationItem($item = [])
    {
        // Check if the item array is not empty.
        if (!empty($item)) {
            // Use updateOrCreate to either create or update the navigation item.
            Navigation::updateOrCreate(['id' => $item['id']], $item);
        }
    }

    /**
     * Writes related products data to the database.
     *
     * This method iterates through the related products in the provided data array
     * and calls the writeToRelatedItem method to store each related product in the database.
     *
     * @param array $data The related products data array.
     * @param int $product_id The product ID to associate with the related products.
     * @return void
     */
    public function writeToRelated($data = [], $product_id)
    {
        // Check if the data array contains related products.
        if (isset($data['result']['data']) && !empty($data['result']['data'])) {
            // Iterate through each related product in the data array.
            foreach ($data['result']['data'] as $item) {
                // Call writeToRelatedItem to store the related product in the database.
                $this->writeToRelatedItem($item, $product_id);
            }
        }
    }

    /**
     * Writes a single related product item to the database.
     *
     * This method sets the product ID for the related product and uses the
     * updateOrCreate method of the Related model to either create a new related
     * product or update an existing one based on the item's ID.
     *
     * @param array $item The related product item data.
     * @param int $product_id The product ID to associate with the related product.
     * @return void
     */
    public function writeToRelatedItem($item = [], $product_id)
    {
        // Check if the item array is not empty.
        if (!empty($item)) {
            // Set the product ID for the related product.
            $item['product_id'] = $product_id;
            // Use updateOrCreate to either create or update the related product.
            Related::updateOrCreate(['id' => $item['id']], $item);
        }
    }

    public function addToCart()
    {

        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_ADD_TO_CART_URL'), // URL from environment configuration.
                'body' => [
                    'deposit_amount' => $requestAll['deposit_amount'],
                    'deposite_tax' => $requestAll['deposite_tax'],
                    'driving_license_required' => $requestAll['driving_license_required'],
                    'price' => $requestAll['price'],
                    'quantity' => $requestAll['quantity'],
                    'variants_products_id' => $requestAll['variants_products_id'],
                    'location' => $requestAll['location'],
                    'rent_start' => @$requestAll['rent_start'],
                    'rent_end' => @$requestAll['rent_end'],
                    'fullfilment_option' => $requestAll['fullfilment_option'],
                    'rental_duration' => $requestAll['rental_duration'],
                    'rental_type' => $requestAll['rental_type'],
                    'sales_tax' => $requestAll['sales_tax'],
                    'term' => $requestAll['term'],
                    'token' => $requestAll['token'],
                    'price_id' => $requestAll['price_id'],
                    'custom_fields' => $requestAll['custom_fields'],
                    'is_apply' => $requestAll['is_apply'],
                    'zone' => $requestAll['zone']
                ],
            ];

            if( isset($requestAll['package_id']) ) {
                $requestData['body']['package_id'] = $requestAll['package_id'];
            } else {
                $requestData['body']['product_id'] = @$requestAll['product_id'];
            }

            $requestData['body'] = json_encode( $requestData['body'] );

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);


            if (isset($responseJson['result']['error'])) {
                session()->flash('error', 'ERROR: ' .  $responseJson['result']['error']);
                if( isset($requestAll['package_id']) ){
                    return redirect( route('rentmy.package', ['slug' => $requestAll['slug']]) );
                }                
                return redirect( route('rentmy.detail', ['slug' => $requestAll['slug']]) );
            }

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return redirect(route('rentmy.cart', $cartData['token']));
            }

            session()->flash('error', 'ERROR: Failed to add item to cart. Please try again.');
            if( isset($requestAll['package_id']) ){
                return redirect( route('rentmy.package', ['slug' => $requestAll['slug']]) );
            }
            return redirect(route('rentmy.detail', ['slug' => $requestAll['slug']]));
        }

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        // Return the view with the retrieved data.
        return view('rentmy.cart', [
            'cart' => $responseJson['result']['data'] ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);

    }

    public function checkout()
    {

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        $cart = session('rentmy_cart');

        if( !isset($cart['token']) ){
            return redirect( route('rentmy.index') );
        }

        // Return the view with the retrieved data.
        return view('rentmy.checkout', [
            'cart' => $cart,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);

    }

    public function thankYou()
    {

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        // Return the view with the retrieved data.
        return view('rentmy.thank_you', [
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);

    }

    public function cart($token = null)
    {

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();


        $cart = session('rentmy_cart');
        if( isset($cart['cart_items']) ) {
            foreach($cart['cart_items'] as &$item){
                if( !empty($item['product']['options']) ) {
                    $item['product']['options'] = json_decode($item['product']['options'], true);
                }
            }
        }


        // Return the view with the retrieved data.
        return view('rentmy.cart', [
            'cart' => $cart ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);

    }

    public function clearCart()
    {
        session()->forget('rentmy_cart');
        return response()->json([
            'success' => true,
            'message' => 'Cart cleared.',
        ]);
    }

    public function checkCartSession()
    {
        $cart = session('rentmy_cart');
        if( !isset($cart['token']) ){
            return response()->json([
                'success' => false,
                'message' => 'Cart not found.',
            ]);
        }
        return response()->json([
            'success' => true,
            'message' => 'Cart found.',
            'data' => $cart,
        ]);
    }

    public function placeOrder()
    {

        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_PLACE_ORDER_URL'), // URL from environment configuration.
                'body' => [
                    'first_name' => $requestAll['first_name'] ?? '',
                    'last_name' => $requestAll['last_name'] ?? '',
                    'mobile' => $requestAll['mobile'] ?? '',
                    'email' => $requestAll['email'] ?? '',
                    'company' => $requestAll['company'] ?? '',
                    'address_line1' => $requestAll['address_line1'] ?? '',
                    'combinedAddress' => $requestAll['combinedAddress'] ?? '',
                    'address_line2' => $requestAll['address_line2'] ?? '',
                    'city' => $requestAll['city'] ?? '',
                    'state' => $requestAll['state'] ?? '',
                    'country' => $requestAll['country'] ?? '',
                    'zipcode' => $requestAll['zipcode'] ?? '',
                    'special_instructions' => $requestAll['special_instructions'] ?? '',
                    'special_requests' => $requestAll['special_requests'] ?? '',
                    'driving_license' => $requestAll['driving_license'] ?? '',
                    'fieldText' => $requestAll['fieldText'] ?? '',
                    'fieldSelection' => $requestAll['fieldSelection'] ?? '',
                    'custom_checkout' => $requestAll['custom_checkout'] ?? '',
                    'shipping_first_name' => $requestAll['shipping_first_name'] ?? '',
                    'shipping_last_name' => $requestAll['shipping_last_name'] ?? '',
                    'shipping_mobile' => $requestAll['shipping_mobile'] ?? '',
                    'shipping_email' => $requestAll['shipping_email'] ?? '',
                    'shipping_address1' => $requestAll['shipping_address1'] ?? '',
                    'combinedDeliveryAddress' => $requestAll['combinedDeliveryAddress'] ?? '',
                    'shipping_address2' => $requestAll['shipping_address2'] ?? '',
                    'shipping_city' => $requestAll['shipping_city'] ?? '',
                    'shipping_state' => $requestAll['shipping_state'] ?? '',
                    'shipping_country' => $requestAll['shipping_country'] ?? '',
                    'shipping_zipcode' => $requestAll['shipping_zipcode'] ?? '',
                    'type' => $requestAll['type'] ?? '',
                    'note' => $requestAll['note'] ?? '',
                    'payment_gateway_name' => $requestAll['payment_gateway_name'] ?? '',
                    'additional_charges' => $requestAll['additional_charges'] ?? '',
                    'currency' => $requestAll['currency'] ?? '',
                    'token' => $requestAll['token'] ?? '',
                    'custom_values' => $requestAll['custom_values'] ?? '',
                    'signature' => $requestAll['signature'] ?? '',
                    'gateway_id' => $requestAll['gateway_id'] ?? '',
                    'order_source' => $requestAll['order_source'] ?? '',
                    'shipping_method' => $requestAll['shipping_method'] ?? '',
                    'location_id' => $requestAll['location_id'] ?? '',
                ],
            ];

            if( isset( $requestAll['delivery'] ) ){
                $requestData['body']['delivery'] = json_decode($requestAll['delivery'], true);
            }


            $requestData['body'] = json_encode( $requestData['body'] );

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if (isset($responseJson['result']['data']['order']['success']) && $responseJson['result']['data']['order']['success'] == true ) {

                session()->forget('cart');

                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return redirect( route('rentmy.thank_you') );
            }

        }

        return redirect( route('rentmy.checkout') );

    }


    public function getPriceValue()
    {
        $requestAll = request()->all();

// echo '<div>';
// echo "\$requestAll:\n";
// echo '<pre><code>';
// print_r( $requestAll );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_GET_PRICE_VALUE_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'custom_fields' => $requestAll['custom_fields'] ?? [],
                    'deposit_amount' => $requestAll['deposit_amount'],
                    'driving_license_required' => $requestAll['driving_license_required'],
                    'fullfilment_option' => $requestAll['fullfilment_option'],
                    'is_apply' => $requestAll['is_apply'] ?? false,
                    'location' => $requestAll['location'],
                    'price' => $requestAll['price'],
                    'price_id' => $requestAll['price_id'],
                    'product_id' => $requestAll['product_id'],
                    'quantity' => $requestAll['quantity'],
                    'rent_end' => @$requestAll['rent_end'],
                    'rent_start' => @$requestAll['rent_start'],
                    'rental_duration' => $requestAll['rental_duration'],
                    'rental_type' => $requestAll['rental_type'],
                    'sales_tax' => $requestAll['sales_tax'],
                    'term' => $requestAll['term'],
                    'token' => $requestAll['token'],
                    'variants_products_id' => $requestAll['variants_products_id'],
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);
            
            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

// echo '<div>';
// echo "\$responseJson:\n";
// echo '<pre><code>';
// print_r( $responseJson );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";


            if (isset($responseJson['result']['data'])) {
                return response()->json($responseJson['result']);
            }
        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to get price value.',
        ]);
    }


    public function getPackagePrice()
    {
        $requestAll = request()->all();

        // Retrieve the authentication token.
        // $token = $this->getToken();

        $settings = Setting::first();
        $token = $settings->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {
            $products = [];
            if( !empty($requestAll['products']) ){
                foreach( $requestAll['products'] as $key => $product ){
                    $product = [
                        'variants_products_id' => $product['variants'][0]['id'],
                        'quantity' => $product['quantity'],
                        'product_id' => $product['id'],
                        'available' => 1,
                        'product_index' => $key,
                    ];
                    $products[] = $product;
                }
            }

            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_GET_PACKAGE_PRICE_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'custom_fields' => $requestAll['custom_fields'] ?? [],
                    'deposit_amount' => $requestAll['deposit_amount'],
                    'driving_license_required' => $requestAll['driving_license_required'],
                    'fullfilment_option' => $requestAll['fullfilment_option'],
                    'is_apply' => $requestAll['is_apply'] ?? false,
                    'location' => $requestAll['location'],
                    'package_id' => $requestAll['package_id'],
                    'price' => $requestAll['price'],
                    'price_id' => $requestAll['price_id'],
                    'quantity' => $requestAll['quantity'],
                    'rent_end' => @$requestAll['rent_end'],
                    'rent_start' => @$requestAll['rent_start'],
                    'rental_duration' => $requestAll['rental_duration'],
                    'rental_type' => $requestAll['rental_type'],
                    'term' => $requestAll['term'],
                    'token' => $requestAll['token'],
                    'variants_products_id' => $requestAll['variants_products_id'],
                    'products' => $products,                    
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);
            
            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if (isset($responseJson['result']['data'])) {
                return response()->json($responseJson['result']);
            }
        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to get price value.',
        ]);
    }



    public function getContents()
    {
        // Retrieve the authentication token.
        $token = $this->getToken();

            // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_CONTENTS_URL') // URL from environment configuration.
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $contents = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $contentsJson = json_decode($contents, true);

            // Return the JSON response from the API.
            return $contentsJson;
        }

        // Return false if retrieval fails.
        return false;
    }


    public function deleteCartItem()
    {
        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_DELETE_CART_ITEM_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'cart_item_id' => $requestAll['cart_item_id'],
                    'product_id' => $requestAll['product_id'],
                    'token' => $requestAll['token'],
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return response()->json($responseJson['result']);
            }

        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to delete cart item.',
        ]);
    }

    public function updateCartItem()
    {
        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_UPDATE_CART_ITEM_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'id' => $requestAll['id'],
                    'increment' => $requestAll['increment'] ?? '',
                    'price' => $requestAll['price'] ?? '',
                    'sales_tax' => $requestAll['sales_tax'] ?? '',
                    'token' => $requestAll['token'] ?? '',
                    'view_token' => $requestAll['view_token'] ?? '',
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return response()->json($responseJson['result']);
            }

        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to update cart item.',
        ]);
    }


    public function shippingRate()
    {
        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_SHIPPING_RATE_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'address' => $requestAll['address'],
                    'pickup' => $requestAll['pickup'],
                    'token' => $requestAll['token'],
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

// echo '<div>';
// echo "\$response:\n";
// echo '<pre><code>';
// print_r( $response );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

// echo '<div>';
// echo "\$responseJson:\n";
// echo '<pre><code>';
// print_r( $responseJson );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";

            if( isset($responseJson['result']) && !empty($responseJson['result'])){
                return response()->json($responseJson);
            }

        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to get shipping rate.',
        ]);
    }   

    public function cartDelivery()
    {
        $requestAll = request()->all();

        // Retrieve the authentication token.
        $token = $this->getToken();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_CART_DELIVERY_URL'), // URL from environment configuration.
                'body' => json_encode([
                    'shipping_method' => $requestAll['shipping_method'],
                    'token' => $requestAll['token'],
                    'shipping_cost' => $requestAll['shipping_cost'],
                    'address' => $requestAll['address'],
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

// echo '<div>';
// echo "\$response:\n";
// echo '<pre><code>';
// print_r( $response );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

// echo '<div>';
// echo "\$responseJson:\n";
// echo '<pre><code>';
// print_r( $responseJson );
// echo '</code></pre>';
// echo '</div>';
// echo "\n";

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return response()->json($responseJson['result']);
            }

        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to update cart delivery.',
        ]);
    }


    public function rsk_index()
    {
        return view('rsk.index');
    }

    public function rsk_product_list()
    {
        return view('rsk.product_list');
    }

    public function rsk_product_details()
    {
        return view('rsk.product_details');
    }

    public function rsk_package_details()
    {
        return view('rsk.package_details');
    }

    public function rsk_cart()
    {
        return view('rsk.cart');
    }  

    public function rsk_checkout()
    {
        return view('rsk.checkout');
    }    

    public function rsk_settings()
    {
        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();

        // Check if a setting exists.
        if (empty($setting)) {
            // If no setting exists, create a new setting record.
            Setting::create([]);
            // Retrieve the newly created setting.
            $setting = Setting::get()->first();
        }

        // Return the view for editing the setting, passing the setting data.
        return view('rsk.settings', compact('setting'));
    }

    public function rsk_update_settings(Request $request)
    {
        // Validate the request data.
        $request->validate([
            'store_name' => 'nullable|string|max:255',
            'store_id' => 'nullable|integer',
            'location_id' => 'nullable|integer',
            'access_token' => 'nullable|string'
        ]);

        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();

        // Update the setting with the validated request data.
        $setting->update($request->all());

        // Return the view for editing the setting with a success message.
        return view('rsk.settings', [
            'setting' => $setting,
            'success' => 'Settings updated successfully.'
        ]);
    }

    public function rsk_config_js()
    {
        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();

        // Read the config.js template file
        $template = file_get_contents(public_path('rsk_assets/js/config_js'));

        // Replace placeholders in the template with actual values
        $template = str_replace('module.exports = RENTMY_GLOBAL', '', $template);
        $template = str_replace('ENV_DOMAIN', request()->getScheme() . '://' . request()->getHost() . '/', $template);
        $template = str_replace('ENV_API_BASE_URL', $setting->api_base_url, $template);
        $template = str_replace('ENV_STORE_ID', $setting->store_id, $template);
        $template = str_replace('ENV_LOCATION_ID', $setting->location_id, $template);
        $template = str_replace('ENV_STORE_NAME', $setting->store_name, $template);
        $template = str_replace('ENV_ACCESS_TOKEN', $setting->access_token, $template);
        $template = str_replace('ENV_ASSET_URL', $setting->asset_url, $template);

        // Set response header as application/json
        header('Content-Type: application/javascript');
        echo $template;
        exit;

    }

    public function rsk_order_complete()
    {
        return view('rsk.order_complete');
    }


    public function homepage(Request $request)
    {

        // Retrieve navigation data, either from the database or the Rentmy API.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data from the database.
        $settings = Setting::first();

        $contents = $this->getSiteContents(); 

        // Return the view with the retrieved data.
        return view('rentmy.homepage', [
            'settings' => $settings,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'contents' => $contents['result']['data'] ?? [],
        ]);
    }

    
    public function updateCart()
    {
        $requestAll = request()->all();
        session(['rentmy_cart' => $requestAll['data']]);
        return response()->json([
            'success' => true,
            'message' => 'Cart updated.',
        ]);

    }


    public function package($slug = null, Request $request)
    {

        // Retrieve settings data.
        $settings = Setting::first();

        // Attempt to retrieve the product from the local database using the slug.
        $product = Product::where('slug', $slug)->first();

        // Check if the product was found in the database.
        if (false && !empty($product)) {
            // Output a message indicating data is from the database. (For debugging purposes)
            // echo '<div>';
            // echo "PRODUCT_FROM_DB";
            // echo '</div>';
            // echo "\n";

            // Prepare the data array with the product details.
            $data = [
                'status' => 'OK',
                'result' => [
                    'data' => $product->toArray(), // Convert the product model to an array.
                ]
            ];

            // Assign the prepared data to the response JSON.
            $responseJson = $data;
        } else {
            // Prepare the API request data.
            $requestData = [
                'url' => env('RENTMY_PACKAGE_URL') // URL from environment configuration.
            ];

            // Replace the placeholder '{slug}' in the URL with the actual slug.
            $requestData['url'] = str_replace('{slug}', $slug, $requestData['url']);

            // Replace the placeholder '{location_id}' in the URL with the actual location ID.
            $requestData['url'] = str_replace('{location_id}', Setting::first()->location_id, $requestData['url']);

            // Replace the placeholder '{token}' in the URL with the actual token.
            $token = session('rentmy_cart.token') ?? '';
            $requestData['url'] = str_replace('{token}', $token, $requestData['url']);

            // Replace the placeholder '{start_date}' in the URL with the actual start date.
            $requestData['url'] = str_replace('{start_date}', date('Y-m-d'), $requestData['url']);

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $settings->access_token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            // Add the slug to the response data if it exists.
            if (!empty($responseJson['result']['data'])) {
                $responseJson['result']['data']['slug'] = $slug;
            }

            // Write the retrieved product data to the database.
            // $this->writeToProduct($responseJson);
        }

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve related products data using the product ID.
        if( isset($responseJson['result']['data']['id']) ) {
            $relatedJson = $this->getRelated($responseJson['result']['data']['id']);

// https://clientapi.rentmy.co/api/products/custom-fields/values/6280

            $customFieldsValues = $this->getCustomFieldsValues($responseJson['result']['data']['id']);
        } else {
            $relatedJson = [];
            $customFieldsValues = [];
        }


        if( isset($responseJson['result']['data']['seo']['meta_title']) ) {
            $responseJson['result']['data']['seo']['meta_title'] = str_replace('{{site_name}}', $settings->store_slug, $responseJson['result']['data']['seo']['meta_title']);
        }

        $contents = $this->getContents();

        // Return the view with the retrieved data.
        return view('rentmy.package', [
            'product' => $responseJson['result']['data'] ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'related' => $relatedJson['result']['data'] ?? [],
            'customFieldsValues' => $customFieldsValues,
            'settings' => $settings,
            'contents' => $contents,
            'cart' => session('rentmy_cart') ?? [],
        ]);


    }


    public function getCustomFieldsValues($product_id)
    {
        $settings = Setting::first();
        $token = $settings->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $url = $settings->api_base_url . 'products/custom-fields/values/' . $product_id;

            $requestData = [
                'url' => $url,
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                return $responseJson['result']['data'];
            }

        }

        return [];
    }


    public function addToCartPackage()
    {

        $requestAll = request()->all();

        $settings = Setting::first();
        $token = $settings->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {

            $products = [];
            if( !empty($requestAll['products']) ){
                $requestAll['products'] = json_decode( $requestAll['products'], true );
                foreach( $requestAll['products'] as $key => $product ){
                    $product = [
                        'variants_products_id' => $product['variants'][0]['id'],
                        'quantity' => $product['quantity'],
                        'product_id' => $product['id'],
                        'available' => 1,
                        'product_index' => $key,
                    ];
                    $products[] = $product;
                }
            }
            
            // Prepare the API request data.
            $url = $settings->api_base_url . 'package/add-to-cart';

            $requestData = [
                'url' => $url,
                'body' => [
                    'price' => $requestAll['price'],
                    'package_id' => $requestAll['package_id'],
                    'quantity' => $requestAll['quantity'],
                    'variants_products_id' => $requestAll['variants_products_id'],
                    'location' => $requestAll['location'],
                    'rent_start' => @$requestAll['rent_start'],
                    // 'rent_start' => '2025-06-26 12:00',
                    'rent_end' => @$requestAll['rent_end'],
                    // 'rent_end' => '2025-07-17 12:00',
                    'rental_duration' => $requestAll['rental_duration'],
                    'rental_type' => $requestAll['rental_type'],
                    'fullfilment_option' => $requestAll['fullfilment_option'],
                    'term' => $requestAll['term'],
                    // 'term' => 3,
                    'token' => $requestAll['token'],
                    'price_id' => $requestAll['price_id'],
                    'custom_fields' => json_decode($requestAll['custom_fields']),
                    'is_apply' => $requestAll['is_apply'],
                    'products' => $products,
                ],
            ];

            $requestData['body'] = json_encode( $requestData['body'] );

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if (isset($responseJson['result']['error'])) {
                session()->flash('error', 'ERROR: ' .  $responseJson['result']['error']);
                return redirect( route('rentmy.package', ['slug' => $requestAll['slug']]) );
            }

            if( isset($responseJson['result']['data']) && !empty($responseJson['result']['data'])){
                $cartData = $responseJson['result']['data'];
                // Store the cart data in the session.
                session(['rentmy_cart' => $cartData]);
                return redirect(route('rentmy.cart', $cartData['token']));
            }

            session()->flash('error', 'ERROR: Failed to add item to cart. Please try again.');
            return redirect( route('rentmy.package', ['slug' => $requestAll['slug']]) );
        }

        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        // Return the view with the retrieved data.
        return view('rentmy.cart', [
            'cart' => $responseJson['result']['data'] ?? [],
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);

    }


    public function getDatesPriceDuration()
    {

        $settings = Setting::first();
        $token = $settings->access_token;

        $requestAll = request()->query();

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $url = $settings->api_base_url . 'product/get_dates_price_duration?start_date=' . urlencode($requestAll['start_date']) . '&start_time=' . $requestAll['start_time'] . '&price_id=' . $requestAll['price_id'] . '&location=' . $requestAll['location'];

            $requestData = [
                'url' => $url
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if (isset($responseJson['result']['data'])) {
                return response()->json($responseJson['result']);
            }
        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to get package price.',
        ]);
    }


    public function cartsApplyCoupon()
    {
        $requestAll = request()->all();

        $settings = Setting::first();
        $token = $settings->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $url = $settings->api_base_url . 'carts/apply-coupon';

            $requestData = [
                'url' => $url,
                'body' => json_encode([
                    'coupon' => $requestAll['coupon'],
                    'token' => $requestAll['token'],
                ]),
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlPost($requestData['url'], $requestData['body'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if(isset($responseJson['result']['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $responseJson['result']['error'] ?? 'Failed to apply coupon.',
                ]);
            } elseif(isset($responseJson['result']['data'])) {
                // Store the cart data in the session.
                session(['rentmy_cart' => $responseJson['result']['data']]);
                return response()->json($responseJson['result']);
            }
        }

        return response()->json([
            'success' => false,
            'message' => $responseJson['result']['error'] ?? 'Failed to apply coupon.',
        ]);
    }


    public function page($slug = null)
    {
        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        $page = Page::where('slug', $slug)->first();

        if(!$page) {
            $this->getPageData($slug);
            $page = Page::where('slug', $slug)->first();
        }

        if(!$page) {
            abort(404);
        }

        $page->contents = json_decode($page->contents, true);

        // Return the view with the retrieved data.
        return view('rentmy.page', [
            'page' => $page,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);
    }


    public function getPageData($slug)
    {

        $settings = Setting::first();
        $token = $settings->access_token;

        // Check if a token was successfully retrieved.
        if ($token) {
            // Prepare the API request data.
            $url = $settings->api_base_url . 'pages/' . $slug;

            $requestData = [
                'url' => $url
            ];

            // Prepare the API request headers.
            $headers = [
                'accept: application/json',
                'authorization: Bearer ' . $token, // Include the authentication token.
                'Content-Type: application/json'
            ];

            // Make the API call using the curlGet method and retrieve the response.
            $response = $this->curlGet($requestData['url'], $headers);

            // Decode the JSON response from the API.
            $responseJson = json_decode($response, true);

            if (isset($responseJson['result']['data'])) {
                $pageData = $responseJson['result']['data'];
                $pageData['contents'] = json_encode($pageData['contents']);
                $pageData['children'] = json_encode($pageData['children']);
                Page::create($pageData);
            }
        }
    }

    public function about()
    {
        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        $page = Page::where('slug', 'about')->first();

        if(!$page) {
            $this->getPageData('about');
            $page = Page::where('slug', 'about')->first();
        }

        if(!$page) {
            abort(404);
        }

        $page->contents = json_decode($page->contents, true);

        // Return the view with the retrieved data.
        return view('rentmy.page', [
            'page' => $page,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);
    }


    public function contact()
    {
        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        $page = Page::where('slug', 'contact')->first();

        if(!$page) {
            $this->getPageData('contact');
            $page = Page::where('slug', 'contact')->first();
        }

        if(!$page) {
            abort(404);
        }

        $page->contents = json_decode($page->contents, true);

        // Return the view with the retrieved data.
        return view('rentmy.page', [
            'page' => $page,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);
    }

    public function termsAndConditions()
    {
        // Retrieve navigation data.
        $navigationJson = $this->getNavigation();

        // Retrieve settings data.
        $settings = Setting::first();

        $page = Page::where('slug', 'terms-and-conditions')->first();

        if(!$page) {
            $this->getPageData('terms-and-conditions');
            $page = Page::where('slug', 'terms-and-conditions')->first();
        }

        if(!$page) {
            abort(404);
        }

        $page->contents = json_decode($page->contents, true);

        // Return the view with the retrieved data.
        return view('rentmy.page', [
            'page' => $page,
            'navigation' => $navigationJson['result']['data'] ?? [],
            'settings' => $settings,
        ]);
    }


}