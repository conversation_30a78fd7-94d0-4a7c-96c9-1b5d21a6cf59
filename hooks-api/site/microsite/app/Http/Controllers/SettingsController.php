<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Models\Catalog;
use App\Models\Product;
use App\Models\Category;
use App\Models\Navigation;
use App\Models\Related;
use App\Models\RequestsCache;
use App\Models\StoreContent;
use App\Models\Content;
use App\Models\Page;
use App\Models\CustomField;


class SettingsController extends Controller
{
/**
     * Show the form for editing the specified setting.
     *
     * This method retrieves the first setting from the database. If no setting
     * exists, it creates a new setting record. It then returns the view for
     * editing the setting, passing the setting data to the view.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();

        // Check if a setting exists.
        if (empty($setting)) {
            // If no setting exists, create a new setting record.
            Setting::create([]);
            // Retrieve the newly created setting.
            $setting = Setting::get()->first();
        }

        // Return the view for editing the setting, passing the setting data.
        return view('rentmy.settings', compact('setting'));
    }

    /**
     * Update the specified setting in storage.
     *
     * This method validates the request data, updates the setting in the database,
     * truncates several tables (Catalog, Product, Category, Navigation, Related,
     * and RequestsCache), and returns the view for editing the setting with a
     * success message.
     *
     * @param \Illuminate\Http\Request $request The incoming HTTP request.
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        // Validate the request data.
        $request->validate([
            'store_name' => 'nullable|string|max:255',
            'location' => 'nullable|integer',
        ]);

        // Retrieve the first setting from the database.
        $setting = Setting::get()->first();
        $requestAll = $request->all();
        $requestAll['location'] = $requestAll['location_id'];

        // Update the setting with the validated request data.
        $setting->update($requestAll);

        // Truncate several tables to clear cached or old data.
        Catalog::truncate();
        Product::truncate();
        Category::truncate();
        Navigation::truncate();
        Related::truncate();
        RequestsCache::truncate();
        StoreContent::truncate();
        Content::truncate();
        Page::truncate();
        CustomField::truncate();
        session()->forget('rentmy_cart');

        // Return the view for editing the setting with a success message.
        return view('rentmy.settings', [
            'setting' => $setting,
            'success' => 'Setting updated successfully.'
        ]);
    }
}
