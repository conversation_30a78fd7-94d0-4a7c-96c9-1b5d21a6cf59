<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomField extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'custom_fields';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the primary key is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'int';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'query',
        'store_id',
        'location',
        'field_name',
        'field_label',
        'field_type',
        'sequence_no',
        'field_is_required',
        'collect_signature',
        'field_values',
        'section',
        'input_hints',
        'created',
        'modified',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'store_id' => 'integer',
        'location' => 'integer',
        'sequence_no' => 'integer',
        'field_is_required' => 'integer',
        'collect_signature' => 'integer',
        'field_values' => 'array',
        'created' => 'datetime',
        'modified' => 'datetime',
    ];
}
