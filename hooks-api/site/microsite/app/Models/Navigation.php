<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Navigation extends Model
{
    use HasFactory;

    protected $table = 'navigation';

    protected $fillable = [
        'sequence_no',
        'content_id',
        'content_type',
        'label',
        'content_url',
        'status',
        'type',
        'parent_id',
        'children',
    ];

    protected $casts = [
        'sequence_no' => 'integer',
        'content_id' => 'integer',
        'parent_id' => 'integer',
        'children' => 'array', // Assuming children is stored as JSON
    ];

    public $timestamps = false;

}
