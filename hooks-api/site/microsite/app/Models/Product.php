<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'products';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the primary key is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'sales_tax',
        'deposit_amount',
        'name',
        'description',
        'buy_price',
        'rent_price',
        'variant_set',
        'driving_license',
        'keyword',
        'store_id',
        'is_tracked',
        'options',
        'client_specific_id',
        'status',
        'images',
        'extact_durations',
        'exact_time',
        'booking',
        'enduring_rental',
        'variant_list',
        'variant_set_list',
        'prices',
        'cart_available',
        'available',
        'default_variant',
        'seo',
        'band_pricing',
        'slug'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'sales_tax' => 'integer',
        'deposit_amount' => 'integer',
        'buy_price' => 'integer',
        'rent_price' => 'integer',
        'driving_license' => 'boolean',
        'store_id' => 'integer',
        'is_tracked' => 'integer',
        'client_specific_id' => 'integer',
        'status' => 'integer',
        'exact_time' => 'boolean',
        'booking' => 'boolean',
        'enduring_rental' => 'boolean',
        'cart_available' => 'integer',
        'available' => 'integer',
        'options' => 'array',
        'images' => 'array',
        'extact_durations' => 'array',
        'variant_list' => 'array',
        'variant_set_list' => 'array',
        'prices' => 'array',
        'default_variant' => 'array',
        'seo' => 'array',
        'band_pricing' => 'array'
    ];
}
