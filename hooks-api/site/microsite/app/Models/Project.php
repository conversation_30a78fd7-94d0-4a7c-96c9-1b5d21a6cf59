<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'api_key',
        'timezone',
        'ref_names',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'ref_names' => 'array',
    ];

    /**
     * Get the records for the project.
     */
    public function records()
    {
        return $this->hasMany(Record::class);
    }
}
