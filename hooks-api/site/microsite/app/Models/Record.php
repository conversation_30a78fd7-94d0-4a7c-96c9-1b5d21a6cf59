<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Record extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'project_id',
        'account',
        'event',
        'status',
        'description',
        'value',
        'custom_content',
        'ref1',
        'ref2',
        'ref3',
        'ref4',
        'ref5',
    ];

    /**
     * Get the project that owns the record.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
