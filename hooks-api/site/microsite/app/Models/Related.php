<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Related extends Model
{
    use HasFactory;

    public $timestamps = false; // Disable timestamps

    protected $table = 'related';

    protected $fillable = [
        'id',
        'available',
        'sales_tax',
        'deposit_amount',
        'store_id',
        'url',
        'uuid',
        'name',
        'type',
        'status',
        'products_availabilities',
        'images',
        'variants_product',
        'prices',
        'default_variant',
        'product_id'
    ];

    protected $casts = [
        'available' => 'integer',
        'sales_tax' => 'integer',
        'deposit_amount' => 'integer',
        'store_id' => 'integer',
        'type' => 'integer',
        'status' => 'integer',
        'products_availabilities' => 'array',
        'images' => 'array',
        'variants_product' => 'array',
        'prices' => 'array',
        'default_variant' => 'array',
    ];
}
