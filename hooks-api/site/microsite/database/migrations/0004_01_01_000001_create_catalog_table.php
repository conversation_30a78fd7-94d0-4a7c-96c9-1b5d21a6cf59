<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCatalogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('catalog', function (Blueprint $table) {
            $table->unsignedBigInteger('id'); // bigint unsigned
            $table->integer('type'); // int
            $table->string('name'); // varchar(255)
            $table->integer('sales_tax'); // int
            $table->string('url'); // varchar(255)
            $table->string('uuid'); // varchar(255)
            $table->integer('has_variant'); // int
            $table->longText('default_variant'); // longtext
            $table->longText('prices'); // longtext
            $table->longText('images'); // longtext
            $table->primary('id'); // Add primary key constraint.
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('catalog');
    }
}
