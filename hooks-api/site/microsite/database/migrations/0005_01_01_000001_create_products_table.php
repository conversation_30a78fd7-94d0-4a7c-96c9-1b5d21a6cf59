<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary(); // bigint(20) unsigned, primary key
            $table->integer('sales_tax')->nullable(); // int(11)
            $table->integer('deposit_amount')->nullable(); // int(11)
            $table->string('name')->nullable(); // varchar(255)
            $table->text('description')->nullable(); // text
            $table->integer('buy_price')->nullable(); // int(11)
            $table->integer('rent_price')->nullable(); // int(11)
            $table->string('variant_set')->nullable(); // varchar(255)
            $table->tinyInteger('driving_license'); // tinyint(1)
            $table->string('keyword')->nullable(); // varchar(255)
            $table->integer('store_id')->nullable(); // int(11)
            $table->integer('is_tracked')->nullable(); // int(11)
            $table->text('options')->nullable(); // text
            $table->integer('client_specific_id')->nullable(); // int(11)
            $table->integer('status')->nullable(); // int(11)
            $table->longText('images')->nullable(); // longtext
            $table->longText('extact_durations')->nullable(); // longtext
            $table->tinyInteger('exact_time')->nullable(); // tinyint(1)
            $table->tinyInteger('booking')->nullable(); // tinyint(1)
            $table->tinyInteger('enduring_rental')->nullable(); // tinyint(1)
            $table->longText('variant_list')->nullable(); // longtext
            $table->longText('variant_set_list')->nullable(); // longtext
            $table->longText('prices')->nullable(); // longtext
            $table->integer('cart_available')->nullable(); // int(11)
            $table->integer('available')->nullable(); // int(11)
            $table->longText('default_variant')->nullable(); // longtext
            $table->longText('seo')->nullable(); // longtext
            $table->longText('band_pricing')->nullable(); // longtext
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
