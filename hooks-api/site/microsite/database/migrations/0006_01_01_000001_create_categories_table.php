<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCategoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->increments('id'); // int unsigned AUTO_INCREMENT PRIMARY KEY
            $table->string('uuid', 100)->nullable(); // varchar(100) NULL
            $table->string('name')->nullable(); // varchar(255) NULL
            $table->string('url')->nullable(); // varchar(255) NULL
            $table->integer('sequence_no')->nullable(); // int NULL
            $table->integer('total_product')->nullable(); // int NULL
            $table->longText('children')->nullable(); // longtext NULL
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('categories');
    }
}
