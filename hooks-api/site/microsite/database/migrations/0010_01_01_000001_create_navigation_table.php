<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNavigationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('navigation', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('sequence_no')->nullable();
            $table->integer('content_id')->nullable();
            $table->string('content_type', 100)->nullable();
            $table->string('label')->nullable();
            $table->string('content_url')->nullable();
            $table->string('status', 100)->nullable();
            $table->string('type', 100)->nullable();
            $table->integer('parent_id')->nullable();
            $table->longText('children')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('navigation');
    }
}
