<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRelatedTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('related', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('available')->nullable();
            $table->integer('sales_tax')->nullable();
            $table->integer('deposit_amount')->nullable();
            $table->integer('store_id')->nullable();
            $table->string('url')->nullable();
            $table->string('uuid', 100)->nullable();
            $table->string('name')->nullable();
            $table->integer('type')->nullable();
            $table->integer('status')->nullable();
            $table->longText('products_availabilities')->nullable();
            $table->longText('images')->nullable();
            $table->longText('variants_product')->nullable();
            $table->longText('prices')->nullable();
            $table->longText('default_variant')->nullable();
            $table->unsignedInteger('product_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('related');
    }
}
