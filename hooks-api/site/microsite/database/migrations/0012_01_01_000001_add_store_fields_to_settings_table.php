<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStoreFieldsToSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('settings', function (Blueprint $table) {
                $table->integer('store_id')->nullable();
                $table->integer('location_id')->nullable();
                $table->text('access_token')->nullable();
                $table->string('api_base_url')->nullable();
                $table->string('asset_url')->nullable();
            });
        } catch (\Throwable $th) {
            //throw $th;
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        try {
            Schema::table('settings', function (Blueprint $table) {
                $table->dropColumn([
                    'store_id',
                    'location_id',
                    'access_token',
                    'api_base_url',
                    'asset_url'
                ]);
            });
        } catch (\Throwable $th) {
            //throw $th;
        }

    }
}