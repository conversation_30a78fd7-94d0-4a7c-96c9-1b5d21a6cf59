<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement()->primary();
            $table->unsignedInteger('store_id')->nullable();
            $table->unsignedInteger('location')->nullable();
            $table->string('name', 255)->nullable();
            $table->string('slug', 255)->nullable();
            $table->longText('contents')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keyword', 255)->nullable();
            $table->string('meta_title', 255)->nullable();
            $table->tinyInteger('status')->nullable();
            $table->string('type', 100)->nullable();
            $table->string('tags', 255)->nullable();
            $table->unsignedInteger('parent_id')->nullable();
            $table->text('featured_image')->nullable();
            $table->text('thumbnail_image')->nullable();
            $table->text('canonical_url')->nullable();
            $table->longText('children')->nullable();
            $table->dateTime('created')->nullable();
            $table->dateTime('modified')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages');
    }
}
