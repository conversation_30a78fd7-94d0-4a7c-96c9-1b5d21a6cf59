<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->unsignedInteger('id', 10)->autoIncrement()->primary();
            $table->unsignedInteger('api_id', 10)->nullable();
            $table->string('tag', 255)->nullable();
            $table->string('type', 255)->nullable();
            $table->tinyInteger('status')->nullable();
            $table->longText('content')->nullable();
            $table->dateTime('created')->nullable();
            $table->dateTime('modified')->nullable();
            
            $table->engine = 'InnoDB';
            $table->charset = 'latin1';
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contents');
    }
}
