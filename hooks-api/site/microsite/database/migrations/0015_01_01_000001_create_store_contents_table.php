<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoreContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('store_contents', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement()->primary();
            $table->tinyInteger('status')->nullable();
            $table->string('tag', 100)->nullable();
            $table->string('type', 100)->nullable();
            $table->longText('contents')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('store_contents');
    }
}
