<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_fields', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement()->primary();
            $table->string('query', 50)->nullable();
            $table->unsignedInteger('store_id')->nullable();
            $table->unsignedInteger('location')->nullable();
            $table->string('field_name', 255)->nullable();
            $table->string('field_label', 255)->nullable();
            $table->string('field_type', 10)->nullable();
            $table->integer('sequence_no')->nullable();
            $table->tinyInteger('field_is_required')->nullable();
            $table->tinyInteger('collect_signature')->nullable();
            $table->longText('field_values')->nullable();
            $table->text('section')->nullable();
            $table->text('input_hints')->nullable();
            $table->dateTime('created')->nullable();
            $table->dateTime('modified')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_fields');
    }
}
