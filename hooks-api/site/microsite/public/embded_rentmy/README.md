# Embedded RentMy

Embedded rentmy cart will pop in to any cors server


## Project Setup

```sh
npm install
```

### Compile for Development (don't need for production)

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build 
```


### Embedded Code to Place Client's DOM

1. Inside Client's HTML Head (for now we connected using store slug, later we will connect by store uid)
```sh
    <script>
       var storeSlug = 'teststore20'
    </script>
    
    <script type="module" crossorigin src="[Host name]/dist/assets/[js fil].js"></script>
    <link rel="stylesheet" href="[Host name]/dist/assets/[css file].css">
```

2. Inside Client's HTML Body (at the bottom of the body)
```sh
    <div id="RENTMY_LOADER"></div>   
    <div id="RENTMY_CART_CONTAINER"></div>
```

3. Add to Cart Buttons Template (here button codes can be copied from rentmy store admin, product details page)
```sh   
     <div class="RENTMY_ITEM_CONTAINER" data-uid="<uid>" product-type="product/package" data-view="ture/false" ></div>
```
Components
```sh
<button class="RENTMY_BTN_ADDTO_CART" data-uid="<uid>">Add to cart</button>
<div class="RENTMY_RADIO_PURCHASE_TYPE" data-uid="<uid>"></div>
<div class="RENTMY_RADIO_PRICE_OPTIONS" data-uid="<uid>"></div>
<div class="RENTMY_PRICE_CONTAINER" data-uid="<uid>"></div>
<div class="RENTMY_AVAILABILITY_CONTAINER" data-uid="<uid>"></div>
```
