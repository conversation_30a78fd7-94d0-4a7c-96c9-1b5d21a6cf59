
export const rentMyStoreConfig = {

    //client side base url [String]
    clientBaseURL : typeof clientBaseURL !== 'undefined' ? clientBaseURL : 'https://teststore09.rentmy.shop',

    //Enable api request cache [Boolean]
    enableRequestCache: true,

    //Set request max timeout in second [Integer]
    requestTimeout: 50,

    //Authorization Mode type, authMode = APP | WEB [String]
    authMode: typeof authMode !== 'undefined' ? authMode : 'web',

    //if mode type is WEB storeName is required
    storeSlug: typeof storeSlug !== 'undefined' ? storeSlug : 'teststore09',

    //If mode is APP then set API keys [Object]
    credentials : {
        storeUid : typeof storeUid !== 'undefined' ? storeUid : '',
        storeKey: typeof storeKey !== 'undefined' ? storeKey : '',
        storeSecret: typeof storeSecret !== 'undefined' ? storeSecret : ''
    },

    //Store disable default time [Boolean]
    storeDisableDefaultTime: true,

    //Product thumb default image path [String]
    productImageThumb: '/assets/img/home/<USER>',
    apiBaseURL: 'https://clientapi.rentmy.co/api/'
}
