

<script>
import {RentMySDK} from "rentmyjsdk";
import '@mobiscroll/javascript/dist/css/mobiscroll.min.css'
import Cart from "@/components/Cart/Cart.vue";
import {createNamespacedHelpers} from "vuex";

const {mapGetters: mapGettersStore} = createNamespacedHelpers('store')
export default {
  components: {Cart},
  data() {
    return {
      //HTML ELMS
      variantContainerElm: null,
      purchaseTypeElm: null,
      cartBtnElm: null,
      pricesOptionsElm: null,
      priceContainerElm: null,
      availabilityContainerElm: null,


      rentMYLoader: null,

      defaultType: 'cart',
      defaultRentalType: 'buy',

    }
  },
  async mounted() {
    this.rentMYLoader = document.querySelector("#RENTMY_LOADER");

    await this.$store.dispatch('store/loadConfig',  {_v: this, container: this.rentMYLoader});
    await this.$store.dispatch('cart/loadCart', true,  {_v: this, container: this.rentMYLoader});

    await [...document.querySelectorAll(".RENTMY_ITEM_CONTAINER")].forEach(el => this.itemDetailsInputGenerate(el));
    await [...document.querySelectorAll(".RENTMY_BTN_ADDTO_CART")].forEach(el => this.addEventForAddToCart(el));
  },
  computed: {
    ...mapGettersStore(['currencyConfig']),
  },
  methods: {
    addEventForAddToCart(elem) {
      elem.addEventListener("click", (e) => {
        let product = JSON.parse(e.target.getAttribute("data-product"));
        let suggestion = null;
        const params = {_v: this, container: this.rentMYLoader}

        if (product.type === 1) { // for product
          const rentStart = RentMySDK.utils.Storage.get('rent_start');
          if (rentStart) {
            suggestion = RentMySDK.suggestions.cart.addToCart.body;
          } else {
            suggestion = RentMySDK.suggestions.cart.addOrUpdateToCartWithoutDate.body;
            params[suggestion.required.type] = this.defaultType;
            params[suggestion.required.productType] = product.type;
          }
          params[suggestion.required.quantity] = 1; // variable
          params[suggestion.required.productId] = product.product_id;
          params[suggestion.required.rentalType] = product.rental_type; // variable buy | rent
          params[suggestion.required.location] = RentMySDK.utils.Storage.get('locationId');
          params[suggestion.required.variantsProductsId] = product?.variants_products_id
          if (!product?.variants_products_id)
            params[suggestion.required.variantsProductsId] = product?.default_variant?.variants_products_id
        } else { // for package
          suggestion = RentMySDK.suggestions.cart.packageAddToCart.body;
          params[suggestion.required.quantity] = 1; // variable
          params[suggestion.required.packageId] = product.package_id;
          params[suggestion.required.rentalType] = product.rental_type;  // variable buy | rent
          params[suggestion.required.location] = RentMySDK.utils.Storage.get('locationId');
          params[suggestion.required.variantsProductsId] = product?.variants_products_id
          if (!product?.variants_products_id)
            params[suggestion.required.variantsProductsId] = product?.default_variant?.variants_products_id
          params.apiName = 'packageAddToCart'

          params[suggestion.optional.products] = product.products;
        }

        this.$store.dispatch("cart/addToCart", params);
        this.$emit('addedToCart', {success: true})
      });
    },

    priceOptionChangeActionEvent(event, htmlElements, item) {
      item.price_id = event.target.value
      this.cartBtnDataGenerate(htmlElements, item)
    },

    initElems(itemUID) {
      return {
        cartBtnElm: document.querySelector(`.RENTMY_BTN_ADDTO_CART[data-uid="${itemUID}"]`),
        pricesOptionsElm: document.querySelector(`.RENTMY_RADIO_PRICE_OPTIONS[data-uid="${itemUID}"]`),
        purchaseTypeElm: document.querySelector(`.RENTMY_RADIO_PURCHASE_TYPE[data-uid="${itemUID}"]`),
        priceContainerElm: document.querySelector(`.RENTMY_PRICE_CONTAINER[data-uid="${itemUID}"]`),
        availabilityContainerElm: document.querySelector(`.RENTMY_AVAILABILITY_CONTAINER[data-uid="${itemUID}"]`),
      }
    },

    async itemDetailsInputGenerate(elem) {

      const itemUID = elem.getAttribute("data-uid");
      const isPackage = elem.getAttribute("product-type") === 'package';
      let isVariantElmShow = true;
      if (elem.hasAttribute('data-view') && (elem.getAttribute("data-view") === 'none' || elem.getAttribute("data-view") === 'false' || elem.getAttribute("data-view") === 'hide'))
        isVariantElmShow = false

      const item = await this.$store.dispatch("products/view", {pUID: itemUID, isPackage: isPackage,  _v: this, container: this.rentMYLoader});
      console.log(item)

      const htmlElements = this.initElems(itemUID)
      htmlElements.variantContainerElm = elem;

      const prices = this.pricesFormat(item)
      console.log(prices)
      //add buy and rent prices list
      this.generatePurchaseType(htmlElements, item, prices)

      //variant fields generate + action events bind
      if (isVariantElmShow)
        this.generateVariantsHTML(htmlElements, itemUID, item)
      else
        htmlElements.variantContainerElm.innerHTML = ''

      //initial price bind
      this.priceBind(htmlElements, prices.priceRaw.rent)

      this.availableBind(htmlElements, item.available)

      //cart button data generate and bind
      this.cartBtnDataGenerate(htmlElements, item)
    },

    generatePurchaseType(htmlElements, item, pricesAll) {

      if (htmlElements.pricesOptionsElm)
        htmlElements.pricesOptionsElm.innerHTML = ''

      const formCheck = document.createElement("div");
      formCheck.className = 'RENTMY_FORM_CHECK';

      if (pricesAll.radioBtnHelper.buy) {
        const buyLabel = document.createElement("label");
        buyLabel.setAttribute('for', 'rentmy-cart-buy-' + item.id);

        const buyInput = document.createElement("input");
        buyInput.setAttribute('name', 'rental_type');
        buyInput.type = 'radio';
        buyInput.id = 'rentmy-cart-buy-' + item.id;
        buyInput.value = 'buy';

        if (!pricesAll.radioBtnHelper.rent)
          buyInput.setAttribute('checked', true);

        buyLabel.appendChild(buyInput);
        buyLabel.appendChild(document.createTextNode('Buy'));
        buyInput.addEventListener('change', e => {
          if (htmlElements.pricesOptionsElm)
            htmlElements.pricesOptionsElm.innerHTML = ''

          item.rental_type = 'buy'
          this.priceBind(htmlElements, pricesAll.priceRaw.buy)
          this.cartBtnDataGenerate(htmlElements, item)
        })
        formCheck.appendChild(buyLabel);
      }

      if (pricesAll.radioBtnHelper.rent) {
        const rentLabel = document.createElement("label");
        rentLabel.setAttribute('for', 'rentmy-cart-rent-' + item.id);

        const rentInput = document.createElement("input");
        rentInput.setAttribute('name', 'rental_type');
        rentInput.type = 'radio';
        rentInput.id = 'rentmy-cart-rent-' + item.id;
        rentInput.value = 'rent';
        rentInput.setAttribute('checked', true);
        rentLabel.appendChild(rentInput)
        rentLabel.appendChild(document.createTextNode('Rent'));
        this.addRentTypeEvent(htmlElements, item, pricesAll)
        rentInput.addEventListener('change', e => this.addRentTypeEvent(htmlElements, item, pricesAll))
        formCheck.appendChild(rentLabel);
      }

      if (htmlElements.purchaseTypeElm) {
        htmlElements.purchaseTypeElm.innerHTML = ''
        htmlElements.purchaseTypeElm.appendChild(formCheck);
      }
    },

    addRentTypeEvent(htmlElements, item, pricesAll ){
      const prices = pricesAll.pricingOptions
      this.priceBind(htmlElements, pricesAll?.priceRaw?.rent)
      item.rental_type = 'rent'
      if (prices.rent.length > 0) {
        if (htmlElements.pricesOptionsElm)
          htmlElements.pricesOptionsElm.innerHTML = ''

        for (const rentPrice of prices.rent) {
          let radioLabel = document.createElement("label");
          radioLabel.setAttribute('for', 'rentmy-price-rent-' + rentPrice.id);

          if (prices.rent.length !== 1) {
            let radioInput = document.createElement("input");
            radioInput.setAttribute('name', 'price_id');
            radioInput.className = 'RENTMY_PRICE_RENT_OPTIONS';
            radioInput.type = 'radio';
            radioInput.id = 'rentmy-price-rent-' + rentPrice.id;
            radioInput.value = rentPrice.id;
            radioLabel.appendChild(radioInput)
            radioInput.addEventListener('change', e => this.priceOptionChangeActionEvent(e, htmlElements, item))
          }
          radioLabel.innerHTML += (rentPrice.label);
          if (htmlElements.pricesOptionsElm)
            htmlElements.pricesOptionsElm.appendChild(radioLabel)
        }
      }
      this.cartBtnDataGenerate(htmlElements, item)
    },

    generateVariantsHTML(htmlElements, itemUID, item) {
      if (item.type && item.type === 2)
        return this.createPackageVariants(htmlElements, item)

      const variantContainer = document.createElement("div");
      variantContainer.className = 'RENTMY_ITEM_VARIANTS';
      if (item.variant_set_list.length > 0) {
        for (const [key, vSet] of item.variant_set_list.entries()) {
          let options = [];
          for (const variant of item.variant_list)
            if (variant.variant_set_id === vSet.id)
              options.push(variant)

          variantContainer.appendChild(this.createSelectInput(htmlElements, vSet.id, vSet.name, options, item, itemUID, item.variant_set_list.length - 1 === key));
        }
      }

      htmlElements.variantContainerElm.innerHTML = ''
      htmlElements.variantContainerElm.appendChild(variantContainer)
    },

    createPackageVariants(htmlElements, item) {
      const products = item.products
      htmlElements.variantContainerElm.innerHTML = ''
      if (products.length > 0) {
        for (const [key, product] of products.entries()) {
          //<img class="rentmy-product-image" :src="getImageUrl(item.product)" alt="product image">
          const productImg = document.createElement('img');
          productImg.src = RentMySDK.utils.helpers.productThumb(product)
          productImg.alt = product.name;
          productImg.className = 'RENTMY_ITEM_VARIANT_IMAGE';

          const labelElm = document.createElement("label");
          labelElm.appendChild(document.createTextNode(`${product.name} (${product.quantity})`));
          labelElm.setAttribute('for', 'rentmy-v-p-' + product.id);

          //if Unassigned: Unassigned only variant
          if (product.variants.length === 1 && product.variants[0].variant_chain.includes('Unassigned')){
            const formControl = document.createElement("div");
            formControl.className = 'RENTMY_ITEM_VARIANT_SET';
            formControl.appendChild(productImg);
            formControl.appendChild(labelElm);
            htmlElements.variantContainerElm.appendChild(formControl)
            continue
          }

          const selectElm = document.createElement("select");
          selectElm.setAttribute('id', 'rentmy-v-' + product.id);
          let options = []
          for (const option of product.variants)
            options.push({id: option.id, name: option.variant_chain})

          this.variantOptionGenerate(selectElm, options, true)
          selectElm.addEventListener('change', async e => {
            let variants = [];
            for (const pv of product.variants) {
              if (pv.selected)
                delete pv.selected;

              variants.push(pv)
            }

            item.products[key]['variants'] = []
            for (const pv of variants) {
              if (pv.id == e.target.value)
                pv.selected = true;

              item.products[key]['variants'].push(pv)
            }

            const qty = await this.$store.dispatch("products/packageVariantChangedQuantity", {pUID: item.uid, variants: this.getPackageVariants(item.products),  _v: this, container: this.rentMYLoader});
            this.availableBind(htmlElements, qty)
            this.cartBtnDataGenerate(htmlElements, item)
          })

          const formControl = document.createElement("div");
          formControl.className = 'RENTMY_ITEM_VARIANT_SET';
          formControl.appendChild(productImg);
          formControl.appendChild(labelElm);
          formControl.appendChild(selectElm);
          htmlElements.variantContainerElm.appendChild(formControl)
        }
      }
    },

    createSelectInput(htmlElements, id, label, options, item, itemUID, isLast = false) {
      const selectElm = document.createElement("select");
      selectElm.setAttribute('id', 'rentmy-v-' + id);
      this.variantOptionGenerate(selectElm, options)
      selectElm.addEventListener('change', async e => {
        if (!isLast) {
          const newVariants = await this.$store.dispatch("products/productVariantChain", {
            productId: item.id,
            variantId: e.target.value,
            _v: this, container: this.rentMYLoader
          });
          if (newVariants.length > 0) {
            let changeableElm = document.querySelector("#rentmy-v-" + newVariants[0].variant_set_id)
            this.variantOptionGenerate(changeableElm, newVariants)
          }
          return true;
        }

        //when last variant change
        let vValues = []
        for (const vSet of item.variant_set_list) {
          const elmID = "rentmy-v-" + vSet.id
          if (elmID !== e.target.id)
            vValues.push(document.querySelector('#' + elmID).value)
        }

        const result = await this.$store.dispatch("products/productPathOfVariantChain", {
          productId: item.id,
          variantId: e.target.value,
          variantChain: vValues.length > 0 ? vValues.join(',') : '',
          _v: this, container: this.rentMYLoader
        });
        item.variants_products_id = result.variants_products_id
        this.cartBtnDataGenerate(htmlElements, item)
        this.availableBind(htmlElements, result.available)
        const prices = this.pricesFormat(result)
        //add buy and rent prices list
        this.generatePurchaseType(htmlElements, item, prices)
        //initial price bind
        this.priceBind(htmlElements, prices.priceRaw.rent)
      })


      const labelElm = document.createElement("label");
      labelElm.appendChild(document.createTextNode(label));
      labelElm.setAttribute('for', 'rentmy-v-' + id);

      const formControl = document.createElement("div");
      formControl.className = 'RENTMY_ITEM_VARIANT_SET';
      formControl.appendChild(labelElm);
      formControl.appendChild(selectElm);

      return formControl;
    },

    variantOptionGenerate(elm, options, isPackage = false) {
      elm.innerHTML = '';
      if (!isPackage)
        options = [{name: '-Select One-', id: ''}].concat(options)

      for (const option of options) {
        const optionElm = document.createElement("option");
        optionElm.appendChild(document.createTextNode(option.name));
        if (option.selected)
          optionElm.setAttribute('selected', true);
        optionElm.value = option.id;
        elm.appendChild(optionElm);
      }
    },

    priceBind(htmlElements, amount) {
      if (htmlElements.priceContainerElm)
        htmlElements.priceContainerElm.innerHTML = this.currencyFormat(this.currencyConfig, amount)
    },

    availableBind(htmlElements, qty) {
      if (htmlElements.availabilityContainerElm)
        htmlElements.availabilityContainerElm.innerHTML = `<span class="rentmy-availbe-qty">${qty} </span>`
    },

    cartBtnDataGenerate(htmlElements, data) {
      if (htmlElements.cartBtnElm)
        htmlElements.cartBtnElm.setAttribute("data-product", JSON.stringify(this.getCartButtonData(data)))
    },

    getCartButtonData(item) {
      //product add to cart params generate
      if (item.type === 1)
        return {
          product_id: item.id,
          type: item.type,
          price_id: item?.price_id || null,
          rental_type: item.rental_type ?? this.defaultRentalType,
          variants_products_id: item.variants_products_id ?? item.default_variant.variants_products_id
        }

      //package add to cart params generate
      return {
        package_id: item.id,
        type: item.type,
        rental_type: item.rental_type ?? this.defaultRentalType,
        products: this.getPackageCartVariants(item.products),
        price_id: item?.price_id || null,
        variants_products_id: item.variants_products_id ?? item.default_variant.variants_products_id
      }
    },

    getPackageCartVariants(products){
      let cartData = [];
      for (const product of products){
        let variant = {product_id: product.id, quantity: product.quantity}
        if (product.variants.length > 0){
          for (const pv of product.variants){
            if(pv?.selected)
              variant.variants_products_id = pv.id
          }

          if (!variant.variants_products_id)
            variant.variants_products_id = product.variants[0].id

          cartData.push(variant)
        }
      }

      return cartData
    },

    getPackageVariants(products){
      let variants = [];
      for (const product of products){
        let variant = {product_id: product.id, quantity: product.quantity}
        if (product.variants.length > 0){
          for (const pv of product.variants){
            if(pv?.selected)
              variant.variants_products_id = pv.id
          }

          if (!variant.variants_products_id)
            variant.variants_products_id = product.variants[0].id

          variants.push(variant.variants_products_id)
        }
      }

      return variants
    },

    pricesFormat(item) {
      let prices = item.prices ? item.prices[0] || [] : item.price[0] || []
      const rentals = ["hourly", "daily", "weekly", "monthly", "rent","fixed"];
      const types = Object.keys(prices)
      const radioBtnHelper = {buy: false, rent: false}
      let pricingOptions = {
        rent: [],
        buy: ''
      };
      let priceRaw = {rent: 0, buy: 0};
      let actionTypesRadios = [];

      for (const type of types) {
        //format for buy option
        if (type === 'base' && prices[type].price) {
          pricingOptions.buy = this.currencyFormat(this.currencyConfig, prices[type].price)
          priceRaw.buy = prices[type].price
          radioBtnHelper.buy = true
        }

        //format rent buy option
        if (rentals.includes(type)) {
          for (const rent of prices[type]) {
            if (rent.price > 0) {
              //set first default price
              if (priceRaw.rent <= 0)
                priceRaw.rent = rent.price

              //once execute inside this condition enable rent button
              radioBtnHelper.rent = true

              //now prepare option label and set
              let typeLabel = rent.label
              if (rent.duration > 1)
                typeLabel = typeLabel + 's'
              const price = this.currencyFormat(this.currencyConfig, rent.price)
              const label = `<strong><i class="lni lni-arrow-right"></i> ${price}</strong> for ${rent.duration} ${typeLabel}`
              pricingOptions.rent.push({id: rent.id, label: label})
            }
          }
        }
      }

      return {pricingOptions: pricingOptions, priceRaw: priceRaw, radioBtnHelper: radioBtnHelper};
    },

    currencyFormat(currencyConfig, amount, isSymbol = true) {
      let symbol = currencyConfig.symbol
      if (!isSymbol)
        symbol = currencyConfig.code

      if (currencyConfig.pre)
        return `<span class="symbol">${symbol}</span><span class="amount">${amount}</span>`

      return `<span class="amount">${amount}</span><span class="symbol">${symbol}</span>`
    },
  },


}
</script>
<style>
@import "https://fonts.googleapis.com/css2?family=Work+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap";
@import "https://cdn.lineicons.com/2.0/LineIcons.css";
@import "@/assets/base.css";

</style>
<template>
  <cart/>
</template>
