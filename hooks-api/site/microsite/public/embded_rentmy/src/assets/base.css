
.rentmy-cartbar-launcher {
  position: fixed;
  right: 15px;
  bottom: 15px;
  height: 60px;
  width: 60px;
  background: #fbfdfc;
  overflow: hidden;
  cursor: pointer;
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16) !important;
  box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16) !important;
  border-radius: 5px;
  -webkit-transition: width 100ms ease-in;
  transition: width 100ms ease-in;
  -webkit-transition: all 100ms ease-in;
  transition: all 100ms ease-in;
  color: #515151;
  z-index: 99;
}

.rentmy-cartbar-launcher:hover {
  width: 320px;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-icon::before,
.rentmy-cartbar-launcher .rentmy-cartbar-launcher-icon::after,
.rentmy-cartbar-launcher .rentmy-cartbar-launcher-icon {
  width: 60px;
  height: 60px;
  background: #444;
  color: #fff;
  display: inline-block;
  vertical-align: middle;
  text-align: center;
  line-height: 60px;
  font-size: 25px;
  font-weight: unset;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary {
  background: #fbfdfc;
  width: 255px;
  height: 60px;
  display: inline-block;
  vertical-align: top;
  padding: 8px 15px;
  font-size: 13px;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary .rentmy-summary,
.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary .rentmy-dates {
  color: #444;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary .rentmy-summary strong,
.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary .rentmy-dates strong {
  font-weight: 600;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary .rentmy-summary .rentmy-total {
  float: right;
}

.rentmy-cartbar-launcher .rentmy-cartbar-launcher-summary hr {
  border: 0;
  height: 1px;
  background: #f0f3f4;
}

.rentmy-cartsidebar-overlay {
  position: unset;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  opacity: 0;
  -webkit-transition: all 0.5s cubic-bezier(0.59, -0.17, 0.3, 1.67);
  transition: all 0.5s cubic-bezier(0.59, -0.17, 0.3, 1.67);
}

.rentmy-cartsidebar-overlay.is-open {
  opacity: unset;
  pointer-events: unset;
}

.rentmy-cartsidebar-overlay.is-open .rentmy-cart-sidebar-content {
  -webkit-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content {
  -webkit-transform: translate(0px, -50px);
  transform: translate(0px, -50px);
  -webkit-transition: all 0.7s cubic-bezier(0.59, -0.17, 0.3, 1.67);
  transition: all 0.7s cubic-bezier(0.59, -0.17, 0.3, 1.67);
  position: fixed;
  padding: 0;
  width: 340px;
  height: 600px;
  background-color: #fff;
  color: #231D23;
  overflow: hidden;
  -webkit-box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.4);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.4);
  right: 15px;
  bottom: 15px;
  z-index: 9999;
  border-radius: 8px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-modalclose {
  position: absolute;
  padding: 10px 15px;
  font-size: 15px;
  text-align: center;
  background: transparent;
  color: #fff;
  top: 0;
  right: 0;
  cursor: pointer;
  -webkit-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
  z-index: 9;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-modalclose:hover {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head {
  border-radius: 8px 8px 0 0;
  width: 100%;
  z-index: 1;
  background: #555;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-title {
  font-size: 12px;
  text-transform: uppercase;
  text-align: center;
  padding: 20px 0 10px;
  z-index: 2;
  position: relative;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  font-weight: 500;
  color: #fff;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-selected-date .rentmy-selected-date-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 0;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-selected-date .rentmy-selected-date-inner .rentmy-selected-date-leftside,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-selected-date .rentmy-selected-date-inner .rentmy-selected-date-rightside {
  padding: 0 15px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-selected-date .rentmy-selected-date-inner .rentmy-selected-date-leftside p,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-sidebar-head .rentmy-selected-date .rentmy-selected-date-inner .rentmy-selected-date-rightside p {
  color: #fff;
  font-size: 12px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines {
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  padding-bottom: 10px;
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 0;
  height: 386px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul {
  list-style: none;
  margin: 0;
  padding: 0px 10px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item {
  margin: 10px auto 0 auto;
  padding: 10px;
  border-radius: 3px;
  font-size: 16px;
  font-weight: 400;
  background-color: #fff;
  -webkit-box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px;
  position: relative;
  z-index: 2;
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-product-image {
  width: 60px;
  height: 60px;
  display: inline-block;
  border-radius: 3px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line {
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
  color: #515151;
  width: 200px;
  font-size: 13px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-product-name {
  font-size: 15px;
  margin: 3px 0 7px;
  font-weight: 600;
  line-height: 1.3;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block {
  float: left;
  width: 100%;
  padding: 0;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block .rentmy-num-in {
  float: left;
  width: auto;
  border: 1px solid #f2f3f8;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block .rentmy-num-in span {
  font-size: 20px;
  width: 30px;
  height: 26px;
  text-align: center;
  display: block;
  float: left;
  background-color: #f2f3f8;
  color: #586c76;
  cursor: pointer;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  font-weight: 400 !important;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block .rentmy-num-in span:hover {
  background-color: #f2f3f8;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block .rentmy-num-in .rentmy-plus {
  font-size: 16px !important;
  line-height: 26px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block .rentmy-in-num {
  font-size: 14px;
  float: left;
  height: 26px;
  width: 40px;
  background-color: #fff;
  text-align: center;
  outline: 0;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-cart-line .rentmy-modal-quantity .rentmy-number-block input {
  border: none;
  float: left;
  width: 44px;
  line-height: 34px;
  text-align: center;
  color: #586c76;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-price {
  position: absolute;
  right: 10px;
  top: 46px;
  font-weight: 500 !important;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-remove-product {
  position: absolute;
  top: 6px;
  right: 10px;
  padding: 7px;
  cursor: pointer;
  color: #586c76;
  width: 25px;
  display: inline-block;
  text-align: center;
  opacity: 0.5;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines ul li .rentmy-list-item .rentmy-remove-product i {
  font-weight: bold;
  font-size: 10px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary {
  width: 100% !important;
  background: #FFF !important;
  padding: 15px 15px !important;
  bottom: 0;
  color: #515151;
  z-index: 3;
  -webkit-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-detail,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-strong {
  font-size: 14px;
  line-height: 1.6;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-detail:first-child,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-strong:first-child {
  margin-top: 0;
  margin-bottom: 15px;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-detail span,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-strong span {
  font-weight: 600 !important;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-detail .rentmy-amount,
.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-strong .rentmy-amount {
  float: right;
}

.rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary .rentmy-checkout-btn {
  display: block;
  padding: 12px 20px;
  border-radius: 3px;
  text-align: center;
  background-color: #444;
  width: 100%;
  color: #fff;
  text-decoration: none !important;
}

@media (max-width: 767px) {
  .rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content {
    width: 330px;
    height: 95%;
  }
  .rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-lines {
    height: 240px;
  }
  .rentmy-cartsidebar-overlay .rentmy-cart-sidebar-content .rentmy-cart-sidebar-body .rentmy-cart-sidebar-inner .rentmy-cart-sidebar-summary {
    position: absolute;
    bottom: 0;
  }
}

.rentmy-datetime-modal-overlay {
  z-index: 99999;
}

.rentmy-datetime-modal-overlay .rentmy-modal {
  width: 670px !important;
  min-height: unset !important;
  padding: 0 !important;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-body {
  padding: 20px;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-body .rentmy-modal-inner .rentmy-modal-rightside label,
.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-body .rentmy-modal-inner .rentmy-modal-leftside label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #586c76;
  display: inline-block;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-body .rentmy-modal-inner .rentmy-modal-rightside .rentmy-datetime-input input,
.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-body .rentmy-modal-inner .rentmy-modal-leftside .rentmy-datetime-input input {
  outline: 0;
  border: 1px solid #f2f3f8;
  height: 40px;
  padding: 0 10px;
  font-size: 15px;
  border-radius: 4px;
  color: #586c76;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 18px;
  border-top: 1px solid #dde1e3;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-footer .rentmy-modal-footer-inner {
  display: inline-block;
  margin-left: auto;
  padding-left: 18px;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-footer .rentmy-modal-footer-inner button {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  height: 42px;
  padding: 0px 18px;
  color: #586c76;
  background-color: white;
  font-weight: 500;
  font-size: 14px;
  text-align: center;
  line-height: 42px;
  border-radius: 6px;
  cursor: pointer;
  outline: none;
  border: 1px solid #dde1e3;
  text-decoration: none !important;
}

.rentmy-datetime-modal-overlay .rentmy-modal .rentmy-modal-footer .rentmy-modal-footer-inner button.rentmy-apply-button {
  background-color: #444 !important;
  color: #fff !important;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-top .rentmy-column-12 .rentmy-cart-ordertable table {
  background-color: transparent;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom {
  padding: 30px 0;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-row {
  padding: 0;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-row .rentmy-column-5 .rentmy-apply-coupon-btn {
  float: right;
  margin-bottom: 30px;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-row .rentmy-column-12 .rentmy-proceed-checkout-btn {
  float: right;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery {
  padding: 0 15px;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery h5 {
  font-size: 20px;
  color: #555;
  text-align: center;
  border-bottom: 1px solid;
  padding: 15px 10px;
  border-bottom: 1px solid #eee;
  background-color: #f2f3f8;
  width: 100%;
}

.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery table thead tr th h4,
.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery table thead tr td h4,
.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery table tbody tr th h4,
.rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery table tbody tr td h4 {
  font-size: 20px;
  color: #555;
  font-weight: 600;
}

@media (max-width: 767px) {
  .rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-top .rentmy-column-12 .rentmy-cart-ordertable table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
  }
  .rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-button {
    font-size: 14px;
    padding: 10px 15px;
  }
  .rentmy-cart-wrapper .rentmy-container .rentmy-row.rentmy-cart-bottom .rentmy-column-6 .rentmy-cart-order-summery {
    padding: 0;
    margin-top: 20px;
  }
}

.rentmy-checkout-wrapper {
  padding: 50px 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-customer-login {
  background-color: #f2f3f8;
  padding: 12px 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-customer-login h5 {
  font-weight: 400;
  font-size: 18px;
  color: #555;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-customer-login h5 a {
  padding-left: 15px;
  color: #59a9a1;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content {
  padding-top: 40px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-billing-address,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment {
  border: 3px solid #f2f3f8;
  padding: 0 20px;
  position: relative;
  width: 100%;
  padding-top: 25px;
  padding-bottom: 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-billing-address h2,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment h2 {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 22px;
  letter-spacing: .5px;
  position: absolute;
  left: 11px;
  top: -13px;
  background-color: #fff;
  width: auto;
  padding: 0 10px;
  height: 22px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-billing-address .rentmy-row,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-row {
  margin: 0 -15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-billing-address .rentmy-row .rentmy-column-6,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-billing-address .rentmy-row .rentmy-column-12,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-row .rentmy-column-6,
.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-row .rentmy-column-12 {
  padding: 0 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment {
  margin-top: 50px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content {
  padding: 10px 0 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-pickup-location-list {
  padding-bottom: 20px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-pickup-location-list .rentmy-radio-inline {
  width: 100%;
  height: 35px;
  padding: 6px 0;
  border-bottom: 1px solid #f2f3f8;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-pickup-location-list .rentmy-radio-inline:first-child {
  border-top: 1px solid #f2f3f8;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-pickup-location-list .rentmy-radio-inline .rentmy-radio {
  font-weight: 400;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery {
  padding-left: 30px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner {
  position: relative;
  padding: 20px 20px 20px;
  border: 3px solid #65B3AC;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-title {
  position: absolute;
  top: -15px;
  left: 20px;
  display: inline-block;
  font-size: 22px;
  text-transform: uppercase;
  margin: 0 auto;
  padding: 0 10px;
  letter-spacing: .5px;
  background-color: #fff;
  font-weight: 500;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 85px;
  width: 100%;
  border-bottom: 1px solid #f2f3f8;
  margin: 0 auto;
  padding: 10px 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-img {
  width: 60px;
  margin-right: 14px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-img img {
  width: 60px;
  height: 65px;
  -o-object-fit: contain;
  object-fit: contain;
  border: 1px solid #f2f3f8;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-product-details .rentmy-order-product-name {
  width: 100%;
  display: inline-block;
  padding-top: 8px;
  padding-bottom: 8px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-product-details .rentmy-order-product-name .rentmy-order-product-quantity {
  font-family: 'Work Sans', sans-serif;
  font-weight: 500 !important;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-product-details .rentmy-order-details-bottom {
  width: 100%;
  display: inline-block;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-product-details .rentmy-order-details-bottom .rentmy-order-quantity {
  width: auto;
  float: left;
  font-weight: 400;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-list .rentmy-order-list-box .rentmy-order-product-details .rentmy-order-details-bottom .rentmy-order-product-price {
  width: auto;
  float: left;
  text-align: right;
  padding-right: 6px;
  padding-left: 5px;
  font-size: 14px;
  font-weight: 300;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody {
  border: none;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody tr {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody tr th {
  background-color: transparent;
  border: none;
  padding: 10px 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody tr th strong {
  font-weight: 500;
  font-size: 18px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody tr td {
  border: none;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-order-table table tbody tr td strong {
  font-weight: 500;
  font-size: 18px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-additional-charge-title {
  font-weight: 500;
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 20px;
  width: 100%;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row {
  -ms-flex-wrap: unset;
  flex-wrap: unset;
  padding-bottom: 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-checkbox-inline {
  min-width: 30%;
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content {
  position: relative;
  width: 100%;
  min-height: 1px;
  -webkit-box-flex: 1;
  -ms-flex: auto;
  flex: auto;
  max-width: -webkit-max-content;
  max-width: -moz-max-content;
  max-width: max-content;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-btn-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  margin-top: -6px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-btn-toolbar .rentmy-btn-group {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
  margin-bottom: 10px;
  margin-right: 10px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-btn-toolbar .rentmy-btn-group .rentmy-btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: .375rem .75rem;
  font-size: 14px;
  line-height: 1.5;
  border-radius: .25rem;
  -webkit-transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-box-shadow .15s ease-in-out;
  position: relative;
  -ms-flex: 0 1 auto;
  -webkit-box-flex: 0;
  flex: 0 1 auto;
  background: #f2f3f8;
  border-color: #eee;
  color: #333;
  font-weight: 400;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-btn-toolbar select {
  height: 34px;
  width: auto;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area {
  display: none;
  width: 100%;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area .rentmy-input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
  width: 230px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area .rentmy-input-group input {
  position: relative;
  -ms-flex: 1 1 auto;
  -webkit-box-flex: 1;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
  height: 34px !important;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  margin-right: 10px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area .rentmy-input-group .rentmy-input-group-append {
  margin-left: -1px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area .rentmy-input-group .rentmy-input-group-append .rentmy-btn {
  color: #fff;
  background-color: #555;
  background-image: none;
  border-color: #555;
  padding: 0;
  width: 35px;
  height: 32px;
  text-align: center;
  margin-top: 0px;
  border-radius: 2px !important;
  position: relative;
  z-index: 2;
  margin-right: 5px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-input-ammount-area .rentmy-input-group .rentmy-input-group-append .rentmy-optional-cancel-btn {
  color: #555 !important;
  background-color: #f2f3f8 !important;
  border-color: #f2f3f8 !important;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-single-optional-service {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-single-optional-service label {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-optional-service .rentmy-row .rentmy-column-12 .rentmy-row .rentmy-optional-service-content .rentmy-single-optional-service select {
  height: 34px;
  width: auto;
  margin-top: -8px;
  margin-left: 5px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-payment {
  padding: 20px 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-payment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-payment-form {
  padding: 15px 0;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-payment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-payment-form .rentmy-row {
  margin: 0 -15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-checkout-payment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-payment-form .rentmy-row .rentmy-form-group {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-checkbox {
  padding-bottom: 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-checkbox .rentmy-radio-inline .rentmy-checkbox {
  font-weight: 400;
  font-size: 15px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-checkbox .rentmy-radio-inline .rentmy-checkbox a {
  font-weight: 500;
  color: #333;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-checkbox .rentmy-radio-inline .rentmy-checkbox a:hover {
  text-decoration: underline !important;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-button .rentmy-backtocart-btn {
  background-color: #2da4e0;
  padding: 12px 30px;
  color: #fff;
  border-radius: 3px;
}

.rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-button .rentmy-placeorder-btn {
  background-color: #65b3ab;
  padding: 12px 30px;
  color: #fff;
  border-radius: 3px;
  float: right;
}

@media (max-width: 991px) {
  .rentmy-checkout-wrapper {
    padding: 30px 0;
  }
  .rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-billing-fullfilment-content .rentmy-fulfillment .rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content .rentmy-pickup-location-list .rentmy-radio-inline {
    height: auto;
  }
  .rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery {
    padding-left: 0;
    margin-top: 30px;
  }
  .rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-button .rentmy-backtocart-btn {
    padding: 12px 20px;
  }
  .rentmy-checkout-wrapper .rentmy-container .rentmy-row .rentmy-column-6 .rentmy-checkout-ordersummery .rentmy-checkout-ordersummery-inner .rentmy-ordersummery-button .rentmy-placeorder-btn {
    padding: 12px 20px;
  }
}

.rentmy-collaps .rentmy-collaps-item .rentmy-collaps-btn.rentmy-active .rentmy-radio-inline .rentmy-radio span:after {
  display: block;
}

.rentmy-collaps .rentmy-collaps-item .rentmy-collaps-content {
  display: none;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner {
  margin-bottom: 30px;
  background: #f2f3f8;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner h3 {
  border-left: 3px solid #444;
  text-transform: unset;
  height: 40px;
  line-height: 40px;
  font-weight: 600;
  font-size: 20px;
  padding-bottom: 15px;
  padding-left: 15px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul {
  padding-top: 10px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li {
  display: inline-block;
  width: 100%;
  border-bottom: 1px solid #eee;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li a {
  font-weight: 500;
  text-transform: unset;
  letter-spacing: unset;
  display: block;
  color: #555;
  font-size: 15px;
  word-spacing: 3px;
  text-decoration: none;
  padding: 12px 15px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li a i {
  float: right;
  font-size: 10px;
  margin-top: 4px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li.rentmy-filter-checkbox-list {
  padding-top: 20px;
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li.rentmy-filter-checkbox-list .rentmy-checkbox-inline {
  padding: 2px 0;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li .rentmy-filter-collaps-content {
  display: none;
  padding: 20px 15px 15px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li .rentmy-filter-collaps-content .rentmy-row .rentmy-column-12 {
  text-align: center;
  padding-top: 15px;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper ul li .rentmy-filter-collaps-content .rentmy-row .rentmy-column-12 .rentmy-button {
  display: inline-block;
  color: #fff;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper.rm-category-area .rentmy-filter-collaps-content {
  display: none;
  padding: 0;
  width: 100%;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper.rm-category-area .rentmy-filter-collaps-content ul {
  border-top: 1px solid #eee;
  padding-top: 0;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper.rm-category-area .rentmy-filter-collaps-content ul li a {
  padding-left: 25px;
  color: #777;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper.rm-category-area .rentmy-filter-collaps-content ul li:last-child {
  border-bottom: none;
}

.rentmy-container .rentmy-row .rentmy-column-3 .rentmy-category-filter .rentmy-category-filter-inner .rentmy-category-filter-wrapper .rm-filter-area .rentmy-filter-collaps-content {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-container .rentmy-row .rentmy-category-product-list {
  padding-left: 30px;
}

.rentmy-container .rentmy-row .rentmy-category-product-list .rentmy-product-list .rentmy-product {
  position: relative;
  width: 100%;
  min-height: 1px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 33.333333%;
  flex: 0 0 33.333333%;
  max-width: 33.33333%;
  padding: 0 15px;
}

@media (max-width: 1199px) {
  .rentmy-container .rentmy-row .rentmy-column-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .rentmy-container .rentmy-row .rentmy-category-product-list {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .rentmy-container .rentmy-row .rentmy-category-product-list .rentmy-product-list .rentmy-product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 767px) {
  .rentmy-container .rentmy-row .rentmy-column-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .rentmy-container .rentmy-row .rentmy-category-product-list {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 0;
  }
  .rentmy-container .rentmy-row .rentmy-category-product-list .rentmy-product-list .rentmy-product {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.rentmy-scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background-color: #F5F5F5;
}

.rentmy-scrollbar::-webkit-scrollbar {
  width: 6px;
  background-color: #f2f3f8;
}

.rentmy-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #555;
}

.rentmy-loading {
  width: 130px;
  padding: 5px 10px;
  background-color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-box-shadow: 0 0 30px 0 rgba(51, 77, 136, 0.08);
  box-shadow: 0 0 30px 0 rgba(51, 77, 136, 0.08);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 2px;
}

.rentmy-loading-text {
  display: inline-block;
  font-weight: 400;
  color: #888;
}

.rentmy-loading-circle {
  display: inline-block;
  height: 0;
  width: 0;
  padding: 10px;
  border: 2px solid #ccc;
  border-right-color: #333;
  border-radius: 22px;
  -webkit-animation: rotate 1s infinite linear;
  margin-left: 10px;
}

@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}

.rentmy-optional-service-tablearea {
  padding: 0;
}

.rentmy-optional-service-tablearea .rentmy-ordrcomplete-optionalservice-table thead,
.rentmy-optional-service-tablearea .rentmy-ordrcomplete-optionalservice-table tbody {
  border-left: none;
  border-right: none;
}

.rentmy-ordercomplete-success-message {
  position: relative;
  padding: .75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: .25rem;
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
  margin-bottom: 20px;
}

.rentmy-contact-wrapper {
  padding: 50px 0;
}

.rentmy-contact-wrapper .rentmy-container {
  padding: 0;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-12 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-12 h5 {
  font-size: 35px;
  padding-bottom: 40px;
  position: relative;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 .rentmy-row .rentmy-column-12,
.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 .rentmy-row .rentmy-column-6 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 .rentmy-row .rentmy-column-12 textarea,
.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 .rentmy-row .rentmy-column-6 textarea {
  height: 150px;
  overflow: auto;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-5 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-5 iframe {
  width: 100%;
  height: 300px;
  margin-top: 23px;
  border: 5px solid #f2f3f8;
}

@media (max-width: 991px) {
  .rentmy-contact-wrapper {
    padding: 50px 0;
  }
  .rentmy-contact-wrapper .rentmy-container {
    padding-left: 15px;
    padding-right: 15px;
  }
  .rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .rentmy-contact-wrapper .rentmy-container .rentmy-row .rentmy-column-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.rentmy-aboutus-wrapper {
  padding: 50px 0;
}

.rentmy-aboutus-wrapper .rentmy-container {
  padding: 0;
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 h5 {
  font-size: 35px;
  padding-bottom: 40px;
  position: relative;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 p {
  font-weight: 400;
  color: #666;
  text-align: justify;
}

.rentmy-aboutus-wrapper {
  padding: 50px 0;
}

.rentmy-aboutus-wrapper .rentmy-container {
  padding: 0;
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 h5 {
  font-size: 35px;
  padding-bottom: 40px;
  position: relative;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row {
  margin-left: -15px;
  margin-right: -15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 {
  padding-left: 15px;
  padding-right: 15px;
}

.rentmy-aboutus-wrapper .rentmy-container .rentmy-row .rentmy-column-12 p {
  font-weight: 400;
  color: #666;
  text-align: justify;
}
/*# sourceMappingURL=style.css.map */
