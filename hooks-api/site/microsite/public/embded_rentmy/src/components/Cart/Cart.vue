<template>
    <div class="Cart">
      <!-- rentmy cart bar  -->
      <div class="rentmy-cartbar-launcher" @click="openMiniCartSidebar">
        <div class="rentmy-cartbar-launcher-icon">
          <i class="lni lni-shopping-basket" aria-hidden="true"></i>
        </div>
        <div class="rentmy-cartbar-launcher-summary">
          <div class="rentmy-dates">
            <span v-if="rentalStart" class="rentmy-selected-date">
              {{ scheduleFormat(schedulePickerConfig, rentalStart) }} - {{
                scheduleFormat(schedulePickerConfig, rentalEnd)
              }}
            </span>
            <span v-if="!rentalStart" class="rentmy-selected-date"> Select Rental Schedule</span>
          </div>
          <hr/>
          <div class="rentmy-summary">
            <span>
                <strong id="item-count">
                  {{ getItemCount }}
                  <span>
                    <strong v-if="getItemCount > 1">items</strong>
                     <strong v-else>item</strong>
                  </span>
                </strong>
              </span>
            <span class="rentmy-total">
                <strong v-if="currencyConfig" v-html="currencyFormat(currencyConfig,  getSubtotal )"></strong>
              </span>
          </div>
        </div>
      </div>
      <!--cart sidebar-->
      <div class="rentmy-cartsidebar-overlay" :class="isOpen">
        <div class="rentmy-cart-sidebar-content">
          <div class="rentmy-cart-modalclose" @click="closeMiniCartSidebar"><i class="lni lni-close"></i></div>
          <div class="rentmy-cart-sidebar-body">
            <div class="rentmy-cart-sidebar-inner">
              <div class="rentmy-sidebar-head">
                <div class="rentmy-title">Cart</div>
                <div class="rentmy-selected-date">
                  <DatetimePicker  />
                </div>
              </div>
              <div class="rentmy-cart-sidebar-lines">
                <ul>
                  <li v-for="item in getItems" v-bind:key="item.key">
                    <div class="rentmy-list-item" :class="cartErrorClass(item)">
                      <img class="rentmy-product-image" :src="getImageUrl(item.product)" alt="product image">
                      <div class="rentmy-cart-line">
                        <div class="rentmy-product-name rentmy-large">{{ item.product.name }}</div>
                        <div class="rentmy-modal-quantity">
                          <div class="rentmy-number-block">
                            <niceQuantity @change="qtyChanged($event, item)" :val="item.quantity" :min="1" :max="item.available"/>
                          </div>
                        </div>
                        <span class="rentmy-price" v-html="currencyFormat(currencyConfig,  item.sub_total )"></span>
                        <p v-if="rentalStart">Available: <span class="rentmy-available">{{ item.available }}</span></p>
                        <button class="rentmy-remove-product" @click="deleteCartItem(item.id, item.product.id)"><i class="lni lni-close"></i></button>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="rentmy-cart-sidebar-summary">
                <div class="rentmy-detail rentmy-strong">
                  <span> Subtotal </span>
                  <span v-if="currencyConfig" class="rentmy-amount" v-html="currencyFormat(currencyConfig,  getSubtotal)"></span>
                </div>
                <a :href="checkoutURL" @click="clearCart"  class="button rentmy-checkout-btn" :class="btnCheckoutClass" >CHECKOUT</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
import {createNamespacedHelpers} from "vuex";
import {RentMySDK} from "rentmyjsdk";
 import niceQuantity from "./niceQuantity.vue";
 import DatetimePicker from "./datetimePicker.vue";
import moment from "moment/moment"

const {mapGetters: storeMapGetters} = createNamespacedHelpers('store');
const {mapGetters: cartMapGetters} = createNamespacedHelpers('cart');

export default {
  name: 'Cart',
  components: {niceQuantity, DatetimePicker},
  data() {
    return {
      checkOutDomain: "rentmy.shop",
      isOpen: "",
      rentMYLoader: null,
      editDate: false,
      defaultType: 'cart',
      defaultRentalType: 'rent',
      visiblePages: ['/', '/products-list']
    }
  },
  mounted() {
    this.rentMYLoader = document.querySelector("#RENTMY_LOADER");
  },
  computed: {
    ...storeMapGetters(["schedulePickerConfig", "currencyConfig"]),
    ...cartMapGetters(["rentalStart", "getToken", "rentalEnd", "getItems", 'getItemCount', 'getCartErrors',
      'getSubtotal', 'getDeliveryCharge', 'getDiscount', 'getTax', 'getDepositAmount', 'getTotal']),
    btnCheckoutClass() {
      if (!this.rentalStart || this.getCartErrors)
        return 'disabled'

      return ''
    },
    checkoutURL(){
      const storeSlug =  RentMySDK.utils.Storage.get('storeSlug');
      return this.getToken ? "https://"+storeSlug+"."+this.checkOutDomain+"/cart/"+this.getToken : '#'
    }
  },
  watch:{
    getItems:function (){
      this.openMiniCartSidebar()
    }
  },
  methods: {
    clearCart(){
      this.$store.dispatch("cart/clearCart");
    },
    openEditDate() {
      this.editDate = true
    },
    closeDatetimePicker() {
      this.editDate = false
      this.$store.dispatch("cart/setOrUpdateRentalDates", {_v: this, param: {}, container: this.rentMYLoader});
    },
    openMiniCartSidebar() {
      this.isOpen = "is-open";
    },
    closeMiniCartSidebar() {
      this.isOpen = "";
    },
    cartErrorClass(item) {
      if (!this.getCartErrors)
        return ''

      for (const errorItem of this.getCartErrors)
        if (errorItem.id === item.id)
          return 'rentmy-list-empty-item'

    },
    deleteCartItem(itemId, productId) {
      this.$store.dispatch("cart/deleteCartItem", {
        _v: this, container: this.rentMYLoader,
        params: {
          itemId: itemId,
          productId: productId
        }
      });
    },
    qtyChanged(value, item) {
      if (item.quantity === value)
        return

      this.$store.dispatch("cart/updateCart", {
        params: {
          increment: value > item.quantity ? 1 : '0',
          item: item,
          rentalType: this.defaultRentalType
        },
        _v: this, container: this.rentMYLoader
      });
    },
    switchLanguage(locale) {
      //  Update the locale cookie
      this.$cookies.set("locale", locale, {
        path: "/"
      });

      //  Refresh page to reload components
      window.location.reload(true);
    },
    rentalTypes(product) {
      if (!product.prices)
        return false

      const types = []
      for (const [type, prices] of  product.prices){

      }
    },
    currencyFormat(currencyConfig, amount, isSymbol = true) {
      let symbol = currencyConfig.symbol
      if (!isSymbol)
        symbol = currencyConfig.code

      if (currencyConfig.pre)
        return `<span class="symbol">${symbol}</span><span class="amount">${parseFloat(amount).toFixed(2)}</span>`

      return `<span class="amount">${parseFloat(amount).toFixed(2)}</span><span class="symbol">${symbol}</span>`
    },
    scheduleFormat(schedulePickerConfig, dateTime) {
      let format = schedulePickerConfig.dateFormat;
      if (schedulePickerConfig.showStartTime || schedulePickerConfig.showEndTime)
        format = format + ' h:mm A'

      return moment(dateTime).format(format);
    },
    getImageUrl(product) {
      return RentMySDK.utils.helpers.productThumb(product)
    },
    dateTimeConversion(dateTime){
      const data = Date.parse(dateTime);
      let localDate = new Date(data);
      let month = localDate.getMonth() + 1;
      month = (month<10) ? ('0'+month.toString()) : month.toString();
      let day = localDate.getDate();
      day = (day<10) ? ('0'+day.toString()) : day.toString();
      let hour = localDate.getHours();
      hour = (hour<10) ? ('0'+hour.toString()) : hour.toString();
      let minute = localDate.getMinutes();
      minute = (minute<10) ? ('0'+minute.toString()) : minute.toString();

      /* date('Y-m-d H:i', strtotime($data['rent_start'] . ' ' . $data['start_time']));*/
      const date = localDate.getFullYear() + '-' + month + '-' + day + ' ' + hour + ":" + minute;
      return date;
    },
    dateFieldFormat(){
      const start = RentMySDK.utils.Storage.get('rent_start');
      const end = RentMySDK.utils.Storage.get('rent_end');
      let date = '';
      if(start){
        date = this.dateFiledConvert(start);
        if(end){
          date = date + ' - ' + this.dateFiledConvert(end);
        }
      }
      return date;
    },
    dateFiledConvert(dateTime){
      const data = Date.parse(dateTime);
      let localDate = new Date(data);
      let month = localDate.getMonth() + 1;
      month = (month<10) ? ('0'+month.toString()) : month.toString();
      let day = localDate.getDate();
      day = (day<10) ? ('0'+day.toString()) : day.toString();

      let hour = localDate.getHours();
      if(hour>11) hour = hour - 12;
      let minute = localDate.getMinutes();
      minute = (minute<10) ? ('0'+minute.toString()) : minute.toString();
      const meridian = (hour > 11) ? 'PM' : 'AM';
      /* 02/16/2021 08:00 AM */
      const date = month + '/' + day + '/' + localDate.getFullYear() + ' ' + hour + ":" + minute + ' ' + meridian;
      return date;
    }
  }

}
</script>
