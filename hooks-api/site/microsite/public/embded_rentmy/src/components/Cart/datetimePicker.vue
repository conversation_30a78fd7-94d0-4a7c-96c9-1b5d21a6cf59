<template>
  <div class="rentmy-selected-date-inner" @click="openEditDate($event)">
    <p v-if="!rentalStart"> Select Rental Schedule </p>
    <div class="rentmy-selected-date-leftside" v-if="rentalStart">
      <p> {{ scheduleFormat(schedulePickerConfig, rentalStart) }} </p>
    </div>
    <div class="rentmy-selected-date-rightside" v-if="rentalStart">
      <p>{{ scheduleFormat(schedulePickerConfig, rentalEnd) }} </p>
    </div>
  </div>

</template>

<script>
import {
  datepicker
} from "@mobiscroll/javascript";
import '@mobiscroll/javascript/dist/css/mobiscroll.min.css'
import {createNamespacedHelpers} from "vuex";
import {RentMySDK} from "rentmyjsdk";
import moment from "moment/moment"
const {mapGetters: storeMapGetters} = createNamespacedHelpers('store');
const {mapGetters: cartMapGetters} = createNamespacedHelpers('cart');

export default {
  props: ['value'],
  name: 'DatetimePicker',
  data() {
    return {
      rentMYLoader: null,
      datepicker: null,
      form: {
        dates: ''
      },
      rentStart: '',
      rentEnd: '',
      responsive: {
        small: {
          display: 'center'
        },
        medium: {
          display: 'center'
        },
        large: {
          display: 'center'
        }
      }
    }
  },
  computed: {
    ...storeMapGetters(["schedulePickerConfig"]),
    ...cartMapGetters(["rentalStart", "rentalEnd"]),
  },
  created() {
    // "02/16/2021 09:00 AM";
    this.form.dates = this.dateFieldFormat();
  },
  mounted() {
    this.init()
    this.rentMYLoader = document.querySelector("#RENTMY_LOADER");
  },
  methods: {
    scheduleFormat(schedulePickerConfig, dateTime) {
      let format = schedulePickerConfig.dateFormat;
      if (schedulePickerConfig.showStartTime || schedulePickerConfig.showEndTime)
        format = format + ' h:mm A'

      return moment(dateTime).format(format);
    },
    openEditDate() {
      this.datepicker.open()
    },
    closeDatetimePicker() {
      this.editDate = false
      this.$store.dispatch("cart/setOrUpdateRentalDates", {_v: this, container: this.rentMYLoader, params: {}});
    },

    async checkedRentalDates(event) {

      const dateTimes = event.valueText;
      let rentalTimes = dateTimes.split(" - ");
      if (rentalTimes[0] && rentalTimes[0].length) {
        this.rentStart = this.dateTimeConversion(rentalTimes[0]);
        this.rentEnd = this.rentStart;
      }
      if (rentalTimes[1] && rentalTimes[1].length) {
        this.rentEnd = this.dateTimeConversion(rentalTimes[1]);
      }

      const start = new Date(this.rentStart);
      if (start) {
        await this.$store.dispatch('cart/initiateRentalDates', {
          _v: this, container: this.rentMYLoader,
          rent: {start: this.rentStart, end: this.rentEnd}
        });
      }

      this.$emit('change', event)
    },
    init() {
      let type = "date";
      let controls = ['calendar'];
      if (this.schedulePickerConfig.showStartTime || this.schedulePickerConfig.showEndTime) {
        controls.push('time');
      }
      if (this.schedulePickerConfig.showEndDate || this.schedulePickerConfig.showEndTime) {
        type = "range";
      }

      this.datepicker = datepicker('.rentmy-selected-date-inner', {
        controls: controls,
        select: type,
        responsive: this.responsive,
        touchUi: true,
        min: new Date(),
        onClose:  (event, inst) =>{
          this.checkedRentalDates(event)
        }
      });
    },

    dateTimeConversion(dateTime){
      const data = Date.parse(dateTime);
      let localDate = new Date(data);
      let month = localDate.getMonth() + 1;
      month = (month<10) ? ('0'+month.toString()) : month.toString();
      let day = localDate.getDate();
      day = (day<10) ? ('0'+day.toString()) : day.toString();
      let hour = localDate.getHours();
      hour = (hour<10) ? ('0'+hour.toString()) : hour.toString();
      let minute = localDate.getMinutes();
      minute = (minute<10) ? ('0'+minute.toString()) : minute.toString();

      /* date('Y-m-d H:i', strtotime($data['rent_start'] . ' ' . $data['start_time']));*/
      const date = localDate.getFullYear() + '-' + month + '-' + day + ' ' + hour + ":" + minute;
      return date;
    },

    dateFieldFormat(){
      const start = RentMySDK.utils.Storage.get('rent_start');
      const end = RentMySDK.utils.Storage.get('rent_end');
      let date = '';
      if(start){
        date = this.dateFiledConvert(start);
        if(end){
          date = date + ' - ' + this.dateFiledConvert(end);
        }
      }
      return date;
    },

    dateFiledConvert(dateTime){
      const data = Date.parse(dateTime);
      let localDate = new Date(data);
      let month = localDate.getMonth() + 1;
      month = (month<10) ? ('0'+month.toString()) : month.toString();
      let day = localDate.getDate();
      day = (day<10) ? ('0'+day.toString()) : day.toString();

      let hour = localDate.getHours();
      if(hour>11) hour = hour - 12;
      let minute = localDate.getMinutes();
      minute = (minute<10) ? ('0'+minute.toString()) : minute.toString();
      const meridian = (hour > 11) ? 'PM' : 'AM';
      /* 02/16/2021 08:00 AM */
      const date = month + '/' + day + '/' + localDate.getFullYear() + ' ' + hour + ":" + minute + ' ' + meridian;
      return date;
    }
  }
}
</script>
