<template>
  <div class="rentmy-num-in">
    <span class="rentmy-minus dis" @click="qtyChange(false)">-</span>
    <input type="text" class="rentmy-in-num" readonly="" @change="inputItselfChanged($event)" :min="getMin"
           :max="getMax" v-model="num">
    <span class="rentmy-plus" @click="qtyChange(true)">+</span>
  </div>
</template>
<script>
export default {
  props: ['max', 'min', 'val'],
  name: 'niceQuantity',
  data() {
    return {
      qty: 0,
      alert: {
        min: {
          title: 'Invalid Quantity',
          message: 'Minimum number of quantity has been met'
        },
        max: {
          title: 'Invalid Quantity',
          message: 'Maximum number of quantity has been met'
        }
      }
    }
  },
  computed: {
    getMax() {
      return this.max || null
    },
    getMin() {
      return this.min || 0
    },
    num: {
      get (){
        return this.qty
      },
      set(value){
        this.qty = value
      }
    }
  },
  methods: {
    inputItselfChanged(qty) {
      if (qty < this.getMin) {
        this.$store.dispatch('alerts/warning', this.alert.min)
        return
      }

      if (this.getMax && qty > this.getMax) {
        this.$store.dispatch('alerts/warning', this.alert.max)
        return;
      }

      this.$emit('change', this.num)
    },
    qtyChange(isIncrement) {
      if (this.num <= this.getMin && !isIncrement) {
        this.$store.dispatch('alerts/warning', this.alert.min)
        return
      }

      if (this.getMax && this.num >= this.getMax && isIncrement) {
        this.$store.dispatch('alerts/warning', this.alert.max)
        return;
      }

      if (isIncrement)
        this.num++
      else
        this.num--

      this.$emit('change', this.num)
    }
  },
  watch:{
    val: function(newVal) {
      this.qty = newVal;
    }
  },
  mounted() {
    this.qty = this.val
  }
}

</script>
