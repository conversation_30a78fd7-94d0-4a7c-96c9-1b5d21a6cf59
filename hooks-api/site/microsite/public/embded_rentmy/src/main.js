
import { createApp } from 'vue'
import { createStore } from 'vuex'

import App from './App.vue'

import cart from "./store/cart";
import store from "./store/store";
import alerts from "./store/alerts";
import products from "./store/products";

import VueLoading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/vue-loading.css';

const app = createApp(App)
app.use(createStore({
    modules: {
        store : store,
        cart : cart,
        alerts: alerts,
        products: products
    },
    strict: true,
}))
app.use(VueLoading);
app.mount('#RENTMY_CART_CONTAINER')
