import {
    toast
} from "@mobiscroll/javascript";


export default {
    namespaced: true,
    state: () => ({
        message: '',
        title: '',
        hideTimeout: 5000,
        variant: 'primary',
        solid: true,
        append: true,
    }),
    actions: {
        success({commit}, body = {}) {
            if (body.message)
                commit('SET_MESSAGE', body.message);
            if (body.title)
                commit('SET_TITLE', body.title);

                commit('SET_TYPE', 'primary');

            commit('OPEN');
        },
        warning({commit}, body = {}) {
            if (body.message)
                commit('SET_MESSAGE', body.message);
            if (body.title)
                commit('SET_TITLE', body.title);

            commit('SET_TYPE', 'warning');

            commit('OPEN');
        },
        error({commit}, body = {}) {
            if (body.message)
                commit('SET_MESSAGE', body.message);
            if (body.title)
                commit('SET_TITLE', body.title);

            commit('SET_TYPE', 'danger');

            commit('OPEN');
        }
    },
    mutations: {
        SET_MESSAGE(state, message) {
            state.message = message;
        },
        SET_TITLE(state, title) {
            state.title = title;
        },
        SET_TYPE(state, variant) {
            state.variant = variant;
        },
        OPEN(state) {
            toast( {
                message: state.message,
                color: state.variant,
            })
        },
    }
}
