import {RentMySDK} from "rentmyjsdk";

export default {
    namespaced: true,
    state: () => ({
        token: null,
        cart: null,
        cartErrors: null,
        rentStart: null,
        rentEnd: null,
        defaultStartTime: '12:00', // will be used when start time hidden
        defaultEndTime: '12:00', // will be used same as start date
        defaultEndDate: 'default' // when end date is hidden . but client use '+1 hour' , '+1 day' in the configstart_time = '12:00' , // will be used when start time hidden
    }),
    getters: {
        getToken: state => state.token,
        rentalStart: state => state.rentStart,
        rentalEnd: state => state.rentEnd,

        getCart: state => state.cart,
        getItemCount: state => state.cart?.cart_items?.length || 0,
        getItems: state => state.cart?.cart_items || [],
        getCartErrors: state => state.cartErrors,

        getSubtotal: state => state.cart?.sub_total || 0,
        getDeliveryCharge: state => state.cart?.delivery_charge || 0,
        getDiscount: state => state.cart?.sub_total || 0,
        getTax: state => state.cart?.tax || 0,
        getDepositAmount: state => state.cart?.depositAmount || 0,
        getTotal: state => state.cart?.total || 0,
    },
    actions: {
        async loadCart({state, commit, dispatch}, byForce= false, body = {}) {

            const start =  await RentMySDK.utils.Storage.get('rent_start')
            const end = await RentMySDK.utils.Storage.get('rent_end')
            await commit('SET_RENTAL_DATES', {
                start: start,
                end: end,
            });

            const token =  await RentMySDK.utils.Storage.get('cartToken')
            //return if cart token is not exists in local storage
            if (!token)
                return

            await commit('SET_TOKEN', token);

            //return if already cart data loaded
            if (state.cart && !byForce)
                return;

            let loader = null;
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }
            const rs = await RentMySDK.request('cart', 'viewCart', null, null, [state.token]);
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK')
                commit('SET_CART', rs.data);

            await dispatch('setOrUpdateRentalDates', {_v: null, params: {}, noAlert: true})
        },

        async initiateRentalDates({state, commit, dispatch}, data={}) {
            if(data?.rent){
                if (data.rent?.start)
                    await RentMySDK.utils.Storage.put('rent_start', data.rent.start);
                if (data.rent?.end)
                    await RentMySDK.utils.Storage.put('rent_end', data.rent.end);

                await commit('SET_RENTAL_DATES', data.rent);
                await dispatch('cart/setOrUpdateRentalDates', data, {root: true});
            }
        },

        async setOrUpdateRentalDates({state, commit, dispatch}, data) {
            const suggestion = RentMySDK.suggestions.cart.setOrUpdateRentalDates.body.required;
            if (!state.token){
                if (!data.noAlert) {
                    dispatch('alerts/success', {
                        message: 'Rental dates are added',
                        title: 'Rental Date'
                    }, {root: true});
                }

                return;
            }

            //set cart params
            let params = {}
            params[suggestion.token] = state.token;
            params[suggestion.source] = 'nano';
            params[suggestion.type] = 'cart';

            if (state.rentStart)
                params[suggestion.startDate] = state.rentStart;

            if (state.rentEnd)
                params[suggestion.endDate] = state.rentEnd;

            let loader = null;
            if (data.container && data._v && data._v.$loading){
                loader = data._v.$loading.show({
                    container: data.container,
                    canCancel: true,
                    onCancel: data._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('cart', 'setOrUpdateRentalDates', params);
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK') {
                commit('SET_CART', rs.data);

                if (!data.noAlert){
                    if (rs.response.result?.error){
                        dispatch('alerts/error', {
                            message: rs.response.result?.message,
                            title: 'Rental Date'
                        }, {root: true});

                        commit('SET_ERRORS', rs.response.result.error);
                    }else {
                        dispatch('alerts/success', {
                            message: 'Rental Dates are added',
                            title: 'Rental Date'
                        }, {root: true});
                    }
                }
            }else {
                if (!data.noAlert) {
                    dispatch('alerts/warning', {
                        message: 'Something went wrong',
                        title: 'Add To Cart'
                    }, {root: true});
                }
            }
        },

        async addToCart({state, commit, dispatch}, data) {

            let apiName = 'addOrUpdateToCartWithoutDate';

            if (state.token)
                data['token'] = state.token;

            if (state.rentStart){
                apiName = 'addToCart';
                data['rent_start'] = state.rentStart;
            }

            if (data.apiName)
                apiName = data.apiName;

            if (state.rentEnd)
                data['rent_end'] = state.rentEnd;

            let loader = null;
            if (data.container && data._v && data._v.$loading){
                loader = data._v.$loading.show({
                    container: data.container,
                    canCancel: true,
                    onCancel: data._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('cart', apiName, data);

            if (state.rentStart)
                await dispatch('setOrUpdateRentalDates', {_v: null, params: {}, noAlert: true})

            if (loader)
                loader.hide()

            if (rs.response?.result?.error){
                dispatch('alerts/warning', {
                    message: rs.response?.result?.error,
                    title: 'Add To Cart'
                }, {root: true});
                return;
            }

            if (rs.response.status === 'OK') {
                if (!state.rentStart)
                    commit('SET_CART', rs.data);

                dispatch('alerts/success', {
                    message: 'Product added to the cart successfully',
                    title: 'Add To Cart'
                }, {root: true});
            }else {
                dispatch('alerts/warning', {
                    message: 'Something went wrong',
                    title: 'Add To Cart'
                }, {root: true});
            }
        },

        async deleteCartItem({state, commit, dispatch}, data) {

            if (!state.token){
                dispatch('alerts/warning', {
                    message: 'Empty cart token, unable to delete cart item',
                    title: 'Delete Cart'
                }, {root: true});

                return
            }

            const params = {};
            const suggestion = RentMySDK.suggestions.cart.deleteCartItem.body;
            params[suggestion.required.token] = state.token;
            params[suggestion.required.cartItemId] = data.params.itemId;
            params[suggestion.required.productId] = data.params.productId;

            let loader = null;
            if (data.container && data._v && data._v.$loading){
                loader = data._v.$loading.show({
                    container: data.container,
                    canCancel: true,
                    onCancel: data._v.onCancel,
                });
            }
            const rs = await RentMySDK.request('cart', 'deleteCartItem', params);

            if(loader)
                loader.hide();

            if (rs.response.status === 'OK') {
                commit('SET_CART', rs.data);

                dispatch('alerts/success', {
                    message: 'Item has been successfully removed from cart',
                    title: 'Cart Item Delete'
                }, {root: true});
            }
        },

        async updateCart({state, commit, dispatch}, data) {

            if (!state.token)
                return

            let apiName = 'updateCart';
            let CartUpdateSuggestions = RentMySDK.suggestions.cart.addOrUpdateToCartWithoutDate.body;
            let params = {};

            params['token'] = state.token;

            if (state.rentStart) {
                CartUpdateSuggestions = RentMySDK.suggestions.cart.updateCart.body;
                params[CartUpdateSuggestions.required.id] = data.params.item.id;
                params[CartUpdateSuggestions.required.increment] = data.params.increment;
                params[CartUpdateSuggestions.required.price] = data.params.item.price;
            } else {
                params[CartUpdateSuggestions.required.quantity] = (data.params.increment === 1) ? 1 : -1;
                params[CartUpdateSuggestions.required.productId] = data.params.item.product.id;
                params[CartUpdateSuggestions.required.rentalType] = data.params.rentalType;
                params[CartUpdateSuggestions.required.location] = RentMySDK.utils.Storage.get('locationId');
                params[CartUpdateSuggestions.required.productType] = data.params.item.product_type;
                params[CartUpdateSuggestions.required.type] = 'cart';
                if (data.params.item.product_type === 1 && data.params.item?.variants_products_id)
                    params[CartUpdateSuggestions.required.variantsProductsId] = data.params.item.variants_products_id;

                params[CartUpdateSuggestions.optional.cartItemId] = data.params.item.id;
                params[CartUpdateSuggestions.optional.price] = data.params.item.price;
                params[CartUpdateSuggestions.optional.increment] = data.params.increment;
                if (data.params.item.id)
                    params[CartUpdateSuggestions.optional.id] = data.params.item.id;
                params[CartUpdateSuggestions.optional.action] = 'update-cart-item';
                apiName = 'addOrUpdateToCartWithoutDate'
            }

            let loader = null;
            if (data.container && data._v && data._v.$loading){
                loader = data._v.$loading.show({
                    container: data.container,
                    canCancel: true,
                    onCancel: data._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('cart', apiName, params);

         //   await dispatch('setOrUpdateRentalDates', {_v: null, params: {}, noAlert: true})

            if(loader)
                loader.hide();

            if (rs.response?.result?.error){
                dispatch('alerts/warning', {
                    message: rs.response?.result?.error,
                    title: 'Add To Cart'
                }, {root: true});
                return false;
            }

            if (rs.response.status === 'OK') {
                commit('SET_CART', rs.data);

                dispatch('alerts/success', {
                    message: 'Cart has been updated successfully',
                    title: 'Update Cart'
                }, {root: true});
            }else {
                dispatch('alerts/warning', {
                    message: 'Something went wrong',
                    title: 'Update Cart'
                }, {root: true});
            }
        },

        async clearCart({state, commit, dispatch}, data = {}) {
            await RentMySDK.utils.Storage.clear()
            if (data.alert)
                dispatch('alerts/warning', {
                    message: 'Something went wrong',
                    title: 'Update Cart'
                }, {root: true});
        },
    },

    mutations: {
        SET_TOKEN(state, token) {
            state.token = token;
            RentMySDK.utils.Storage.put('cartToken', token)
        },

        SET_CART(state, data) {
            if (data?.token){
                state.token = data.token;
                RentMySDK.utils.Storage.put('cartToken', data.token);
            }
            if (state.cart?.cart_items){
                for (const item of state.cart.cart_items){
                    if (data?.cart_items){
                        for (const newItem of data.cart_items){
                            if (item.id === newItem.id && !newItem.available && item.available)
                                newItem.available = item.available
                        }
                    }

                }
            }
            state.cart = data;
        },

        SET_ERRORS(state, errors) {
            state.cartErrors = errors
        },

        SET_RENTAL_DATES(state, data={}) {
            state.rentStart =  data?.start || null;
            state.rentEnd =  data?.end || null;
        },
    }
}
