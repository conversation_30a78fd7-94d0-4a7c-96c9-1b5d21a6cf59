import {RentMySDK} from "rentmyjsdk";

export default {
    namespaced: true,
    state: () => ({
        products: [],
        pagination: null,
        sortOption: ''
    }),
    getters: {
        products: state => state.products,
        pagination: state => state.pagination,
        sortedBy: state => state.sortOption
    },
    actions: {
        async loadProducts({state, commit}, body = {}) {
            //return when already has products pulled and land from different page
            if (body.init && state.products.length > 0)
                return;

            //if already total exists
            if (!body.init && state.products.length === state.pagination.total){
                return;
            }

            let loader = null
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('products', 'productsOnlineList', body.payloads)

            if (rs.response.status === 'OK') {

                if (body.sorting)
                    commit('SET_SORT', body.sorting);

                if (state.products.length > 0 && !body.sorting)
                    commit('APPEND_PRODUCTS', rs.data);
                else
                    commit('SET_PRODUCTS', rs.data);

                const pagination = rs.response.result
                delete pagination.data
                commit('SET_PAGINATION', pagination);
            }

            if (loader)
                loader.hide()
        },

        async sortBy({state, commit}, body = {}) {
            commit('SET_SORT', body.sortBy);
        },

        async view({state, commit}, body = {}) {
            let apiName = 'productDetails'
            if (body.isPackage)
                apiName = 'packageDetails'

            let query = {}
            const suggestion = RentMySDK.suggestions.products.productDetails.query
            query[suggestion.required.location] = await RentMySDK.utils.Storage.get('locationId')
            query[suggestion.optional.token] = await RentMySDK.utils.Storage.get('cartToken')

            let loader = null
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }
            const rs = await RentMySDK.request('products', apiName, null, query, [body.pUID, new Date().getTime()]);
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK') {
                rs.data.type = body.isPackage ? 2: 1
                return rs.data;
            }

            return false;
        },

        async packageVariantChangedQuantity({state, commit}, body = {}) {
            let query = {}
            const suggestion = RentMySDK.suggestions.products.packageVariantQuantity.query
            query[suggestion.required.location] = await RentMySDK.utils.Storage.get('locationId')
            query[suggestion.optional.token] = await RentMySDK.utils.Storage.get('cartToken')

            if (body.variants.length > 0)
                query[suggestion.optional.variants] = body.variants

            let loader = null
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('products', 'packageVariantQuantity', null, query, [body.pUID, new Date().getTime()]);
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK')
                return rs.data | 0;

            return false;
        },
        async productVariantChain({state, commit}, body = {}) {
            let query = {}
            const suggestion = RentMySDK.suggestions.products.productVariantChain.query
            query[suggestion.required.productId] = body.productId
            query[suggestion.required.variantId] = body.variantId
            query[suggestion.optional.variantChain] = body.variantChain ?? ''
            let loader = null
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }
            const rs = await RentMySDK.request('products', 'productVariantChain', null, query);
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK')
                return rs.data;

            return false;
        },

        async productPathOfVariantChain({state, commit}, body = {}) {
            let query = {}
            const suggestion = RentMySDK.suggestions.products.productLastVariantChain.query
            query[suggestion.required.productId] = body.productId
            query[suggestion.required.variantId] = body.variantId
            query[suggestion.required.variantChain] = body.variantChain ?? ''
            let loader = null
            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }
            const rs = await RentMySDK.request('products', 'productLastVariantChain', null, query, []);
            if (loader)
                loader.hide()
            if (rs.response.status === 'OK') {
                rs.data.isPackage = body.isPackage
                return rs.data;
            }

            return false;
        },
    },
    mutations: {
        SET_PRODUCTS(state, products) {
            state.products = products;
        },
        SET_SORT(state, sort) {
            state.sortOption = sort;
        },
        APPEND_PRODUCTS(state, products) {
            for (const product of products)
                state.products.push(product);
        },
        SET_PAGINATION(state, pagination) {
            state.pagination = pagination;
        },
    }
}
