import {RentMySDK} from "rentmyjsdk";

export default {
    namespaced: true,
    state: () => ({
        config: null,
        countries: []
    }),
    getters: {
        currencyConfig: state => state.config?.currency_format || null,
        customer: state => state.config?.customer || null,
        schedulePickerConfig: state => {
            return {
                dateFormat: state.config?.date_format || 'YYYY-MM-DD ',
                showStartTime: state.config?.show_start_time || true,
                showEndDate:  state.config?.show_end_date || true,
                showEndTime: state.config?.show_end_time || true
            }
        },
        countries: state => state?.countries || true,
        paymentConfig: state => state.config?.payments || null,
    },
    actions: {
        async loadConfig({state, commit}, body = {}) {
            if (!state.config) {

                let loader = null
                if (body.container && body._v && body._v.$loading){
                    //set loader
                    loader = body._v.$loading.show({
                        container: body.container,
                        canCancel: true,
                        onCancel: body._v.onCancel,
                    });
                }

                const rs = await RentMySDK.request('config', 'settings',null, {type: 'store_config'})
                if (rs.response.status === 'OK') {
                    commit('SET_CONFIG', rs.data.config);
                }

                if (loader)
                    loader.hide()
            }
        },
        async loadCountries({state, commit}, body ={}){

            if (body.container && body._v && body._v.$loading){
                loader = body._v.$loading.show({
                    container: body.container,
                    canCancel: true,
                    onCancel: body._v.onCancel,
                });
            }

            const rs = await RentMySDK.request('config', 'countries',null)
            if (loader)
                loader.hide()

            if (rs.response.status === 'OK') {
                commit('SET_COUNTRIES', rs.data);
            }  
        }
    },
    mutations: {
        SET_CONFIG(state, config) {
            state.config = config;
        },
        SET_COUNTRIES(state, countries){
            state.countries = countries
        }
    }
}
