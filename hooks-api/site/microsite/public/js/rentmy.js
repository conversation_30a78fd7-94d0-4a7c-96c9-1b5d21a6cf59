
/*
    Copyright RentMy @2021.
    RentMy nano project
    Author: RentMy
 */

const RentMy = {
    globalSelector: '.rentmy-main-wrapper',
    content: null,
    template: {
        isInit: false,
        init: function () {
            this.isInit = true;
            let ref = this;
            let headerCart = localStorage.getItem('cart');
            if (headerCart) {
                headerCart = JSON.parse(headerCart);
                if (headerCart.total_quantity != undefined) {
                    if (headerCart.cart_items.length) {
                        $(".rm-cart-count-circle").show();
                        $(".rm-cart-count-circle").text(`${headerCart.cart_items.length}`);
                    } else {
                        $(".rm-cart-count-circle").hide();
                        $(".rm-cart-count-circle").text('');
                    }
                } else {
                    $(".rm-cart-count-circle").hide();
                    $(".rm-cart-count-circle").text('');
                }
            }else {
                $(".rm-cart-count-circle").hide();
                $(".rm-cart-count-circle").text('');
            }

            $(RentMy.globalSelector)
                .on('click', '.rentmy-mobile-menubar', function () {
                    $('body').find(".rentmy-nav-manu ").slideToggle(200);
                })
                .on('click', '.rentmy-modalclose, .rentmy-datetime-modal-overlay', function () {
                    $('.rentmy-datetime-modal-overlay').removeClass('is-open');
                })
                .on('click', '.rentmy-modal', function (e) {
                    e.stopPropagation();
                })
                .on('click', '.rentmy-search-body', function (){
                    console.log("Working")
                })
                .on('submit', '#search-form', function (event) {
                    event.preventDefault();
                    ref.searchProduct($('#search-product').val());
                })
                // .on('keyup', '#search-form', function (e) {
                //     console.log(e.keyCode)
                //     var keyCode = e.keyCode || e.which;
                //     if (keyCode === 13 || e.key === 'Enter') {
                //         e.preventDefault();
                //         ref.searchProduct($('#search-product').val());
                //         return false;
                //     }
                // })
                .on('keyup', "#subscription-email", function (e) {
                    let currentValue = $(this).val();
                    const btn = '#subscribe-btn'
                    if (currentValue.length > 6) {
                        let pattern = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,6})+$/;
                        if (!pattern.test(currentValue)) {
                            $(btn).prop('disabled', true);
                            $(btn).addClass('disabled');
                        } else {
                            $(btn).prop('disabled', false);
                            $(btn).removeClass('disabled');
                        }
                    } else {
                        $(btn).prop('disabled', true);
                        $(btn).addClass('disabled');
                    }
                })
                .on('click', '#subscribe-btn', function (e) {
                    e.preventDefault();
                    const email = $(RentMy.globalSelector).find("#subscription-email").val();
                    ref.subscribeSubmit(email);
                })
                .on('click', '.rentmy-search-bar, .rentmy-search-closebar', function () {
                    $(RentMy.globalSelector).find('.rentmy-search-body').toggleClass('rentmy-search-show');
                })
                .on('click', '.rentmy-payment-collaps-btn', function (e) {
                    e.preventDefault();
                    const $n = $(this);
                    $n.find('input[type=radio]').prop('checked', true);
                    if ($n.next().hasClass('show')) {
                        $n.next().removeClass('show');
                    } else {
                        $n.parent().parent().find('.rentmy-payment-collaps-content').removeClass('show');
                        $n.parent().parent().find('.rentmy-payment-collaps-content').slideUp(350);
                        $n.next().toggleClass('show');
                    }
                    $n.next().slideToggle(350);
                })
                .on('click', '.rentmy-fulfillment-collaps .rentmy-fulfillment-collaps-item .rentmy-fulfillment-collaps-btn', function (e) {
                    e.preventDefault();
                    const $n = $(this);
                    $n.find('input[type=radio]').prop('checked', true);
                    if ($n.next().hasClass('show')) {
                        $n.next().removeClass('show');
                    } else {
                        $n.parent().parent().find('.rentmy-fulfillment-collaps-content').removeClass('show');
                        $n.parent().parent().find('.rentmy-fulfillment-collaps-content').slideUp(350);
                        $n.next().toggleClass('show');
                    }
                    $n.next().slideToggle(350);
                })
                .on('click', '.rentmy-modalclose', function () {
                    $(RentMy.globalSelector).find('.rentmy-product-modal-overlay').removeClass('is-open');
                    $(RentMy.globalSelector).find('.rentmy-package-modal-overlay').removeClass('is-open');
                })
                .on('click', '.rentmy-modal-cartbtn', function () {
                    RentMy.products.addToCart()
                })

                .on('click', '.rentmy-custom-cartbtn-edit', function () {
                    const cart_item_id = $(this).data('cart_item_id');
                    const product_id = $(this).data('product_id');
                    RentMy.cartWidget.deleteCartItem(cart_item_id, product_id)
                    RentMy.products.addToCart()
                })

                .on('submit', '.setCrtDateTime', function (e) {
                    e.preventDefault();
                    const formData = $(this).serializeArray();
                    let params = {};
                    $.map(formData, function (n, i) {
                        params[n['name']] = n['value'];
                    });

                    if (params.start_time == undefined) {
                        params.start_time = '';//DEFAULT_START_TIME
                    }

                    if (params.rent_end == undefined) {
                        params.rent_end = '';
                    }

                    if (params.end_time == undefined) {
                        params.end_time = '';//DEFAULT_END_TIME
                    }

                    params.action_type = 'set_cart_date_time'
                    RentMy.cartWidget.submitSelectDatePopup(params)
                })

            //bind time list dropdown to all the class listed with .timelist
            $(RentMy.globalSelector).find('select.timelist')
                .html(['Select Time']
                    .concat(...RentMy.helpers.getTimeRanges(15, 'en'))
                    .map(time => `<option value="${time != 'Select Time' ? time : ''}">${time}</option>`));
        },
        searchProduct: function (searchParam) {
            window.location = $('#search-form').attr('action') + '&search=' + searchParam;
        },
        subscribeSubmit: async function (email) {
            const responseRaw = await RentMy.helpers.ajaxPost({
                email: email,
                action_type: 'newsletter_subscription'
            })

            try {
                const response = JSON.parse(responseRaw);
                if (response.error) {
                    RentMy.alert.errorAlert(response.error);
                } else {
                    RentMy.alert.successAlert(response.message);
                }
            } catch (e) {
            }
        }
    },
    cartWidget: {
        content: CONTENT,
        globalSelector: null,
        $elm: null,
        $sidebar: null,
        $datePopup: null,
        sidebar: null,
        cart: false,
        base_url: BASE_URL,
        showAvailableLabel: false,
        isInit: false,
        init: function () {

            this.isInit = true;
            //init elements accroding to rentmy global container
            this.globalSelector = RentMy.globalSelector;
            this.$elm = $(this.globalSelector).find('.rentmy-cartbar-launcher')
            this.$sidebar = $(this.globalSelector).find('.rentmy-cartsidebar-overlay')
            this.$datePopup = $(this.globalSelector).find('.rentmy-datetime-modal-overlay')
            this.sidebar = {
                $rent_start: $(this.globalSelector).find('.rentmy-cartsidebar-overlay').find('.rentmy-selected-date-leftside p'),
                $rent_end: $(this.globalSelector).find('.rentmy-cartsidebar-overlay').find('.rentmy-selected-date-rightside p'),
            };

            window.dispatchEvent((new CustomEvent('rentMy.cart.init', {
                bubbles: true,
                detail: {
                    cart : this,
                }
            })))

            cart = localStorage.getItem('cart')
            if (cart != undefined || cart != null) {
                try {
                    cart = JSON.parse(cart)

                    if (cart.cart_items != undefined && RENTMY_CART_TOKEN === cart.token) {

                        if($('.rentmy-custom-builder').length > 0){
                            this.setCustomBuilderMiniCartData(cart)
                        }else{
                            this.setValue(cart)
                            this.setCartDateTime()
                        }

                    } else {
                        this.removeCart()
                    }
                } catch (e) {
                    console.log(e)
                    this.removeCart()
                }
            }

            $(RentMy.globalSelector)
                .on('click', '.rentmy-cartbar-launcher', function () {
                    RentMy.cartWidget.openMiniCartSidebar();
                })
                .on('click', '.rentmy-remove-product', function () {
                    const trace = JSON.parse($(this).closest('li').attr('data-trace'))
                    RentMy.cartWidget.deleteCartItem(trace.cart_item_id, trace.product_id)
                })

                .on('click', '.rentmy-remove-cart-product', function () {
                    const trace = JSON.parse($(this).closest('tr').attr('data-trace'))
                    RentMy.cartWidget.deleteCartItem(trace.cart_item_id, trace.product_id, 'cart')
                })

                .on('click', '.rentmy-edit-product', function () {
                    const trace = JSON.parse($(this).closest('li').attr('data-trace'))
                    const product_id = trace.product_id;
                    window.location.href =  RentMy.cartWidget.base_url + '?page=details&uid='+trace.product_uid+"&item_id="+trace.cart_item_id
                })

                .on('click', '.rentmy-edit-cart-product', function () {
                    const trace = JSON.parse($(this).closest('tr').attr('data-trace'))
                    const product_id = trace.product_id;
                    window.location.href =  RentMy.cartWidget.base_url + '?page=details&uid='+trace.product_uid+"&item_id="+trace.cart_item_id
                })

                .on('click', '.rentmy-cart-modalclose', function () {
                    RentMy.cartWidget.closeMiniCartSidebar()
                })
                .on('click', '.rentmy-cartsidebar-overlay .rentmy-num-in span', async function () {

                    var $input = $(this).parents('.rentmy-number-block').find('input.rentmy-in-num');
                    //  const maxQty = parseInt($input.attr('max'));
                    let type = 'increase';

                    if ($(this).hasClass('rentmy-minus')) {
                        var count = parseInt($input.val()) - 1;
                        count = await count < 1 ? 0 : count;
                        if (count < 2) {
                            $(this).addClass('rentmy-dis');
                        } else {
                            $(this).removeClass('rentmy-dis');
                        }
                        if (count < 0) {
                            RentMy.alert.errorAlert('Minimum quantity exhausted!')
                            return;
                        }
                        type = 'decrease'
                        const trace = await JSON.parse($(this).closest('li').attr('data-trace'))
                        RentMy.cartWidget.update_quantity(type, trace);
                    } else {
                        var count = parseInt($input.val()) + 1
                        if (count > 1) {
                            $(this).parents('.rentmy-num-block').find(('.rentmy-minus')).removeClass('rentmy-dis');
                        }

                        const trace = await JSON.parse($(this).closest('li').attr('data-trace'))
                        RentMy.cartWidget.update_quantity(type, trace);
                    }

                })
                .on('click', '.rentmy-selected-date', function () {
                    RentMy.cartWidget.openSelectDate();
                    return false;
                })
                .on('click', '.close-datetime-popup', function () {
                    RentMy.cartWidget.closeDatetimePopup();
                })
        },

        addToCart: async function (data, product = false) {
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
               let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent((new CustomEvent('rentMy.cart.ItemAdded', {
                    bubbles: true,
                    detail: {
                        cart : this,
                        request: data,
                        response: response
                    }
                })))

                if (response.status != undefined && response.status === 'OK') {
                    if($('.rentmy-custom-builder').length > 0){
                        this.setCustomBuilderMiniCartData(response.data)
                    }else {
                        this.setValue(response.data)
                    }
                }
                if (response.status != undefined && response.status === 'NOK') {
                    RentMy.alert.errorAlert(response.result.error)
                }

                product.modalClose()
            } catch (e) {
            }
        },

        update_quantity: async function (type, data) {
            let increment = 0;
            if (type == 'increase') {
                increment = 1;
            }
            data.action_type = 'cart_quantity_update'
            data.increment = increment

            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent((new CustomEvent('rentMy.cart.quantityUpdate', {
                    bubbles: true,
                    detail: {
                        cart : this,
                        request: data,
                        response: response
                    }
                })))

                if (response.status != undefined && response.status === 'OK') {
                    if($('.rentmy-custom-builder').length > 0){
                        this.setCustomBuilderMiniCartData(response.data)
                    }else {
                        this.setValue(response.data)
                    }
                }
                if (response.result != undefined && response.result.error != undefined) {
                    RentMy.alert.errorAlert(response.result.error)
                }
            } catch (e) {
            }
        },

        deleteCartItem: async function (item_id, product_id, source='') {
            const param = {
                'action_type': 'delete_cart_item',
                'cart_item_id': item_id,
                'product_id': product_id
            }
            const responseRaw = await RentMy.helpers.ajaxPost(param)

            try {
                let response = JSON.parse(responseRaw)

                //define a custom event
                window.dispatchEvent((new CustomEvent('rentMy.cart.itemDelete', {
                    bubbles: true,
                    detail: {
                        cart : this,
                        request: param,
                        response: response
                    }
                })))

                if (response.status != undefined && response.status === 'OK') {
                    if($('.rentmy-custom-builder').length > 0){
                        this.setCustomBuilderMiniCartData(response.data)
                    }else {
                        this.setValue(response.data)
                    }
                    if (source == 'cart'){
                        location.reload();
                    }
                }
            } catch (e) {
            }

        },
        setCustomBuilderMiniCartData: function (cart){
            console.log("crt data")
            console.log(cart)
            if (cart == undefined)
                return;
            cart.enableCheckoutButton = true
            let errorItems = [];
            if (cart.errors != undefined) {
                errorItems = [...cart.errors.ids]
                RentMy.alert.errorAlert(cart.errors.message)
                delete cart.errors;
                cart.enableCheckoutButton = false
            }
            this.cart = cart;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.cart.dataBind', {
                bubbles: true,
                detail: {
                    cart : this,
                    data: this.cart
                }
            }))

            localStorage.setItem('cart', JSON.stringify(cart))
            if (RentMy.checkout.isInit) {
                try {
                    const rowResponse = JSON.parse(cart.response);
                    RentMy.checkout.bindOrderData(rowResponse)
                } catch (e) {
                }
            }

            if (cart.total_quantity != undefined){
                let itemCountHtml = '';
                if(cart.cart_items.length > 1) itemCountHtml = this.content.mini_cart.lbl_items;
                else itemCountHtml = this.content.mini_cart.lbl_item;
                this.$elm.find('.rentmy-cartbar-launcher-summary #item-count').html(`${cart.cart_items.length || 0} ${itemCountHtml}`);

                if(cart.cart_items.length){
                    $(".rm-cart-count-circle").show();
                    $(".rm-cart-count-circle").text(`${cart.cart_items.length}`);
                } else {
                    $(".rm-cart-count-circle").hide();
                    $(".rm-cart-count-circle").text();
                }
            }
            if (cart.total != undefined) {
                this.$elm.find('.rentmy-cartbar-launcher-summary #cart-total').html(cart.total)
                this.$sidebar.find('.rentmy-cart-sidebar-summary .rentmy-amount').html(cart.total)
            }
            //enable and disable checkout page btn
            if (!cart.enableCheckoutButton)
                this.$sidebar.find('a.rentmy-checkout-btn').addClass('disabled')
            else
                this.$sidebar.find('a.rentmy-checkout-btn').removeClass('disabled')

            if (cart.cart_items != undefined) {
                let items = '';

                if (cart.cart_items.length > 0) {
                    response_data = JSON.parse(cart.response);

                    for (const item of response_data.cart_items) {
                        //check if item has not availabe error
                        cart_item_data =  cart.cart_items[cart.cart_items.findIndex((x)=>x.id==item.id)];
                        let validateClass = 'rentmy-list-item'
                        for (const id of errorItems) {
                            if (item.id = !undefined && id == item.id)
                                validateClass += ' rentmy-list-empty-item'
                        }

                        items += `<li  data-trace='${cart_item_data.trace}'>
                            <div class="rentmy-list-item">
<!--                                <img class="rentmy-product-image" src="${cart_item_data.image}" />-->
                                <div class="rentmy-cart-info">
                                    <div class="rentmy-product-name rentmy-large">${cart_item_data.name} ${'('+item.variant_chain_name+')'}</div>
                                    <ul class="rentmy-modal-optionlist">`;

                                    item.products.forEach(prod=>{
                                        items += `<span>${prod.name} :`;
                                        let variant_chain = prod.variant_chain.split(',');
                                        variant_chain.forEach(function (variant){
                                            let variant_spilted = variant.split(':');
                                            items += `${variant_spilted[1]},`;
                                        })
                                        items += `</span><br>`;
                                    });

                                    items += `</ul>`;
                                    items += `<span>${this.calculateAddonsPriceWithItemPrice(item)}</span>`;
                                    items += `<a class="rentmy-edit-product"><i class="bx bx-pencil"></i></a>`;
                                   items += `<a class="rentmy-remove-product"><i class="lni lni-close"></i></a>
                                </div>
                            </div>
                        </li>`
                    }
                }

                this.$sidebar.find('.rentmy-cart-sidebar-productlist ul').html(items)
                if (cart.response){
                    response_data = JSON.parse(cart.response);
                    let order_summary_html = `<table class="table">
                        <tbody>
                        <tr>
                            <td>${this.content.cart.lbl_th_subtotal}</td>
                            <td class="text-right">
                                <span>${RentMy.helpers.currencyFormat(response_data.sub_total??0)}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>${this.content.cart.lbl_shipping}</td>
                            <td class="text-right"><span> ${RentMy.helpers.currencyFormat(response_data.delivery_charge??0)} </span></td>
                        </tr>
                        <tr>
                            <td>${this.content.cart.lbl_discount}</td>
                            <td class="text-right"><span> ${RentMy.helpers.currencyFormat(response_data.total_discount??0)}</span></td>
                        </tr>
                        <tr>
                            <td>${this.content.cart.lbl_tax}</td>
                            <td class="text-right"><span> ${RentMy.helpers.currencyFormat(0)}</span></td>
                        </tr>
                        </tbody>
                    </table>`;
                    this.$sidebar.find('.rentmy-cart-order-summery').html(order_summary_html)
                    this.$sidebar.find('.rentmy-cart-sidebar-summary .rentmy-amount').html(RentMy.helpers.currencyFormat(response_data.total??0))
                }


                this.checkAndUpdateAvailability()
                this.$elm.addClass('rentmy-cartbar-launcher-hover').delay(4000).queue(function () {
                    $(this).removeClass("rentmy-cartbar-launcher-hover").dequeue();
                });
            }
        },
        calculateAddonsPriceWithItemPrice(item){
            let price = item.price;
            item.products.forEach(variant=>{
                if (variant.sub_total != undefined && variant.sub_total != ''){
                    price += variant.sub_total
                }
            })
            return RentMy.helpers.currencyFormat(price)
        },

        setValue: function (cart) {
            if (cart == undefined)
                return;
            cart.enableCheckoutButton = true
            let errorItems = [];
            if (cart.errors != undefined) {
                errorItems = [...cart.errors.ids]
                RentMy.alert.errorAlert(cart.errors.message)
                delete cart.errors;
                cart.enableCheckoutButton = false
            }
            this.cart = cart;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.cart.dataBind', {
                bubbles: true,
                detail: {
                    cart : this,
                    data: this.cart
                }
            }))

            localStorage.setItem('cart', JSON.stringify(cart))
            if (RentMy.checkout.isInit) {
                try {
                    const rowResponse = JSON.parse(cart.response);
                    RentMy.checkout.bindOrderData(rowResponse)
                } catch (e) {
                }
            }

            if (cart.total_quantity != undefined){
                let itemCountHtml = '';
                if(cart.cart_items.length > 1) itemCountHtml = this.content.mini_cart.lbl_items;
                else itemCountHtml = this.content.mini_cart.lbl_item;
                this.$elm.find('.rentmy-cartbar-launcher-summary #item-count').html(`${cart.cart_items.length || 0} ${itemCountHtml}`);

                if(cart.cart_items.length){
                    $(".rm-cart-count-circle").show();
                    $(".rm-cart-count-circle").text(`${cart.cart_items.length}`);
                } else {
                    $(".rm-cart-count-circle").hide();
                    $(".rm-cart-count-circle").text();
                }
            }
            if (cart.total != undefined) {
                this.$elm.find('.rentmy-cartbar-launcher-summary #cart-total').html(cart.total)
                this.$sidebar.find('.rentmy-cart-sidebar-summary .rentmy-amount').html(cart.total)
            }

            this.showAvailableLabel = false;
            let rentmyDateLabel = '<span>'
            if (cart.rent_start != undefined && cart.rent_end != false) {
                this.showAvailableLabel = true;
                this.sidebar.$rent_start.html(cart.rent_start.date + ', ' + cart.rent_start.time).attr('data-date', JSON.stringify(cart.rent_start))
                rentmyDateLabel += ` <strong>${cart.rent_start.date}</strong> <small>${cart.rent_start.time}</small>`
            }
            if (cart.rent_end != false && cart.rent_end != undefined) {
                this.sidebar.$rent_end.html(cart.rent_end.date + ', ' + cart.rent_end.time).attr('data-date', JSON.stringify(cart.rent_end))
                rentmyDateLabel += ` - <strong>${cart.rent_end.date}</strong><small>${cart.rent_end.time}</small>`
            }
            rentmyDateLabel += '</span>'
            this.$elm.find('.rentmy-dates').html(rentmyDateLabel)

            if (cart.rent_start == false) {
                this.resetAllDateTimes();
            }

            //enable and disable checkout page btn
            if (!cart.enableCheckoutButton)
                this.$sidebar.find('a.rentmy-checkout-btn').addClass('disabled')
            else
                this.$sidebar.find('a.rentmy-checkout-btn').removeClass('disabled')

            if (cart.cart_items != undefined) {
                let items = '';
                if (cart.cart_items.length > 0) {
                    for (const item of cart.cart_items) {
                        //check if item has not availabe error
                        let validateClass = 'rentmy-list-item'
                        for (const id of errorItems) {
                            if (item.id = !undefined && id == item.id)
                                validateClass += ' rentmy-list-empty-item'
                        }

                        items += `<li data-trace='${item.trace}'>
                            <div data-id="${item.id}" class="${validateClass}">
                                <img class="rentmy-product-image" src="${item.image}" />
                                <div class="rentmy-cart-line">
                                    <div class="rentmy-product-name rentmy-large">${item.name}</div>
                                    <div class="rentmy-modal-quantity">
                                        <div class="rentmy-number-block">
                                            <div class="rentmy-num-in">
                                                <span class="rentmy-minus rentmy-dis">-</span>`

                        if (this.showAvailableLabel)
                            items += `<input type="text" class="rentmy-in-num" value="${item.quantity}" max="${item.available}" readonly="">`
                        else
                            items += `<input type="text" class="rentmy-in-num" value="${item.quantity}" readonly="">`

                        items += `<span class="rentmy-plus">+</span>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="rentmy-price">${RentMy.helpers.currencyFormat(item.price)}</span>`

                        if (this.showAvailableLabel)
                            items += `<p>${this.content.mini_cart.lbl_available}: <span class="rentmy-available">${item.available}</span></p>`

                        items += `<button class="rentmy-remove-product"><i class="lni lni-close"></i></button>
                                </div>
                            </div>
                        </li>`
                    }
                }

                this.$sidebar.find('.rentmy-cart-sidebar-lines ul').html(items)
                this.checkAndUpdateAvailability()
                this.$elm.addClass('rentmy-cartbar-launcher-hover').delay(4000).queue(function () {
                    $(this).removeClass("rentmy-cartbar-launcher-hover").dequeue();
                });
            }
        },

        //open date time select popup
        openSelectDate: function () {
            this.setCartDateTime();
            //open popup
            this.$datePopup.addClass('is-open')
        },

        //submit date time select popup
        submitSelectDatePopup: async function (prams) {
            const responseRaw = await RentMy.helpers.ajaxPost(prams)
            try {
                let response = JSON.parse(responseRaw)

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.cart.changeRentalDateTime', {
                    bubbles: true,
                    detail: {
                        cart : this,
                        request: prams,
                        response: response
                    }
                }))

                if (response.status != undefined && response.status !== 'NOK') {
                    if($('.rentmy-custom-builder').length > 0){
                        this.setCustomBuilderMiniCartData(response.data)
                    }else {
                        this.setValue(response.data)
                        this.setCartDateTime()
                    }

                } else {
                    if (response.result.message != undefined) {
                        RentMy.alert.errorAlert(response.result.message)
                    }
                    if (response.cartReset == undefined) {
                        this.resetAllDateTimes()
                    }
                }
            } catch (e) {
            }
            this.closeDatetimePopup()
        },

        openMiniCartSidebar: async function () {
            await this.checkAndUpdateAvailability()
            await this.$sidebar.addClass('is-open');
        },

        closeMiniCartSidebar: function () {
            this.$sidebar.removeClass('is-open');
        },

        checkAndUpdateAvailability: async function () {
            if (this.showAvailableLabel && (this.cart.cart_items != undefined && this.cart.cart_items.length > 0)) {
                const responseRaw = await RentMy.helpers.ajaxPost({action_type: 'cart_available_counts'})
                try {
                    let response = JSON.parse(responseRaw)

                    //define a custom event
                    window.dispatchEvent(new CustomEvent('rentMy.cart.checkAvailability', {
                        bubbles: true,
                        detail: {
                            cart : this,
                            response: response
                        }
                    }))

                    if (response.status != undefined && response.status === 'OK') {
                        const $elmItems = this.$sidebar.find('.rentmy-list-item');
                        for (const item of response.result.data) {
                            $.each($elmItems, (index, elm) => {
                                const $elm = $(elm)
                                if ($elm.attr('data-id') == item.cart_item_id) {
                                    $elm.find('.rentmy-available').html(item.available)
                                }
                            })
                        }
                    }
                } catch (e) {
                }
            }
        },

        closeDatetimePopup: function () {
            this.$datePopup.removeClass('is-open')
        },

        //set date time to frontend
        setCartDateTime: function () {
            if (this.cart) {
                if (this.cart.rent_start == false) {
                    this.resetAllDateTimes()
                    return
                }

                const cartRent = this.sidebar.$rent_start.attr('data-date');
                if (cartRent == undefined)
                    return false

                //get cart dates times
                const cartRentStart = JSON.parse(cartRent);
                const cartRentEnd = JSON.parse(this.sidebar.$rent_end.attr('data-date'));

                //set meta
                $('meta[name=rentmy_rent_start]').attr('content', cartRentStart.dateJS)

                //set dates
                const dates = this.$datePopup.find('.rentmy-datetime-input input[type="date"]')
                const $popupRentStartDate = $(dates[0]);
                const $popupRentEndDate = $(dates[1]);

                $popupRentStartDate.val(cartRentStart.dateJS)
                $popupRentEndDate.val(cartRentEnd.dateJS)

                //set times
                const $rentStartTime = this.$datePopup.find('select.timelist[name="start_time"]')
                const $rentEndTime = this.$datePopup.find('select.timelist[name="end_time"]')

                $rentStartTime.find(`option[value="${cartRentStart.time.replace(/^(?:00:)?0?/, '')}"]`).prop('selected', true)
                $rentEndTime.find(`option[value="${cartRentEnd.time.replace(/^(?:00:)?0?/, '')}"]`).prop('selected', true)

                //product page filter inputs
                const $filtersDiv = $(RentMy.globalSelector).find('.rentmy-product-datetime');
                const $datesElms = $filtersDiv.find('input[type="date"]');
                const $pageFilterRentStartDate = $($datesElms[0]);
                const $pageFilterRentEndDate = $($datesElms[1]);
                $pageFilterRentStartDate.val(cartRentStart.dateJS)
                $pageFilterRentEndDate.val(cartRentEnd.dateJS)

                const $pageFilterRentStartTime = $filtersDiv.find('select.timelist[name="start_time"]')
                const $pageFilterRentEndTime = $filtersDiv.find('select.timelist[name="end_time"]')

                $pageFilterRentStartTime.find(`option[value="${cartRentStart.time.replace(/^(?:00:)?0?/, '')}"]`).prop('selected', true)
                $pageFilterRentEndTime.find(`option[value="${cartRentEnd.time.replace(/^(?:00:)?0?/, '')}"]`).prop('selected', true)


                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.cart.setDateTime', {
                    bubbles: true,
                    detail: {
                        cart : this,
                        cartRentStart: cartRentStart,
                        cartRentEnd: cartRentEnd,
                        elmPopupDate: dates,
                        elmPageDate: $filtersDiv,
                    }
                }))
            }
        },

        resetAllDateTimes: function () {
            this.sidebar.$rent_start.html(`${this.content.mini_cart.title_rental_schedule}`).removeAttr('data-date')
            this.sidebar.$rent_end.html('').removeAttr('data-date')
            this.$elm.find('.rentmy-dates').html(`<span class="rentmy-selected-date">${this.content.mini_cart.title_rental_schedule}</span>`)

            //reset dates
            const dates = this.$datePopup.find('.rentmy-datetime-input input[type="date"]')
            const $popupRentStartDate = $(dates[0]);
            const $popupRentEndDate = $(dates[1]);

            $popupRentStartDate.val('')
            $popupRentEndDate.val('')

            //reset times
            const $rentStartTime = this.$datePopup.find('select.timelist[name="start_time"]')
            const $rentEndTime = this.$datePopup.find('select.timelist[name="end_time"]')

            $rentStartTime.find(`option[value=""]`).prop('selected', true)
            $rentEndTime.find(`option[value=""]`).prop('selected', true)

            //reset product page filter inputs
            const $filtersDiv = $('body').find('.rentmy-product-datetime');
            const $datesElms = $filtersDiv.find('input[type="date"]');
            const $pageFilterRentStartDate = $($datesElms[0]);
            const $pageFilterRentEndDate = $($datesElms[1]);
            $pageFilterRentStartDate.val('')
            $pageFilterRentEndDate.val('')

            const $pageFilterRentStartTime = $filtersDiv.find('select.timelist[name="start_time"]')
            const $pageFilterRentEndTime = $filtersDiv.find('select.timelist[name="end_time"]')

            $pageFilterRentStartTime.find(`option[value=""]`).prop('selected', true)
            $pageFilterRentEndTime.find(`option[value=""]`).prop('selected', true)

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.cart.resetDateTime', {
                bubbles: true,
                detail: {
                    cart : this,
                    elmPopupDate: dates,
                    elmPageDate: $filtersDiv,
                }
            }))
        },

        removeCart: function () {
            let cart = localStorage.getItem('cart')
            localStorage.removeItem('cart')
            try {
                cart = JSON.parse(cart);
            }catch (e){}

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.cart.localStorage.delete', {
                bubbles: true,
                detail: {
                    cart : this,
                    data: cart
                }
            }))
        },
    },
    helpers: {
        getTimeRanges: function (interval, language = window.navigator.language, startFrom = '8:00 AM') {
            const ranges = [];
            const date = new Date();
            const format = {
                hour: 'numeric',
                minute: 'numeric',
            };

            for (let minutes = 0; minutes < 24 * 60; minutes = minutes + interval) {
                date.setHours(0);
                date.setMinutes(minutes);
                ranges.push(date.toLocaleTimeString(language, format));
            }

            if (startFrom !== '') {
                let times = []
                ranges.forEach((time, index) => {
                    if (time === startFrom) {
                        for (let start = index; start < ranges.length; start++)
                            times.push(ranges[start])

                        for (let rest = 0; rest < index; rest++)
                            times.push(ranges[rest])
                    }
                })
                return times;
            }
            return ranges;
        },
        currencyFormat(amount) {
            amount = parseFloat(amount).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')
            const currencyFormat = STORE_CONFIG.currency_format
            if(currencyFormat == undefined){
                return '$'+amount;
            }
            if (currencyFormat && currencyFormat.pre) {
                return `<span class="pre">${currencyFormat.symbol}</span><span class="amount">${amount}</span>`
            }
            return `<span class="amount">${amount}</span><span class="pre">${currencyFormat.symbol}</span>`
        },
        insertURLParam: function (key, value) {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get(key) != undefined && urlParams.get(key).length > 1) {
                urlParams.delete(key)
            }
            urlParams.set(key, value);
            window.location.search = urlParams;
        },
        ajaxPost: async function (args, beforePostCallback, afterPostCallback) {
            //callback init before server request sent
            if (typeof beforePostCallback === 'function') {
                beforePostCallback(args);
            }

            let result;
            try {
                result = await $.post('', args)
                    .done(function (response) {
                        try {
                            response = JSON.parse(response);

                            //define a custom event
                            window.dispatchEvent(new CustomEvent('rentMy.request.details', {
                                bubbles: true,
                                detail: {
                                    ajax : this,
                                    action: args.action_type,
                                    request: args,
                                    response: response
                                }
                            }))

                            //callback after server response success
                            if (typeof afterPostCallback === 'function') {
                                afterPostCallback(args, response);
                            }

                            if (response.status === 'NOK') {
                                if (response.result && typeof response.result.error === 'string')
                                    RentMy.alert.errorAlert(response.result.error);
                            }

                        } catch (e) {
                            console.log(e)
                            console.log(response)
                        }
                    });
                return result;
            } catch (error) {
                console.error(error);
            }
        },
        initMultipleImagePopup: function () {
            const activeImage = document.querySelector(".rentmy-product-view-image .rentmy-product-viewimage-active");
            const productImages = document.querySelectorAll(".rentmy-product-multipleimage img");

            function changeImage(e) {
                activeImage.src = e.target.src;
            }

            productImages.forEach((image) => image.addEventListener("click", changeImage));

            $(RentMy.globalSelector).on('click', '.rentmy-product-multipleimage .rentmy-product-thumb-item', function () {
                $(RentMy.globalSelector).find('.rentmy-product-thumb-item').removeClass("rentmy-product-thumb-active");
                $(this).addClass("rentmy-product-thumb-active");
            });
        }
    },
    alert: {
        $elm: null,
        $parentContainer: null,
        html: null,
        type: null,
        icons: {
            success: 'lni lni-checkmark-circle',
            error: 'lni lni-warning'
        },
        setContent: function (html) {
            this.html = html
            this.$elm = $(RentMy.globalSelector).find('.rentmy-alert-message');
            this.$parentContainer = $(RentMy.globalSelector).find('.rentmy-alert-message div').first();
            this.$elm.find('.rentmy-alert-message-text').html(html)
        },
        error: function () {
            //remove classes
            this.resetClasses()
            this.type = 'error'
            //add error classes
            this.$parentContainer.addClass('rentmy-alert-error-message')
            this.$elm.find('.rentmy-alert-message-icon i').addClass(this.icons.error)
        },
        success: function () {
            //remove classes
            this.resetClasses()
            this.type = 'success'
            //add success classes
            this.$parentContainer.addClass('rentmy-alert-success-message')
            this.$elm.find('i').addClass(this.icons.success)
        },
        resetClasses: function () {
            this.$parentContainer.removeClass()
            this.$elm.find('i').removeClass()
        },
        open: function () {
            this.$elm.removeClass('rentmy-hide').delay(4000).queue(function () {
                $(this).addClass("rentmy-hide").dequeue();
            });

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.alert.open', {
                bubbles: true,
                detail: {
                    alert : this,
                    html: this.html,
                    type: this.type
                }
            }))
        },
        errorAlert: function (html) {
            this.setContent(html)
            this.error()
            this.open()
        },
        successAlert: function (html) {
            this.setContent(html)
            this.success()
            this.open()
        }
    },
    isInit: false,
    init: function(page) {
        this.content = CONTENT;
        this.isInit = true;
        //Define prototypes
        Date.prototype.addHours = function (h) {
            this.setTime(this.getTime() + (h * 60 * 60 * 1000));
            return this;
        }
        Date.prototype.mysqlFormat = function () {
            var year, month, day;
            year = String(this.getFullYear());
            month = String(this.getMonth() + 1);
            if (month.length == 1) {
                month = "0" + month;
            }
            day = String(this.getDate());
            if (day.length == 1) {
                day = "0" + day;
            }
            return year + "-" + month + "-" + day;
        }
        Date.prototype.showTime = function () {
            var d = this;
            d.setHours(d.getHours() + 2); // offset from local time
            var h = (d.getHours() % 12) || 12; // show midnight & noon as 12
            return (
                (h < 10 ? '0' : '') + h +
                (d.getMinutes() < 10 ? ':0' : ':') + d.getMinutes() +
                // optional seconds display
                // ( d.getSeconds() < 10 ? ':0' : ':') + d.getSeconds() +
                (d.getHours() < 12 ? ' AM' : ' PM')
            );
        }

        $(async function () {
            if (typeof page !== 'undefined') {
                RentMy.template.init();
                switch (page.toLowerCase()) {
                    case 'home':
                        RentMy.home.init()
                        break;
                    case 'products':
                    case 'products-list':
                    case 'products_list':
                    case 'details':
                    case 'package_details':
                        RentMy.products.init();
                        break;
                    case 'categories':
                        RentMy.categories.init();
                        break;
                    case 'contact':
                        RentMy.contact.init();
                        break;
                    case 'checkout':
                        await RentMy.checkout.init();
                        break;
                    case 'cart':
                        RentMy.cart.init();
                        break;
                    default:
                        break;
                }
                RentMy.cartWidget.init();

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.init', {
                    bubbles: true,
                    detail: {
                        rentMy : this,
                        page: PAGE
                    }
                }))
            }
        });
    },

    //Pages Start
    home: {
        isInit: false,
        init: function () {
            this.isInit = true;
            //define a custom event
            RentMy.products.init();
            window.dispatchEvent(new CustomEvent('rentMy.page.home.init', {
                bubbles: true,
                detail: {
                    home : this,
                }
            }))

        }
    },
    products: {
        $modal: null,  //product / package type modal is triggered
        btnData: null, //products page modal page button may have some data regarding refereed product/package.
        isPackage: 0,   // if triggered modal is 1 for package type or 0 for product type.
        data: null,     //response data from server
        rental_type: 'buy',
        rent_start: null,
        rent_end: null,
        term: 1,
        required_addons: [],
        // content: CONTENT != undefined ? SITE_SPECIFIC : false,
        content: CONTENT,
        variants_products_id: '',

        isInit: false,
        init: function () {
            this.isInit = true;
            // for product details page

            if ($('.rentmy-product-details-wrapper').length > 0){
                $elm = $(".product-info")
                this.btnData = JSON.parse($elm.attr('data-product'))
                this.$modal = $('.rentmy-product-details-wrapper');
                this.requestProductDetails()
            }

            if ($('.rentmy-package-details-wrapper').length > 0){
                $elm = $(".product-info")
                this.btnData = JSON.parse($elm.attr('data-product'))
                this.$modal = $('.rentmy-package-details-wrapper');
                this.isPackage = 1;
                this.requestProductDetails()
            }

            //end details page
            $(RentMy.globalSelector)

                .on('change', 'input[name="rentmy-package"]', function () {
                    const packageType = $('input[name="rentmy-package"]:checked');
                    let fixedPrice = false;
                    if (packageType.attr('data-price') != undefined)
                        fixedPrice = packageType.attr('data-price')
                    RentMy.products.changePriceType(packageType.val(), fixedPrice)
                })
                .on('change', '.variants', function () {
                    RentMy.products.changeVariant($(this))
                })
                .on('change', '.rentmy-product-variants', function () {
                    RentMy.products.changeProductVariant($(this));
                })
                .on('click', '.product-cart-btn', function () {

                    const modal = $(RentMy.globalSelector).find('.rentmy-product-modal-overlay');
                    RentMy.products.modalTrigger(modal, $(this))
                })
                .on('click', '.package-cart-btn', function () {

                    const modal = $(RentMy.globalSelector).find('.rentmy-package-modal-overlay');
                    RentMy.products.modalTrigger(modal, $(this), 1)
                })
                .on('click', '.rentmy-addcart-btn', function () {
                    const _product = $(this).attr('data-product');
                    RentMy.products.addToCart(JSON.parse(_product))
                })
                .on('click', '.rentmy-product-modal-overlay', function () {
                    $(RentMy.globalSelector).find('.rentmy-product-modal-overlay').removeClass('is-open');
                })
                .on('click', '.rentmy-package-modal-overlay', function () {
                    $(RentMy.globalSelector).find('.rentmy-package-modal-overlay').removeClass('is-open');
                })
                .on('click', '.rentmy-modal', function (e) {
                    e.stopPropagation();
                })
                .on('click', '.rentmy-product-modal-overlay .rentmy-num-in span, .rentmy-package-modal-overlay .rentmy-num-in span, .rentmy-product-details-wrapper .rentmy-num-in span, .rentmy-package-details-wrapper .rentmy-num-in span', function () {
                    var $input = $(this).parents('.rentmy-number-block').find('input.rentmy-in-num');
                    const maxQty = parseInt($input.attr('max'));
                    if ($(this).hasClass('rentmy-minus')) {
                        var count = parseFloat($input.val()) - 1;
                        count = count < 1 ? 1 : count;
                        if (count < 2) {
                            $(this).addClass('rentmy-dis');
                        } else {
                            $(this).removeClass('rentmy-dis');
                        }
                        if (count < 0) {
                            RentMy.alert.errorAlert('Minimum quantity exhausted!')
                            return;
                        }
                        $input.val(count);
                    } else {
                        var count = parseFloat($input.val()) + 1
                        if (count > maxQty) {
                            RentMy.alert.errorAlert('Maximum available quantity has been exhausted!')
                            return;
                        }
                        $input.val(count);
                        if (count > 1) {
                            $(this).parents('.rentmy-num-block').find(('.rentmy-minus')).removeClass('rentmy-dis');
                        }
                    }

                    $input.change();

                    return false;
                })
                .on('change', '.rentmy-filter-checkbox-list input[type=checkbox]', function () {
                    let tags = [];
                    $(RentMy.globalSelector).find('.rentmy-filter-checkbox-list input[type=checkbox]').each(function () {
                        let value = $(this).val();
                        if ($(this).prop('checked')) {
                            tags.push(value);
                        }
                    });

                    RentMy.helpers.insertURLParam('tag', tags.toString());
                })
                .on('change', '.rentmy-price-type-filter input[type=radio]', function () {
                    let type = $(this).val()
                    RentMy.helpers.insertURLParam('price_type', type);
                })
                .on('change', '.rentmy-sort-by select', function () {
                    RentMy.helpers.insertURLParam('sort', $(this).val())
                })

                .on('change', '.rentmy-product-option select', function (){
                    let options = [];
                    $('.rentmy-product-option select').each(function (){
                        if ($(this).val() != ''){
                            options.push(JSON.parse($(this).val()));
                        }
                    })
                    RentMy.products.getPriceValue(options);
                })

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.products.init', {
                bubbles: true,
                detail: {
                    products : this,
                }
            }))
        },
        getPriceValue: async function(custom_fields=[]){
            console.log("getPriceValue")
            console.log(this.data)
            let params = {
                action_type: 'get_price_value',
                product_id: this.data.client_specific_id,
                quantity: $(".rentmy-modal-quantity input").val(),
                variants_products_id: this.variants_products_id,
                rent_start: this.data.rent_start,
                rent_end: this.data.rent_end,
            };

            if (custom_fields.length > 0){
                params['custom_fields'] = custom_fields
            }
            const responseRaw = await RentMy.helpers.ajaxPost(params)
            try {
                let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.page.products.getPriceValue', {
                    bubbles: true,
                    detail: {
                        products : this,
                        request: params,
                        response: response
                    }
                }))

                if (response.status === 'OK') {
                    this.setModalData({
                        priceString: RentMy.helpers.currencyFormat(response.result.data)
                    });
                }
            } catch (e) {
            }
        },
        getQuantity: function () {
            return this.$modal.find('input.rentmy-in-num').val();
        },

        format_prices: function (data) {
            if (data.length > 0) {
                var prices = data[0];
                var obj = {
                    buy: {type: false, price: 0, id: null},
                    rent: {type: false, price: []}
                };
                const rent = ["hourly", "daily", "weekly", "monthly", "rent"];
                if (prices.base.price > 0) {
                    obj.buy["type"] = true;
                    obj.buy["price"] = prices.base.price;
                    obj.buy["id"] = prices.base.id;
                    obj.buy["html"] = prices.base.html;
                }
                let ren = [];
                const rentPrices = data[0];

                if (rentPrices.fixed) {
                    obj.rent["price"].push({
                        type: "",
                        price: rentPrices.fixed.price,
                        id: rentPrices.fixed.id,
                        label: "",
                        html: rentPrices.fixed.html,
                        rent_start: rentPrices.fixed.rent_start,
                        rent_end: rentPrices.fixed.rent_end
                    });
                } else {
                    for (let c in rentPrices) {
                        for (let i = 0; i < rentPrices[c].length; i++) {
                            rentPrices[c][i]["type"] = rentPrices[c][i].label;
                            obj.rent["price"].push(rentPrices[c][i]);
                        }
                    }
                }
                if (obj.rent["price"].length > 0) obj.rent["type"] = true;
                return obj;
            }
            return data;
        },

        //when change price type
        changePriceType: function (priceType, fixed = false) {
            //set type
            this.rental_type = priceType
            let prices
            if (this.data.prices != undefined)
                prices = this.format_prices(this.data.prices)
            else if (this.data.price != undefined)
                prices = this.format_prices(this.data.price)
            if (priceType === 'rent') {
                if (prices.rent.price != undefined) {
                    let priceString = `${this.content.products.lbl_product_list_starting_at} <b>${RentMy.helpers.currencyFormat(prices.rent.price[0].price)}</b> ${this.content.products.lbl_product_list_per} ${prices.rent.price[0].duration} ${prices.rent.price[0].label}`;
                    if (fixed != false)
                        priceString = `<b>${RentMy.helpers.currencyFormat(fixed)}</b>`;
                    this.setModalData({
                        priceString: priceString,
                        available: this.data.available,
                        price_type: priceType,
                        formated_prices: prices,
                    })
                }
            } else if (priceType === 'buy') {
                if (prices.buy.price != undefined) {
                    this.setModalData({
                        priceString: `${this.content.products.lbl_product_list_buy_now_for} <b>${RentMy.helpers.currencyFormat(prices.buy.price)}</b>`,
                        availableForSale: this.data.available_for_sale,
                        price_type: priceType,
                        formated_prices: prices,
                    })
                }
            }
        },

        //when change package variant option
        changeVariant: async function (data) {
            const v = $(RentMy.globalSelector).find('.price-options input[name="rentmy-package"]:checked').val()

            if (v === 'rent' && (this.rent_start == '' || this.rent_start == null)) {
                return;
            }
            let params = {
                action_type: 'change_package_variants',
                product_id: data.attr('data-package_id'),
                pacakge_uid: data.attr('data-package_uid'),
                rent_type: v
            };
            let products = [];
            let variants = [];
            $(RentMy.globalSelector).find('.rentmy-package-single-list select').each(function (element) {
                let package_item = {
                    product_id: $(this).data('product'),
                    variant_id: $(this).val()
                }
                products.push(package_item);
                variants.push($(this).val());
            });
            params['products'] = products;
            params['variants'] = variants;

            const responseRaw = await RentMy.helpers.ajaxPost(params)
            try {
                let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.page.products.changePackageVariants', {
                    bubbles: true,
                    detail: {
                        products : this,
                        request: params,
                        response: response
                    }
                }))

                if (response.status === 'OK') {
                    this.setModalData({
                        available: response.result.data
                    });
                }
            } catch (e) {
            }
        },

        //when change product variant option
        changeProductVariant: async function (data) {
            index = data.attr('data-index');
            total = data.attr('data-total');
            if (index != total) {
                current_set_id = data.attr('data-id');
                next_set_id = data.attr('data-next-id');
                let params = {
                    action_type: 'get_variant_chain',
                    product_id: data.attr('data-product-id'),
                    variant_id: data.val(),
                    rent_type: 'rent'
                }
                const responseRaw = await RentMy.helpers.ajaxPost(params)
                try {
                    let response = JSON.parse(responseRaw);

                    // define a custom event
                    window.dispatchEvent(new CustomEvent('rentMy.page.products.changeProductVariant', {
                        bubbles: true,
                        detail: {
                            products : this,
                            request: params,
                            response: response
                        }
                    }))
                    this.bind_select_options(next_set_id, response);
                } catch (e) {
                }

            } else { // last item of the chain
                prev_set_id = data.attr('data-prev-id');
                let params = {
                    action_type: 'get_last_variant',
                    product_id: data.attr('data-product-id'),
                    variant_id: data.val(),
                    chain_id: $('#variantSet_' + prev_set_id).val(),
                    rent_type: 'rent',
                }
                const responseRaw = await RentMy.helpers.ajaxPost(params)
                try {
                    let response = JSON.parse(responseRaw);

                    //define a custom event
                    window.dispatchEvent(new CustomEvent('rentMy.page.products.changeProductVariant', {
                        bubbles: true,
                        detail: {
                            products : this,
                            request: params,
                            response: response
                        }
                    }))
                    this.variants_products_id = response.variants_products_id;
                    this.setModalData({
                        priceString: `${response.prices[0].base.html}`,
                        availableForSale: response.available,
                    })
                } catch (e) {

                }
            }
        },

        bind_select_options: function (el_id, json) {
            if (json && json.length > 0) {
                $('#variantSet_' + el_id).empty().append('<option selected="selected" value="">--Select--</option>');
                for (var i = 0; i < json.length; i++) {
                    var option = $("<option>");
                    option.attr("value", json[i]['id']);
                    option.html(json[i]['name']);
                    $('#variantSet_' + el_id).append(option);
                }
            }
        },

        // set modal data
        setModalData: function (data) {
            //define a custom event

            let rental_type_detail = $('.rental_type_detail').val();
            if( rental_type_detail ) {
                return;
            }


            window.dispatchEvent(new CustomEvent('rentMy.page.products.setModalData', {
                bubbles: true,
                detail: {
                    products : this,
                    data: data,
                }
            }))

            if (data.images != undefined) {
                this.$modal.find('.rentmy-product-multipleimage').html(data.images.map(src => {
                    return `<div class="rentmy-product-thumb-item">
                                <img src="${src}" alt="rentmy" />
                            </div>`
                }))

                this.$modal.find('.rentmy-product-viewimage-active').attr('src', data.images[0])
                RentMy.helpers.initMultipleImagePopup()
            }
            if (data.name != undefined)
                this.$modal.find('.rentmy-product-title').html(data.name)
            if (data.description != undefined)
                this.$modal.find('.rentmy-modalproduct-description').html(data.description)
            if (data.priceString != undefined)
                this.$modal.find('.rentmy-product-price').html(data.priceString)
            if (data.rental_price != undefined)
                this.$modal.find('.rentmy-product-price').html(RentMy.helpers.currencyFormat(data.rental_price))
            if (data.availableForSale != undefined) {
                this.$modal.find('.rentmy-product-available').html(data.availableForSale)
                this.$modal.find('.rentmy-in-num').attr('max', data.availableForSale)
            }
            if (data.available != undefined) {
                this.$modal.find('.rentmy-product-available').html(data.available)
                this.$modal.find('.rentmy-in-num').attr('max', data.available)
            }

            if (data.prices != undefined || data.price != undefined) {

                let prices = '';
                if (data.prices != undefined)
                    prices = this.format_prices(data.prices)
                else if (data.price != undefined)
                    prices = this.format_prices(data.price)

                let priceTypeHTML = '';
                let idPrefix = Math.random()
                if (prices.buy.type) {
                    let buyValue = this.content.product_details.lbl_buy;
                    priceTypeHTML += `<label class="rentmy-radio" for="rentmy-buy-${idPrefix}">
                                        <input type="radio" id="rentmy-buy-${idPrefix}" name="rentmy-package" value="buy" checked/> ` + buyValue + ` <span></span>
                                    </label>`
                }
                if (prices.rent.type) {
                    let rentValue = this.content.product_details.lbl_rent;
                    priceTypeHTML += ` <label class="rentmy-radio" for="rentmy-rent-${idPrefix}">
                                        <input type="radio" id="rentmy-rent-${idPrefix}" name="rentmy-package" data-price="${data.rental_price != undefined ? data.rental_price : ''}" value="rent" /> ` + rentValue + `<span></span>
                                    </label>`
                }

                this.$modal.find('.price-options').html(priceTypeHTML)

                this.$modal.find('input[name="rentmy-package"]').each(function () {
                    if ($(this).val() == 'rent') {
                        $(this).trigger('click')
                    }
                })
            }
            //check if package type
            if (data.isPackage != undefined && data.isPackage === 1) {
                let list = ''
                for (var product of data.products) {
                    list += `<div class="rentmy-package-single-list">
                                <h5>${product.name} (${product.quantity})</h5>`

                    //check variants

                    if ((product.variants.length > 0) && (product.variants[0].variant_chain != "Unassigned: Unassigned")) {

                        list += `<select data-package_uid="${data.uid}" data-package_id="${data.id}" data-product="${product.id}" class="variants">`
                        for (const variant of product.variants) {
                            list += `<option value="${variant.id}">${variant.variant_chain}</option>`
                        }
                        list += `</select>`
                    }

                    list += `</div>`
                }


                this.$modal.find('.rentmy-details-package-body span').html(list)
            }

            // check pricing option
            if (data.price_type != undefined && data.price_type == 'rent') {
                let pricing_list = '<ul>';
                    if (data.formated_prices.rent.type) {
                        for (const price of data.formated_prices.rent.price) {
                            pricing_list += `<li> <strong><i class="lni lni-arrow-right"></i> ${RentMy.helpers.currencyFormat(price.price)}</strong> for ${price.duration} ${price.label}</li>`;
                        }
                    }

                pricing_list += '</ul>';
                this.$modal.find('.rentmy-modal-rentbuy .rentmy-pricing-options').html(pricing_list);

            } else {
                this.$modal.find('.rentmy-modal-rentbuy .rentmy-pricing-options').html('');
            }

            // Product variants
            if (data.isPackage != undefined && data.isPackage != 1) {
                let html = '';
                if (data.variant_set_list.length > 0 && data.variant_list.length > 0) {

                    data.variant_set_list.forEach((variant, index) => {
                        html += '<div class="rentmy-product-variant">';
                        html += `<h5>${variant.name}</h5>`;
                        html += `<select 
                                    class="rentmy-product-variants"
                                    data-total="${data.variant_set_list.length}"
                                    data-id="${variant.id}"
                                    data-product-id="${data.id}"
                                    data-next-id="${data.variant_set_list.length > index + 1 ? data.variant_set_list[index + 1].id : ''}"
                                    data-prev-id="${index > 0 ? data.variant_set_list[index - 1].id : ''}"
                                    id="variantSet_${variant.id}"
                                    data-index="${index + 1}"
                                >`;
                        filtered_varint = data.variant_list.filter(function (opt) {
                            return opt.variant_set_id == variant.id;
                        });

                        filtered_varint.forEach(variant_opt => {
                            html += `<option value='${variant_opt.id}' ${ (data.default_variant.variant_chain_id == variant_opt.id)?'selected':'' }>${variant_opt.name}</option>`;
                        });
                        html += '</select>';
                        html += '</div>';

                    });

                }
                this.$modal.find('.rentmy-modal-product-variants').html(html);

            }

            // Product Options

            if (data.product_options != undefined && data.product_options.length > 0) {
                console.log(data.product_options)
                let productOptionHml = '';
                data.product_options.forEach((options, index) => {
                        if (options.type == 'select'){
                            productOptionHml += '<div class="rentmy-product-option form-group">';
                            productOptionHml += `<h5>${options.name}</h5>`;
                            productOptionHml += `<select 
                                    class=""
                                    data-id="${options.id}"
                                    id="variantSet_${options.id}"
                                    name="product_options[]"

                                >`;
                            productOptionHml += `<option value="">Select</option>`;
                            options.product_field_value.forEach(opt => {
                                let operator = opt.price_amount < 0?'-':'+';
                                productOptionHml += `<option value='${JSON.stringify(opt)}'>${opt.value+' '}${'('+operator+((opt.price_type==1)?(opt.price_amount + '%'):RentMy.helpers.currencyFormat(opt.price_amount))+')'}</option>`;
                            });
                            productOptionHml += '</select>';
                            productOptionHml += '</div>';
                        }

                });
                this.$modal.find('.rentmy-modal-product-options').html(productOptionHml);
            }

            //check if date is selected for cart then show/hide availibility
            const $availableElm = this.$modal.find('.rentmy-product-available').closest('p');
            const $metaDate = $('meta[name=rentmy_rent_start]').attr('content');
            if ($metaDate === '') {
                $availableElm.addClass('rentmy-hide');
                this.$modal.find('.rentmy-in-num').removeAttr('max')
            } else
                $availableElm.removeClass();

            //when binding all data is completed open whatever type of modal it is!
            this.modalOpen();
        },

        //Open whatever modal is to open/triggered
        modalOpen: function () {
            //before modal open rest quantity field
            const $input = this.$modal.find('input.rentmy-in-num')
            $input.val(1);
            $input.trigger('change');

            //open modal
            this.$modal.addClass('is-open')
        },

        //Open whatever modal is to open/triggered
        modalClose: function () {
            //open modal
            this.$modal.removeClass('is-open')
        },

        //send request to server for loading modal with data
        requestProductDetails: async function () {
            let param = {
                action_type: 'product_details',
                isPackage: this.isPackage,
                uid: this.btnData.uid
            };

            const responseRaw = await RentMy.helpers.ajaxPost(param)


            try {
                let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.page.products.load', {
                    bubbles: true,
                    detail: {
                        products : this,
                        request : param,
                        response: response,
                    }
                }))

                if (response.status === 'OK') {
                    this.data = response.result.data
                    if (this.data.isPackage == 1){
                        formated_price = this.format_prices(this.data.price);
                    }else{
                        formated_price = this.format_prices(this.data.prices);
                    }

                    if(formated_price.rent.type){
                        this.data.price_type = 'rent';
                    }
                    this.variants_products_id = this.data.default_variant.variants_products_id;
                    this.setModalData(this.data)
                }
                if (response.result != undefined && response.result.message != undefined) {
                    RentMy.alert.errorAlert(response.result.message)
                }
            } catch (e) {

            }
        },

        requestProductOptions: async function(){

            let param = {
                action_type: 'product_options',
                isPackage: this.isPackage,
                id: this.btnData.id
            };

            const responseRaw = await RentMy.helpers.ajaxPost(param)


            try {
                let response = JSON.parse(responseRaw);

                //define a custom event
                window.dispatchEvent(new CustomEvent('rentMy.page.products.product_option', {
                    bubbles: true,
                    detail: {
                        products : this,
                        request : param,
                        response: response,
                    }
                }))

                if (response.status === 'OK') {

                    this.setModalData({
                        product_options: response.result.data
                    })
                }
                if (response.result != undefined && response.result.message != undefined) {
                    RentMy.alert.errorAlert(response.result.message)
                }
            } catch (e) {

            }
        },

        //modal trigger
        modalTrigger: function ($modal, $elm, isPackageType = 0) {
            this.btnData = JSON.parse($elm.attr('data-product'))
            this.$modal = $modal;
            this.isPackage = isPackageType;
            this.requestProductDetails()
            this.requestProductOptions();
        },

        addToCart: function (product = {}) {
            let data = {}
            let rental_type_detail = $('.rental_type_detail').val();
            if( rental_type_detail ) {
                this.rental_type = rental_type_detail;
            }

            if (product.id != undefined) {
                data.product_id = product.id
                data.quantity = product.quantity
                data.rental_type = 'rent'
                data.variants_products_id = product.variants_products_id
                data.action_type = 'add_to_cart';
                data.product_type = product.type;

            } else {
                data = {
                    product_id: this.data.id,
                    quantity: this.getQuantity(),
                    rental_type: this.rental_type,
                    action_type: 'add_to_cart',
                };
                if (this.isPackage === 1) {
                    var products = [];
                    $('select.variants').each(function (i, item) {
                        var package_item = {
                            product_id: $(this).attr('data-product'),
                            variants_products_id: $(this).val()
                        };
                        products.push(package_item)
                    });
                    data.products = products
                    data.product_type = 2
                    data.variants_products_id = this.data.variants_products_id
                } else {
                    data.product_type = 1
                    data.variants_products_id = this.variants_products_id

                }
            }

            let rent_start = $('.date_start_detail').val();
            let rent_end = $('.date_end_detail').val();
            if( rent_start ) {
                data.rent_start = rent_start;
                data.rent_end = rent_end;
            }

            let custom_fields = $(".rentmy-modal-product-options select[name='product_options[]']")
                .map(function(){
                    if ($(this).val() != ''){
                        return JSON.parse($(this).val());
                    }

                }).get();
            if (custom_fields){
                data['custom_fields'] = custom_fields
            }
            data.required_addons = this.required_addons;
            RentMy.cartWidget.addToCart(data, this)
        }
    },
    categories: {
        isInit: false,
        init: function () {
            this.isInit = true;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.categories.init', {
                bubbles: true,
                detail: {
                    categories : this,
                }
            }))

        }
    },
    cart: {
        isInit: false,
        init: function () {
            this.isInit = true;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.cart.init', {
                bubbles: true,
                detail: {
                    cart : this,
                }
            }))
            $(RentMy.globalSelector)
                .on('click', '.rentmy-apply-coupon-btn', function (){
                    let coupon_code = $('.coupon-code-input').val();
                    if (coupon_code != ''){
                        RentMy.cart.applyCoupon(coupon_code);
                    }
            })

        },
        applyCoupon: async function (coupon_code){
            let data = {
                coupon: coupon_code,
                action_type: 'apply_coupon'
            }
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try{
                let response = JSON.parse(responseRaw)
                if (response.status == 'OK'){
                    location.reload();
                }

            }catch (e){

            }
        }
    },
    checkout: {
        content: CONTENT,
        // config: STORE_CONFIG,
        billing_info: {
            country: 'US,',
            first_name: '',
            last_name: '',
            mobile: '',
            company: '',
            email: '',
            address_line1: '',
            address_line2: '',
            city: '',
            zipcode: '',
            state: ''
        },
        locations: [],
        shipping_info: {
            shipping_address1: '',
            shipping_address2: '',
            shipping_city: '',
            shipping_country: 'US',
            shipping_first_name: '',
            shipping_last_name: '',
            shipping_method: '',
            shipping_mobile: '',
            shipping_name: '',
            shipping_state: '',
            shipping_zipcode: ''
        },
        delivery_info: {
            shipping_address1: '',
            shipping_address2: '',
            shipping_city: '',
            shipping_country: 'US',
            shipping_first_name: '',
            shipping_last_name: '',
            shipping_method: '',
            shipping_mobile: '',
            shipping_name: '',
            shipping_state: '',
            shipping_zipcode: ''
        },
        countries: [],
        delivery_location: [],
        delivery_settings: [],
        zone_list: [],
        fulfillment_errors: [],
        billing_error: [],
        payment_errors: [],
        store_config: STORE_CONFIG,
        currency_symbol: '',
        base_file_url: 'https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/',
        asset_url: BASE_URL,
        base_url: BASE_URL,
        additional_services: [],
        cartable_additional_services: [],
        store_id: STORE_ID,
        payment_getways: [],
        payment_config: {},
        amount_to_pay: '',
        booking_amount: '',
        cart_total: '',
        signature_pad: '',
        customerAddresses: [],
        isInit: false,
        init: async function () {
            this.isInit = true;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.checkout.init', {
                bubbles: true,
                detail: {
                    checkout : this,
                }
            }))

            this.currency_symbol = (this.store_config && this.store_config.currency_format)?this.store_config.currency_format.symbol:'$';
            this.billing_info['country'] = 'US';
            this.payment_config = this.store_config.payments;
            await this.getLocationList();
            await this.getCountries();
            await this.getPaymentMethods();
            await this.getDeliverySettings();
            await this.getCartData();
            await this.getAdditionalServices();

            this.signaturePad();

            if($('.rentmy-customer-info').length > 0){
                let customer = $('.rentmy-customer-info').val();
                if(customer != ''){
                    customer = JSON.parse(customer);
                    this.billing_info.first_name = customer.first_name;
                    this.billing_info.last_name = customer.last_name;
                    this.billing_info.mobile = customer.mobile;
                    this.billing_info.email = customer.email;
                    this.billing_info.company = customer.company;
                }
                await this.getCustomerAddresses();
                this.bindBillingCustomerAddresses();
                this.bindDeliveryCustomerAddresses();

            }

            $(RentMy.globalSelector)
                // Getting Billing Data
                .on('keyup', '.rentmy-billing-address input', function (event) {
                    if (!event.target.name.includes('new')){
                        RentMy.checkout.billing_info[event.target.name] = event.target.value;
                    }
                    RentMy.checkout.calculateTax();

                })
                .on('change', '.rentmy-billing-address select', function (event) {
                    if (!event.target.name.includes('new')){
                        RentMy.checkout.billing_info[event.target.name] = event.target.value;
                    }
                })
                .on('click', '.rentmy-btn-shipping-method', function (event) {
                    $(RentMy.globalSelector).find('.renmty-checkout-shipping input,select').map(function () {
                        if ($(this).attr('name') != undefined)
                            RentMy.checkout.shipping_info[$(this).attr('name')] = $(this).val();
                    });
                    RentMy.checkout.getShippingMethods();
                })
                .on('click', '.rentmy-btn-delivery-cost', function (event) {
                    $(RentMy.globalSelector).find('.renmty-checkout-delivery input,select').map(function () {
                        if ($(this).attr('name') != undefined)
                            RentMy.checkout.delivery_info[$(this).attr('name')] = $(this).val();
                    });
                    RentMy.checkout.getDeliveryCost();
                })
                .on('click', '.rentmy-shipping-methods input[type="radio"]', function () {
                    RentMy.checkout.addShippingToCart($(this).val(), 7);
                })
                .on('click', '.rentmy-delivery-cost input[type="radio"]', function () {
                    RentMy.checkout.addShippingToCart($(this).val(), 2);
                })
                .on('click', '.rentmy-fulfillment input[name="rentmy-pickup-location"]', function () {
                    RentMy.checkout.addShippingToCart(0, 1);
                    RentMy.checkout.getCartData();
                })
                //Additional Services
                .on('click', '.rentmy-optional-service-content .rentmy-btn-amount', function () {
                    let option_val = $(this).val();
                    let service_id = $(this).data('servie_id');

                    $(RentMy.globalSelector).find('#rentmy-service-' + service_id + ' button').removeClass('rentmy-btn-active');
                    $(RentMy.globalSelector).find('#rentmy-service-checkbox-' + service_id).attr('checked', true);
                    $(this).addClass('rentmy-btn-active');
                    RentMy.checkout.update_additional_services(service_id, option_val);

                })
                .on('click', '.rentmy-optional-service-content .rentmy-input-amount-btn', function () {
                    let service_id = $(this).data('id');
                    $(RentMy.globalSelector).find('#rentmy_input_amount_area_' + service_id).css('display', 'block');
                })
                .on('click', '.rentmy-optional-service-content .rentmy-optional-cancel-btn', function () {
                    let service_id = $(this).data('service_id');
                    $(RentMy.globalSelector).find('#rentmy_input_amount_area_' + service_id).css('display', 'none');
                })
                .on('click', '.rentmy-optional-service-content .rentmy-optional-ok-btn', function () {
                    let service_id = $(this).data('service_id');
                    let inputed_value = $(RentMy.globalSelector).find('#rentmy_input_amount_area_' + service_id + ' input').val();
                    RentMy.checkout.update_additional_services(service_id, inputed_value);
                    $(RentMy.globalSelector).find('#rentmy_input_amount_area_' + service_id).css('display', 'none');
                })
                .on('change', '.rentmy-optional-service-content select', function () {
                    let service_id = $(this).data('service_id');
                    let selected_option = $(RentMy.globalSelector).find('#rentmy_additional_service_option_' + service_id).val();
                    RentMy.checkout.set_additional_services_option(service_id, selected_option);
                })
                .on('click', '.rentmy-optional-service input:checkbox', function () {
                    let service_id = $(this).val();
                    let is_checked = $(RentMy.globalSelector).find('#rentmy-service-checkbox-' + service_id).prop('checked');
                    if (!is_checked) {
                        RentMy.checkout.remove_additional_service_from_cart(service_id);
                    } else {
                        RentMy.checkout.update_additional_services(service_id);
                    }
                })
                //Additonal Service End rentmy-payment-method
                .on('click', '.rentmy-placeorder-btn', function () {
                    RentMy.checkout.placeOrder();
                })
                .on('click', '.rentmy-partial-payments', function () {
                    let type = $(this).val();
                    if (type == 'partial') {
                        RentMy.checkout.amount_to_pay = RentMy.checkout.booking_amount;
                    }
                    if (type == 'full') {
                        RentMy.checkout.amount_to_pay = RentMy.checkout.cart.total;
                    }
                    let gateway_id = $(this).data('gateway_id');

                    $(RentMy.globalSelector).find(`#rentmy-payment-content-${gateway_id} input[name=amount_to_pay]`).val(RentMy.checkout.amount_to_pay);

                })
                .on('click', '#rentmy-termcondition', function () {
                    if (RentMy.checkout.store_config && RentMy.checkout.store_config.signature && RentMy.checkout.store_config.signature.active && RentMy.checkout.store_config.signature.online) {
                        if (!$(this).prop('checked')) {
                            $(RentMy.globalSelector).find('.rentmy-signature-pad').css('display', 'none');
                        } else {
                            $(RentMy.globalSelector).find('.rentmy-signature-pad').css('display', 'block');
                        }
                    }
                })
                .on('click', '.rentmy-signature-pad .rentmy-clear-signature', function () {
                    RentMy.checkout.signature_pad.clear();
                })
                .on('click', 'input[name="rentmy-billing-radio-address"]', function () {

                    let value = $(this).val()
                    if(value == 'new'){
                        $('.rentmy-billing_info-new-address-content').css('display','block')
                    }else{
                        $('.rentmy-billing_info-new-address-content').css('display','none')
                        let selected_address = RentMy.checkout.customerAddresses[RentMy.checkout.customerAddresses.findIndex((x)=>x.id==value)];
                        RentMy.checkout.bindRadioAdressWithBillingInfo(selected_address);
                    }

                })
                .on('click', 'input[name="rentmy-delivery-radio-address"]', function () {
                    let value = $(this).val()
                    if(value == 'new'){
                        $('.rentmy-delivery-new-address-content').css('display','block')
                    }else{
                        $('.rentmy-delivery-new-address-content').css('display','none')
                        let selected_address = RentMy.checkout.customerAddresses[RentMy.checkout.customerAddresses.findIndex((x)=>x.id==value)];
                        RentMy.checkout.bindRadioAdressWithShippingInfo(selected_address);
                    }

                })

                .on('click', '.rentmy-btn-add-new-aaddress', function (){
                    let data = {};
                    let params = {};

                    $('.renmty-billing-new-address input,select').each(function (){
                        data[$(this).attr('name')] = $(this).val()
                    })
                    params['address_line1'] = data.new_address_line1;
                    params['address_line2'] = data.new_address_line2;
                    params['country'] = data.new_country;
                    params['city'] = data.new_city;
                    params['zipcode'] = data.new_zipcode;
                    params['state'] = data.new_state;
                    params['type'] = "Primary";
                    params['action_type'] = "customer_new_address";
                    RentMy.checkout.addNewCustomerAddress(data, 'billing');
                })
                .on('click', '.rentmy-btn-add-new-delivery-address', function (){
                    let data = {};
                    let params = {};

                    $('.renmty-delivery-new-address input,select').each(function (){
                        data[$(this).attr('name')] = $(this).val()
                    })

                    params['address_line1'] = data.new_address_line1;
                    params['address_line2'] = data.new_address_line2;
                    params['country'] = data.new_country;
                    params['city'] = data.new_city;
                    params['zipcode'] = data.new_zipcode;
                    params['state'] = data.new_state;
                    params['type'] = "Primary";
                    params['action_type'] = "customer_new_address";
                    RentMy.checkout.addNewCustomerAddress(params, 'delivery');
                })
            ;


        },
        calculateTax: async function(){
            let ref = this;
            let billing_info = RentMy.checkout.billing_info;
           if (billing_info.city == '' || billing_info.state == '' || billing_info.zipcode == '' || billing_info.country == ''){
               return;
           }
           let data = {
               billing_country:billing_info.country,
               billing_city: billing_info.city,
               billing_state:billing_info.state,
               billing_zipcode:billing_info.zipcode,
               action_type:  "calculate_tax"
           }
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                let response = JSON.parse(responseRaw);
                if (response.status == 'OK') {
                    ref.bindOrderData(response.result.data);
                }
            }catch (e){

            }
        },
        addNewCustomerAddress: async function (data, from=''){
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                let response = JSON.parse(responseRaw);

                await RentMy.checkout.getCustomerAddresses();

                if (from =='billing'){
                    $('.rentmy-billing_info-new-address-content').css('display','none')
                    this.bindBillingCustomerAddresses();
                    $('input[name="rentmy-billing-radio-address"]').removeAttr('checked');
                }
                if (from == 'delivery'){
                    $('input[name="rentmy-delivery-radio-address"]').removeAttr('checked');
                    $('.rentmy-delivery-new-address-content').css('display','none')
                    this.bindDeliveryCustomerAddresses();
                }
            }catch (e){

            }

        },
        bindRadioAdressWithBillingInfo: function (address){

            this.billing_info.address_line1 = address.address_line1;
            this.billing_info.address_line2 = address.address_line2;
            this.billing_info.country = address.country;
            this.billing_info.city = address.city;
            this.billing_info.zipcode = address.zipcode;
            this.billing_info.state = address.state;
        },

        bindRadioAdressWithShippingInfo: function (address){

            this.delivery_info.shipping_address1 = address.address_line1;

            this.delivery_info.shipping_address2 = address.address_line2;
            this.delivery_info.shipping_country = address.country;
            this.delivery_info.shipping_city = address.city;
            this.delivery_info.shipping_zipcode = address.zipcode;
            this.delivery_info.shipping_state = address.state;

            $(".rentmy-delivery-radio-info").find('input[name="shipping_address1"]').val(address.address_line1);
            $(".rentmy-delivery-radio-info").find('input[name="shipping_address2"]').val(address.address_line2);
            $(".rentmy-delivery-radio-info").find('input[name="shipping_country"]').val(address.country);
            $(".rentmy-delivery-radio-info").find('input[name="shipping_city"]').val(address.city);
            $(".rentmy-delivery-radio-info").find('input[name="shipping_zipcode"]').val(address.zipcode);
            $(".rentmy-delivery-radio-info").find('input[name="shipping_state"]').val(address.state);

        },
        signaturePad: function () {
            if (RentMy.checkout.store_config && RentMy.checkout.store_config.signature && RentMy.checkout.store_config.signature.active && RentMy.checkout.store_config.signature.online) {
                var canvas = document.getElementById('rentmy-signature');

                this.signature_pad = new SignaturePad(canvas, {
                    'penColor': 'black',
                    'backgroundColor': 'white',
                    backgroundColor: 'rgb(255, 255, 255)' // necessary for saving image as JPEG; can be removed is only saving as PNG or SVG
                });
            }
        },
        getCustomerAddresses: async function (){
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'get_customer_addreses',
            })
            try {
                let response = JSON.parse(responseRaw);
                if (response.status == 'OK'){
                    this.customerAddresses = response.result.data;

                }
            }catch (e){

            }
        },
        bindBillingCustomerAddresses: function (){

            $('rentmy-radio-locations').html('');
            let html = '';
            if (this.customerAddresses.length > 0){
                this.customerAddresses.forEach(function (address){
                    html+= `<div class="rentmy-radio-inline">
                        <label class="rentmy-radio" htmlFor="rentmy-customer-address-${address.id}">
                            <input type="radio" id="rentmy-customer-address-${address.id}"
                                   name="rentmy-billing-radio-address"
                                   value="${address.id}"/>${address.full_address}  <span></span>
                        </label>
                    </div>`
                });
            }
            $(".rentmy-radio-locations").html(html);
        },
        bindDeliveryCustomerAddresses: function (){
            $('.rentmy-delivery-radio-locations').html('');
            let html = '';

            if (this.customerAddresses.length > 0){
                this.customerAddresses.forEach(function (address){
                    html+= `<div class="rentmy-radio-inline">
                        <label class="rentmy-radio" htmlFor="rentmy-delivery-address-${address.id}">
                            <input type="radio" id="rentmy-delivery-address-${address.id}"
                                   name="rentmy-delivery-radio-address"
                                   value="${address.id}"/>${address.full_address}  <span></span>
                        </label>
                    </div>`
                });
            }
            $(".rentmy-delivery-radio-locations").html(html);
        },
        getDeliverySettings: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'get_delivery_settings',
            })

            try {
                let response = JSON.parse(responseRaw);
                ref.delivery_settings = response.delivery_settings;
                if (!ref.delivery_settings.instore_pickup) {
                    $(RentMy.globalSelector).find('.rentmy-fulfillment-pickup').css('display', 'none');
                }
                if (!ref.delivery_settings.delivery) {
                    $(RentMy.globalSelector).find('.rentmy-fulfillment-delivery').css('display', 'none');
                }
            } catch (e) {
            }
        },
        getCartData: async function () {

            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'view_cart_info',
            })

            try {

                let response = JSON.parse(responseRaw);
                if (response.status == 'OK') {
                    ref.bindOrderData(response.result.data);
                }
            } catch (e) {
            }
        },
        bindOrderData: function (data) {

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.checkout.bindData', {
                bubbles: true,
                detail: {
                    checkout : this,
                    data: data
                }
            }))

            let order_product_list = '';
            let order_summary = '';
            this.cart = data;
            this.amount_to_pay = this.cart.total;
            this.booking_amount = this.cart.booking ?? 0;

            this.bindPaymentMethods();

            // $('.rentmy-partial-payments').find('input[name="amount_to_pay"]').val(this.amount_to_pay);
            data.cart_items.forEach(cart_item => {
                if ($('.rentmy-custom-builder').length > 0){

                    order_product_list += `<li>
                                        <div class="rentmy-list-item">
                                            <img class="rentmy-product-image" src="${this.imageLink(cart_item)}" />
                                            <div class="rentmy-cart-info">
                                                <div class="rentmy-product-name rentmy-large">${cart_item.product.name} ${'('+cart_item.variant_chain_name+')'}</div>
                                                <ul class="rentmy-modal-optionlist">`;
                                                    cart_item.products.forEach(prod=>{
                                                        order_product_list += `<span>${prod.name} :`;
                                                        let variant_chain = prod.variant_chain.split(',');
                                                        variant_chain.forEach(function (variant){
                                                            let variant_spilted = variant.split(':');
                                                            order_product_list += `${variant_spilted[1]},`;
                                                        })
                                                        order_product_list += `</span><br>`;
                                                    });
                                                `</ul>
                                            </div>
                                        </div>
                                    </li>`;
                    $(RentMy.globalSelector).find('.rentmy-cart-sidebar-productlist ul').html(order_product_list);
                }else{

                    order_product_list += ` <div class="rentmy-order-list-box">
                                    <div class="rentmy-order-img">
                                        <img src="${this.imageLink(cart_item)}"/>
                                    </div>
                                    <div class="rentmy-order-product-details">
                                        <div class="rentmy-order-product-name">
                                            ${cart_item.product.name}&nbsp;
                                            <strong class="rentmy-order-product-quantity">×&nbsp;${cart_item.quantity}</strong>`;


                    order_product_list+= `</div>
                                        <div class="rentmy-order-details-bottom">
                                            <p class="rentmy-order-quantity">${this.content.product_details.lbl_quantity}: ${cart_item.quantity}</p>
                                            <p class="rentmy-order-product-price">${this.content.cart.lbl_th_price}: ${this.currency_symbol}${cart_item.sub_total}</p>
                                        </div>
                                    </div>
                                </div>`;
                    $(RentMy.globalSelector).find('.rentmy-checkout-ordersummery .rentmy-ordersummery-list').html(order_product_list);
                }

            });
            order_summary += `<table>
                                    <tbody>`;
            if (data.additional_charge > 0) {
                order_summary += `<tr>
                                <th>${this.content.cart.lbl_additional_charge}</th>
                                <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.additional_charge))}</td>
                              </tr>`;
            }
            order_summary += `<tr>
                                    <th>${this.content.cart.lbl_th_subtotal}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.sub_total))}</td>
                               </tr>
                               <tr>
                                   <th>${this.content.cart.lbl_shipping}</th>
                                   <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.delivery_charge))}</td>
                               </tr>
                               <tr>
                                    <th>${this.content.cart.lbl_discount}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.total_discount))}</td>
                               </tr>`;


            if ((data.tax==null) || (data.tax==0) || ((typeof data.tax=='object') && (data.tax.length <= 0))){
                order_summary += `<tr>
                                    <th>${this.content.cart.lbl_tax}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(0))}</td>
                               </tr>`;


            }else{
                for (let i=0; i<data.tax.regular.length; i++){
                    order_summary += `<tr>
                                    <th>${data.tax.regular[i].name}${'('+data.tax.regular[i].rate+'%)'}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.tax.regular[i].total))}</td>
                               </tr>`;
                }
            }
            order_summary += `
                                <tr>
                                     <th>${this.content.cart.delivery_tax??'Delivery Tax'}</th>
                                     <td><strong>${RentMy.helpers.currencyFormat(this.priceFormat(data.delivery_tax))}</strong></td>
                               </tr>
                                <tr>
                                     <th><strong>${this.content.cart.lbl_total}</strong></th>
                                     <td><strong>${RentMy.helpers.currencyFormat(this.priceFormat(data.total))}</strong></td>
                               </tr>
                             </tbody>
                          </table>`;

            if ($('.rentmy-custom-builder').length > 0) {
                let order_summary_custom = '';
                order_summary_custom += `<table>
                                    <tbody>`;
                if (data.additional_charge > 0) {
                    order_summary_custom += `<tr>
                                <td>${this.content.cart.lbl_additional_charge}</td>
                                <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.additional_charge))}</td>
                              </tr>`;
                }
                order_summary_custom += `<tr>
                                    <td>${this.content.cart.lbl_th_subtotal}</td>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.sub_total))}</td>
                               </tr>
                               <tr>
                                   <td>${this.content.cart.lbl_shipping}</td>
                                   <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.delivery_charge))}</td>
                               </tr>
                               <tr>
                                    <td>${this.content.cart.lbl_discount}</td>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.total_discount))}</td>
                               </tr>`;
            if ((data.tax==null) || (data.tax==0) || ((typeof data.tax=='object') && (data.tax.length <= 0))){
                order_summary += `<tr>
                                    <th>${this.content.cart.lbl_tax}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(0))}</td>
                               </tr>`;
            }else{
                for (let i=0; i<data.tax.regular.length; i++){
                    order_summary += `<tr>
                                   <th>${data.tax.regular[i].name}${'('+data.tax.regular[i].rate+'%)'}</th>
                                    <td>${RentMy.helpers.currencyFormat(this.priceFormat(data.tax.regular[i].total))}</td>
                               </tr>`;
                }
            }

                order_summary += `<tr>
                                     <td><strong>${this.content.cart.lbl_total}</strong></td>
                                     <td><strong>${RentMy.helpers.currencyFormat(this.priceFormat(data.total))}</strong></td>
                               </tr>
                             </tbody>
                          </table>`;

                $(RentMy.globalSelector).find('.rentmy-checkout-ordersummery .rentmy-cart-order-summery').html(order_summary_custom);
            }else{
                $(RentMy.globalSelector).find('.rentmy-checkout-ordersummery .rentmy-checkout-order-table').html(order_summary);
            }

        },
        getLocationList: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'location_list',
            })
            try {
                let response = JSON.parse(responseRaw);
                ref.locations = response.data;

                ref.bind_pickup_locations(ref.locations);
            } catch (e) {}
        },
        bind_pickup_locations: function (locaions) {
            let list = '';
            locaions.forEach(location => {
                list += `<div class="rentmy-radio-inline">
                           <label class="rentmy-radio" for="rentmy-pickup-location">
                                 <input type="radio" id="rentmy-pickup-location" name="rentmy-pickup-location" data-delivery='${JSON.stringify(location)}' value="${location.id}"/>
                                       ${location.name} (${location.location})
                                  <span></span>
                           </label>
                        </div>`;
            });
            $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-pickup-location-list').html(list);
        },

        getCountries: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'countries',
            })
            try {
                let response = JSON.parse(responseRaw);
                ref.countries = response;
                ref.bind_countries_option(ref.countries);
            } catch (e) {}
        },
        bind_countries_option: function (countries) {
            let list = '';
            countries.forEach(country => {
                list += `<option value="${country.code}" ${country.code == 'US' ? 'selected' : ''}>${country.name}</option>`
            });
            $(RentMy.globalSelector).find('.rentmy-checkout-wrapper .rentmy-country').html(list);
        },
        getShippingMethods: async function () {
            let ref = this;
            this.fulfillment_errors = [];
            $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-fulfillment-error').html('');

            let error_length = ref.shippingValidation();
            if (error_length > 0) {
                ref.bindFulfillmentError();
                return;
            }
            let data = {};
            let sp_first_name = this.shipping_info.shipping_name.trim().split(' ')[0];
            let sp_last_name = this.shipping_info.shipping_name.trim().split(' ').filter(function (item) {
                return item != sp_first_name
            });
            data = ref.shipping_info;
            data['action_type'] = 'get_shipping_method';
            data['shipping_first_name'] = sp_first_name;
            data['shipping_last_name'] = sp_last_name.join(" ");
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                data = JSON.parse(responseRaw);
                if (data.status == 'OK') {
                    ref.bindShippingMethods(data.result);
                } else {
                    let html = `<ul class="rentmy-error"><li>${data.result.error}</li></ul>`;
                    $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-shipping-methods').html(html);
                }
            } catch (e) {}
        },
        bindShippingMethods: function (data) {

            let method_list = '';
            if (Object.keys(data).length > 0) {
                method_list += `<p class="shipping-method-title"> Select shipping method </p>`;

                for (var key of Object.keys(data)) {
                    if (key == 'flat') {

                        method_list += ` <div class="rentmy-radio-inline">
                            <label class="rentmy-radio" htmlFor="rentmy-shipping-method">
                                <input type="radio" id="rentmy-shipping-method" data-type="7" name="rentmy-shipping-method" data-delivery='${JSON.stringify(data[key])}' value="${data[key].charge}"/>
                               ${data[key].carrier_code} <div class="rentmy-list-price">${this.currency_symbol} ${data[key].charge}</div>
                                <span></span>
                            </label>
                        </div>`;
                    }
                    if (key == 'standard') {
                        method_list += ` <div class="rentmy-radio-inline">
                            <label class="rentmy-radio" htmlFor="rentmy-shipping-method">
                                <input type="radio" id="rentmy-shipping-method" data-type="6" name="rentmy-shipping-method" data-delivery='${JSON.stringify(data[key])}' value="${data[key].charge}"/>
                                ${data[key].carrier_code} <div class="rentmy-list-price">$ ${data[key].charge}</div>
                                <span></span>
                            </label>
                        </div>`;
                    }
                }
            }
            $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-shipping-methods').html(method_list);
        },
        bindFulfillmentError: function () {
            let list = `<ul class="rentmy-error">`;
            this.fulfillment_errors.forEach(error => {
                list += `<li>${error}</li>`;
            });
            list += `</ul>`;
            $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-fulfillment-error').html(list);
        },
        getDeliveryCost: async function () {
            let ref = this;
            $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-fulfillment-error').html('');
            this.fulfillment_errors = [];
            let error_length = ref.deliveryValidation();
            if (error_length > 0) {
                ref.bindFulfillmentError();
                return;
            }
            let data = {};
            let sp_first_name = this.delivery_info.shipping_name.trim().split(' ')[0];
            let sp_last_name = this.delivery_info.shipping_name.trim().split(' ').filter(function (item) {
                return item != sp_first_name
            });
            data = ref.delivery_info;
            data['action_type'] = 'get_delivery_cost';
            data['shipping_first_name'] = sp_first_name;
            data['shipping_last_name'] = sp_last_name.join(" ");

            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                data = JSON.parse(responseRaw);
                if (data.status == 'OK') {
                    if (ref.delivery_settings.charge_by_zone) {
                        let list = '';
                        data.result.location.forEach(zone => {
                            list += `<div class="rentmy-radio-inline">
                                        <label class="rentmy-radio">
                                            <input type='radio' value='${zone.charge}' data-delivery='${JSON.stringify(zone)}' name="delivery_by_zone"/>
                                            ${zone.name}
                                            <div class="rentmy-list-price">${this.currency_symbol}${zone.charge}</div>
                                            <span></span>
                                        </label>
                                     </div>`;
                        });
                        $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-delivery-cost').html(list);
                    } else {
                        let cost = data.result.location[0].charge;
                        let html = `<p>Delivery Cost ${this.currency_symbol}${cost}</p>`;
                        html += `<input type="hidden" id="rentmy_delivery" value='${JSON.stringify(data.result.location[0])}'/>`

                        $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-delivery-cost').html(html);
                        ref.addShippingToCart(cost, 3);
                    }

                } else {
                    let html = `<ul class="rentmy-error"><li>${data.result.error}</li></ul>`;
                    $(RentMy.globalSelector).find('.rentmy-fulfillment .rentmy-delivery-cost').html(html);
                }

            } catch (e) {}
        },
        addShippingToCart: async function (shipping_cost, shipping_method) {
            let ref = this;
            let address = {
                'billing_city': ref.billing_info.city,
                'billing_country': ref.billing_info.country,
                'billing_state': ref.billing_info.state,
                'billing_zipcode': ref.billing_info.zipcode,
            };
            console.log(ref.delivery_info)
            if (shipping_method == 2){
                address['shipping_city'] = ref.delivery_info.shipping_city;
                address['shipping_country'] =  ref.delivery_info.shipping_country;
                address['shipping_state'] = ref.delivery_info.shipping_state;
                address['shipping_zipcode'] = ref.delivery_info.shipping_zipcode;
            }else{
                address['shipping_city'] = ref.shipping_info.shipping_city;
                address['shipping_country'] =  ref.shipping_info.shipping_country;
                address['shipping_state'] = ref.shipping_info.shipping_state;
                address['shipping_zipcode'] = ref.shipping_info.shipping_zipcode;
            }
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'shipping_to_cart',
                shipping_cost: shipping_cost,
                shipping_method: shipping_method,
                tax: 0,
                tax_id: null,
                address: address,
            })
            try {
               let data = JSON.parse(responseRaw);
                if (data.status == 'OK' && shipping_method != 1) {
                    ref.bindOrderData(data.result.data);
                }
            } catch (e) {}
        },
        billingInfoValidation: function () {
            this.billing_error = [];

            if (!this.billing_info.mobile) {
                this.billing_error.push("Mobile is required.");
            } else {
                if (isNaN(parseInt(this.billing_info.mobile))) {
                    this.billing_error.push("Mobile number is not valid.");
                }
            }
            if (!this.billing_info.email) {
                this.billing_error.push("Email is required.");
            } else {
                let regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
                if (!regex.test(this.billing_info.email)) {
                    this.billing_error.push("Email address is not valid.");
                }
            }
            if (!this.billing_info.country) {
                this.billing_error.push("Country is required.");
            }
            if (!this.billing_info.address_line1) {
                this.billing_error.push("Address Line 1 is required.");
            }
            if (!this.billing_info.city) {
                this.billing_error.push("City is required.");
            }
            if (!this.billing_info.state) {
                this.billing_error.push("State is required.");
            }
            if (!this.billing_info.zipcode) {
                this.billing_error.push("Zipcode is required.");
            }

            return this.billing_error.length;
        },
        shippingValidation: function () {
            this.fulfillment_errors = [];
            if (!this.shipping_info.shipping_country) {
                this.fulfillment_errors.push('Country is required.');
            }
            if (!this.shipping_info.shipping_address1) {
                this.fulfillment_errors.push('Address Line 1 is required.');
            }
            if (!this.shipping_info.shipping_city) {
                this.fulfillment_errors.push('City is required.');
            }
            if (!this.shipping_info.shipping_state) {
                this.fulfillment_errors.push('State is required.');
            }
            if (!this.shipping_info.shipping_zipcode) {
                this.fulfillment_errors.push('Zipcode is required.');
            }
            return this.fulfillment_errors.length;
        },
        deliveryValidation: function () {

            this.fulfillment_errors = [];
            let is_error = false;
            if (!this.delivery_info.shipping_country) {
                this.fulfillment_errors.push('Country is required.');
            }
            if (!this.delivery_info.shipping_address1) {
                this.fulfillment_errors.push('Address Line 1 is required.');
            }
            if (!this.delivery_info.shipping_city) {
                this.fulfillment_errors.push('City is required.');
            }
            if (!this.delivery_info.shipping_state) {
                this.fulfillment_errors.push('State is required.');
            }
            if (!this.delivery_info.shipping_zipcode) {
                this.fulfillment_errors.push('Zipcode is required.');
            }

            return this.fulfillment_errors.length;
        },

        // Additional Services
        getAdditionalServices: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'get_additional_services',
                type: 'cart',
            })
            try {
                let response = JSON.parse(responseRaw);
                if (response.status == 'OK') {
                    ref.additional_services = response.result.data;
                    ref.cartable_additional_services = [];
                    ref.additional_services.forEach(service => {
                        ref.cartable_additional_services.push({
                            id: service.id,
                            is_selected: service.is_selected,
                            value: service.existing ? service.existing.config.user_entered : '',
                            order_additional_charge_id: service.existing ? service.existing.id : null,
                            selected_option: null
                        });
                    })
                    this.bindAdditionalServices();
                }


            } catch (e) {}
        },
        is_additional_service: function () {
            let ref = this;

            if (ref.additional_services.length === 0)
                return false;

            let i = 0;
            ref.additional_services.forEach(el => {
                if (el.status)
                    i++;
            });

            return i > 0;
        },
        bindAdditionalServices: function () {
            if (!this.is_additional_service()) {
                return;
            }
            let optionalServiceTitle = this.content.cart.lbl_consider_optional_services; // 'Consider this optional services';
            let html = `<div class="rentmy-row">
                                    <div class="rentmy-additional-charge-title">` + optionalServiceTitle + `</div>
                                    <div class="rentmy-column-12">`;
            this.additional_services.forEach(service => {
                html += `<div class="rentmy-row" id="rentmy-service-${service.id}">
                                            <div class="rentmy-checkbox-inline" >
                                                <label class="rentmy-checkbox" for="rentmy-service-checkbox-${service.id}">
                                                    <input type="checkbox" value="${service.id}" id="rentmy-service-checkbox-${service.id}" ${service.is_required || service.existing ? 'checked' : ''} ${service.is_required ? 'disabled' : ''}/> ${service.description} <span>&nbsp;&nbsp;</span>
                                                </label>
                                            </div>
                                            <div class="rentmy-optional-service-content" id="rentmy_optional_service_content_${service.id}">
                                                <div class="rentmy-btn-toolbar">
                                                    <div class="rentmy-btn-group">`;
                if (!service.is_required) {
                    service.fee.amounts.forEach(amount => {
                        active_class = '';
                        active_input = '';
                        if (service.existing && service.existing.config.user_entered == amount) {
                            active_class = 'rentmy-btn-active';
                        }
                        html += `<button type="button" class="rentmy-btn rentmy-btn-amount ${active_class}" data-servie_id="${service.id}" value="${amount}">${service.fee.type != 'percentage' ? this.currency_symbol : ''}${amount}${service.fee.type == 'percentage' ? '%' : ''}</button>`;

                    });
                } else {
                    html += `
                        <label style="margin-top:7px ">${service.fee.type != 'percentage' ? this.currency_symbol : ''}${service.fee.amounts[0]}${service.fee.type == 'percentage' ? '%' : ''}</label>`;
                }
                let custom_inputed_value = '';
                if (service.input_custom == 1 && !service.is_required) {
                    let is_exist = false;
                    if (service.existing) {
                        service.fee.amounts.forEach(amount => {
                            if (service.existing.config.user_entered == amount) {
                                is_exist = true;
                            }
                        });
                    }
                    if (service.existing && !is_exist) {
                        custom_inputed_value = service.existing.config.user_entered;
                    }

                    html += `<button type="button" class="rentmy-btn rentmy-input-amount-btn ${service.existing && !is_exist ? 'rentmy-btn-active' : ''}" data-id="${service.id}">Input Amount</button>`;
                }
                html += `</div>`;
                if (service.options != null && service.options != "") {
                    html += `<select data-service_id="${service.id}" id="rentmy_additional_service_option_${service.id}">`;
                    html += `<option>--select--</option>`;
                    service.options.split(';').forEach(option => {
                        html += `<option ${(service.existing && (service.existing.config.selected_option == option)) ? 'selected' : ''}>${option}</option>`;
                    });
                    html += `</select>`;

                }
                html += `</div>
                            <div class="rentmy-input-ammount-area" id="rentmy_input_amount_area_${service.id}">
                                  <div class="rentmy-input-group">
                                       <input type="text" value="${custom_inputed_value}"/>
                                       <div class="rentmy-input-group-append">
                                            <button type="button" class="rentmy-btn rentmy-optional-ok-btn" data-service_id="${service.id}"><i class="lni lni-checkmark"></i></button>
                                            <button type="button" class="rentmy-btn rentmy-optional-cancel-btn" data-service_id="${service.id}"><i class="lni lni-close"></i></button>
                                       </div>
                                  </div>
                                </div>
                             </div>
                          </div>`;
            });

            html += `</div></div></div>`;
            $(RentMy.globalSelector).find('.rentmy-checkout-ordersummery .rentmy-optional-service').html(html);
        },
        update_additional_services: function (service_id, charge_amount = '') {
            let ref = this;
            this.cartable_additional_services.map(function (service) {
                if (service.id == service_id) {
                    let service_data = ref.additional_services.filter(additional_service => {
                        return additional_service.id == service_id;
                    });
                    service.value = charge_amount != '' ? charge_amount : service_data[0].fee.amounts[0];
                    service.is_selected = true;
                }
            });
            this.addAdditionalChargeToCart();
        },

        set_additional_services_option: function (service_id, option_value) {
            this.cartable_additional_services.map(function (service) {
                if (service.id == service_id) {
                    service.selected_option = option_value;
                }
            });
            this.addAdditionalChargeToCart();
        },
        remove_additional_service_from_cart: function (service_id) {
            this.cartable_additional_services.map(function (service) {
                if (service.id == service_id) {
                    return service.is_selected = false;
                }
            });
            this.addAdditionalChargeToCart();
        },

        addAdditionalChargeToCart: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'add_additional_service_to_cart',
                additional_charges: JSON.stringify(ref.cartable_additional_services),
            })
            try {
                let data = JSON.parse(responseRaw);
                await ref.getAdditionalServices();
                await ref.getCartData();

            } catch (e) {}
        },
        // Payment getways
        getPaymentMethods: async function () {
            let ref = this;
            const responseRaw = await RentMy.helpers.ajaxPost({
                action_type: 'get_payment_methods',
            })
            try {
                let data = JSON.parse(responseRaw);

                ref.payment_getways = data.data;

                ref.bindPaymentMethods();
            } catch (e) {}
        },

        isPartialRequired: function (gateway) {
            if ((gateway.type !== 'online') && !gateway.config) return false;
            if ((gateway.type !== 'online') && !gateway.config.is_paid) return false;
            if (!this.payment_config || !this.cart.booking) return false;
            let pc = this.payment_config;
            return (pc.type == "percent" && pc.booking < 100 && pc.booking > 0) || (pc.type == "fixed" && pc.booking < this.cart.total && pc.booking > 0);
        },
        bindPaymentMethods: function () {

            let ref = this;
            let html = '';
            let down_payment_text = '';
            if (ref.payment_config){
                down_payment_text = `A ${ref.payment_config.type != 'percent' ? ref.currency_symbol : ''}${ref.payment_config.booking}${ref.payment_config.type == 'percent' ? '%' : ''} down payment is required to secure your reservation. Please choose an option and pay to proceed.`;

            }


            ref.payment_getways.forEach(getway => {
                let is_partial_payment = ref.isPartialRequired(getway);
                if (getway.status == 1) {
                    if (getway.online_type == 'card') {
                        html += `<div class="rentmy-collaps-item rentmy-payment-collaps-item">
                                        <div class="rentmy-collaps-btn rentmy-payment-collaps-btn">
                                            <div class="rentmy-radio-inline">
                                                <label class="rentmy-radio" for="rentmy-payment-${getway.id}">
                                                    <input type="radio"  id="rentmy-payment-${getway.id}" name="rentmy-payment-method" value="card" data-id="${getway.id}" data-name="${getway.name}" data-type="1" data-gateway_type="${getway.type}"/> Credit Card <span></span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="rentmy-collaps-content rentmy-payment-collaps-content" id="rentmy-payment-content-${getway.id}">`;
                        if (is_partial_payment) {
                            html += `<div class="rentmy-down-payment">
                                            <p>${down_payment_text}</p>
                                            <div class="rentmy-radio-inline">
                                                 <label class="rentmy-radio" for="rentmy-pay-full-amount-${getway.id}">
                                                    <input type="radio" class="rentmy-partial-payments" id="rentmy-pay-full-amount-${getway.id}" name="rentmy-partial-payment-${getway.id}" data-gateway_id="${getway.id}" value="full" checked/> Pay full amount due <span></span>
                                                </label>
                                                 <label class="rentmy-radio" for="rentmy-pay-partial-amount-${getway.id}">
                                                    <input type="radio" class="rentmy-partial-payments" id="rentmy-pay-partial-amount-${getway.id}" name="rentmy-partial-payment-${getway.id}" data-gateway_id="${getway.id}" value="partial" /> Pay ${ref.currency_symbol}${ref.cart.booking} now <span></span>
                                                </label>

                                            </div>
                                            <div class="rentmy-column-12">
                                                        <div class="rentmy-form-group">
                                                            <label> Amount to pay* </label>
                                                            <input type="text" name="amount_to_pay" value="${ref.amount_to_pay}"/>
                                                        </div>
                                                    </div>

                                         </div>`;
                        }
                        html += `<div class="rentmy-payment-form">
                                                <div class="rentmy-row">
                                                    <div class="rentmy-column-12">
                                                        <div class="rentmy-form-group">
                                                            <label> Name on Card* </label>
                                                            <input placeholder="Name on Card " type="text" name="card_name"/>
                                                        </div>
                                                    </div>
                                                    <div class="rentmy-column-12">
                                                        <div class="rentmy-form-group">
                                                            <label> Card Number* </label>
                                                            <input placeholder="Card Number " type="text" name="card_number"/>
                                                        </div>
                                                    </div>
                                                    <div class="rentmy-column-6">
                                                        <div class="rentmy-form-group">
                                                            <select name="card_exp_month">
                                                                <option value="">-Select Month-</option>
                                                                <option value="01">01 January</option>
                                                                <option value="02">02 February</option>
                                                                <option value="03">03 March</option>
                                                                <option value="04">04 April</option>
                                                                <option value="05">05 May</option>
                                                                <option value="06">06 June</option>
                                                                <option value="07">07 July</option>
                                                                <option value="08">08 August </option>
                                                                <option value="09">09 September </option>
                                                                <option value="10">10 October </option>
                                                                <option value="11">11 November</option>
                                                                <option value="12">12 December</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="rentmy-column-6">
                                                        <div class="rentmy-form-group">
                                                            <select name="card_exp_year">
                                                                <option value="">-Select Year-</option>
                                                                <option value="20">2020</option>
                                                                <option value="21">2021</option>
                                                                <option value="22">2022</option>
                                                                <option value="23">2023</option>
                                                                <option value="24">2024</option>
                                                                <option value="25">2025</option>
                                                                <option value="26">2026</option>
                                                                <option value="27">2027</option>
                                                                <option value="28">2028</option>
                                                                <option value="29">2029</option>
                                                                <option value="30">2030</option>
                                                                <option value="31">2031</option>
                                                                <option value="32">2032</option>
                                                                <option value="33">2033</option>
                                                                <option value="34">2034</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="rentmy-column-12">
                                                        <div class="rentmy-form-group">
                                                            <label>CVV Number*</label>
                                                            <input placeholder="CVV Number " type="text" name="card_cvv2"/>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;
                    } else {
                        html += `<div class="rentmy-collaps-item rentmy-payment-collaps-item">
                                        <div class="rentmy-collaps-btn rentmy-payment-collaps-btn">
                                            <div class="rentmy-radio-inline">
                                                <label class="rentmy-radio" for="rentmy-payment-${getway.id}">
                                                    <input type="radio" id="rentmy-payment-${getway.id}" name="rentmy-payment-method" data-type="2" data-note="${getway.config.add_note ?? false}" data-is_paid="${getway.config.is_paid ?? false}" data-id="${getway.id}" data-name="${getway.name}" data-gateway_type="${getway.type}"/>${getway.name} <span></span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="rentmy-collaps-content rentmy-payment-collaps-content" id="rentmy-payment-content-${getway.id}">
                                            <div class="renmty-checkout-shipping">
                                                `;
                        if (is_partial_payment) {
                            html += `<div class="rentmy-down-payment">
                                            <p>${down_payment_text}</p>
                                            <div class="rentmy-radio-inline">
                                                 <label class="rentmy-radio" for="rentmy-pay-full-amount-${getway.id}">
                                                    <input type="radio" class="rentmy-partial-payments" id="rentmy-pay-full-amount-${getway.id}" name="rentmy-partial-payment-${getway.id}" data-gateway_id="${getway.id}" value="full" checked/> Pay full amount due <span></span>
                                                </label>
                                                 <label class="rentmy-radio" for="rentmy-pay-partial-amount-${getway.id}">
                                                    <input type="radio" class="rentmy-partial-payments" id="rentmy-pay-partial-amount-${getway.id}" name="rentmy-partial-payment-${getway.id}" data-gateway_id="${getway.id}" value="partial" /> Pay ${ref.currency_symbol}${ref.cart.booking} now <span></span>
                                                </label>

                                            </div>
                                            <div class="rentmy-column-12">
                                                        <div class="rentmy-form-group">
                                                            <label> Amount to pay* </label>
                                                            <input type="text" name="amount_to_pay" value="${ref.amount_to_pay}"/>
                                                        </div>
                                                    </div>

                                         </div>`;
                        } else if (getway.config.is_paid) {
                            html += `<div className="rentmy-column-12">
                                <div className="rentmy-form-group">
                                    <label> Amount to pay* </label>
                                    <input type="text" name="amount_to_pay" value="${ref.amount_to_pay}"/>
                                </div>
                            </div>`;
                        }
                        if (getway.config.add_note) {
                            html += `<div class="rentmy-column-12">
                                 <div class="rentmy-form-group">
                                  <label>Note</label>
                                   <input type="text" name="note"/>

                                  </div>`
                        }
                        html += `</div>
                                    </div>
                                        </div>
                                    </div>`;
                    }
                }
            });

            $(RentMy.globalSelector).find('.rentmy-checkout-payment .rentmy-collaps').html(html);
        },

        //    Place Order
        placeOrder: async function () {
            let errors = [];
            let ref = this;
            let is_billing_error = this.billingInfoValidation();
            if (is_billing_error) {
                errors.push('Please fill billing details properly.');
            }
            this.bindBillingErrors();
            let is_payment_error = this.paymentValidation();
            if (is_payment_error) {
                errors.push('Please fill payment details properly.');
            }
            this.bindPaymentError();
            let is_fulfillment_error = this.fulfillmentValidation();
            if (is_fulfillment_error) {
                errors.push('Please fill fullfillment details properly.');
            }
            this.bindFulfillmentError();
            let is_term_condition = $(RentMy.globalSelector).find('.rentmy-ordersummery-checkbox #rentmy-termcondition').prop('checked');
            if (!is_term_condition) {
                errors.push('You must accept terms & conditions.');
            }
            this.bindBasicErrors(errors);
            if (is_billing_error <= 0 && is_payment_error <= 0 && is_fulfillment_error <= 0 && is_term_condition) {
                let payload = {};
                // let preload = {...this.billing_info, ...this.shipping_info, ...this.delivery_info};
                let sel_fulfillment = $(RentMy.globalSelector).find('.rentmy-fulfillment input[name="rentmy_fulfillment"]:checked');
                payload['delivery'] = {};

                if (sel_fulfillment.val() === 'delivery') {
                    payload = {...this.billing_info, ...this.delivery_info};
                    payload['shipping_method'] = 2;
                    if (this.delivery_settings.charge_by_zone) {
                        payload['delivery'] = $(RentMy.globalSelector).find('.rentmy-delivery-cost input[type=radio]:checked').data('delivery');
                    } else {
                        payload['delivery'] = $(RentMy.globalSelector).find('#rentmy_delivery').val();
                    }

                }
                if (sel_fulfillment.val() === 'shipping') {
                    payload['delivery'] = $(RentMy.globalSelector).find('.rentmy-shipping-methods input[type=radio]:checked').data('delivery');
                    payload = {...this.billing_info, ...this.shipping_info};
                    payload['shipping_method'] = 1;
                }
                if (sel_fulfillment.val() === 'pickup') {
                    payload = {...this.billing_info, ...this.shipping_info};
                    let location_id = $(RentMy.globalSelector).find('.rentmy-pickup-location-list input[type=radio]:checked').val();
                    payload['delivery'] = $(RentMy.globalSelector).find('.rentmy-pickup-location-list input[type=radio]:checked').data('delivery');
                    payload['rm_instore_loc'] = location_id;

                }
                let sel_payment = $(RentMy.globalSelector).find('.rentmy-checkout-payment input[name="rentmy-payment-method"]:checked');

                payload['gateway_id'] = sel_payment.data('id');
                payload['payment_gateway_type'] = sel_payment.data('gateway_type');
                payload['payment_gateway_id'] = sel_payment.data('id');
                payload['payment_gateway_name'] = sel_payment.data('name');
                let payment_content = $('#rentmy-payment-content-' + payload['gateway_id']);
                if (sel_payment.data('gateway_type') === 'offline') {
                    if (sel_payment.data('is_paid') == 1) {
                        payload['amount'] = payment_content.find('input[name=amount_to_pay]').val();
                    }
                    payload['note'] = payment_content.find('input[name=note]').val();
                } else {
                    payload['amount'] = payment_content.find('input[name=amount_to_pay]').val();
                    payload['card_name'] = payment_content.find('input[name=card_name]').val();
                    payload['card_no'] = payment_content.find('input[name=card_number]').val();
                    payload['exp_month'] = payment_content.find('select[name=card_exp_month]').val();
                    payload['exp_year'] = payment_content.find('select[name=card_exp_year]').val();
                    payload['cvv'] = payment_content.find('input[name=card_cvv2]').val();
                }
                payload['action_type'] = 'createOrder';
                payload['type'] = 2;
                payload['signature'] = this.signature_pad != '' ? this.signature_pad.toDataURL('image/jpeg', 0.5) : '';

                const responseRaw = await RentMy.helpers.ajaxPost(payload)
                try {
                    let data = JSON.parse(responseRaw);
                    window.location = ref.base_url + '/?page=order-complete';
                } catch (e) {}
            }
        },
        bindBasicErrors: function (errors) {
            let list = `<ul class="rentmy-error">`;
            errors.forEach(error => {
                list += `<li>${error}</li>`;
            });
            list += `</ul>`;
            $(RentMy.globalSelector).find('.rentmy-ordersummery-checkbox .rentmy-all-error').html(list);
        },
        fulfillmentValidation: function () {
            this.fulfillment_errors = [];
            let element = $(RentMy.globalSelector).find('.rentmy-fulfillment');
            let selected_method = element.find('input[type=radio]:checked');
            let is_fulfillment_selected = selected_method.length;
            if (is_fulfillment_selected <= 0) {
                this.fulfillment_errors.push("Please Select a fulfillment method");
            } else {
                if (selected_method.val() == 'pickup') {
                    if ($(RentMy.globalSelector).find('.rentmy-pickup-location-list input[type=radio]:checked').length <= 0) {
                        this.fulfillment_errors.push("Please Select a pickup location");
                    }

                }
                if (selected_method.val() == 'shipping') {
                    $(RentMy.globalSelector).find('.renmty-checkout-shipping input,select').map(function () {
                        if ($(this).attr('name') != undefined)
                            RentMy.checkout.shipping_info[$(this).attr('name')] = $(this).val();
                    });
                    let shipping_error_length = this.shippingValidation();
                    if ((shipping_error_length <= 0) && ($(RentMy.globalSelector).find('.rentmy-shipping-methods input[type=radio]:checked').length <= 0)) {
                        this.fulfillment_errors.push("Please select a shipping method");
                    }

                }
                if (selected_method.val() == 'delivery') {
                    $(RentMy.globalSelector).find('.renmty-checkout-delivery input,select').map(function () {
                        if ($(this).attr('name') != undefined)
                            RentMy.checkout.delivery_info[$(this).attr('name')] = $(this).val();
                    });
                    let shipping_error_length = this.deliveryValidation();
                    if (this.delivery_settings.charge_by_zone) {
                        if ((shipping_error_length <= 0) && ($(RentMy.globalSelector).find('.rentmy-delivery-cost input[type=radio]:checked').length <= 0)) {
                            this.fulfillment_errors.push("Please select delevery cost");
                        }
                    } else {
                        if ($(RentMy.globalSelector).find('#rentmy_delivery').length == 0) {
                            this.fulfillment_errors.push("Please Click on get delivery cost");
                        }
                    }
                }
            }

            return this.fulfillment_errors.length;
        },

        paymentValidation: function () {
            this.payment_errors = [];
            let element = $(RentMy.globalSelector).find('.rentmy-checkout-payment');
            let selected_method = element.find('input[name="rentmy-payment-method"]:checked');

            let is_payment_selected = selected_method.length;
            let gateway_id = selected_method.data('id');
            let selected_content = $(RentMy.globalSelector).find(`#rentmy-payment-content-${gateway_id}`);
            if (is_payment_selected <= 0) {
                this.payment_errors.push("Please Select a payment method");
            } else {
                let type = selected_method.data('type');
                if (type == 1) {
                    let amount_to_pay = selected_content.find('input[name="amount_to_pay"]').val();
                    let card_name = element.find('input[name="card_name"]').val();
                    let card_number = element.find('input[name="card_number"]').val();
                    let card_exp_month = element.find('select[name="card_exp_month"]').val();
                    let card_exp_year = element.find('select[name="card_exp_year"]').val();
                    let card_cvv2 = element.find('input[name="card_cvv2"]').val();
                    if (card_name == '') {
                        this.payment_errors.push("Name on your card is required");
                    }
                    if (card_number == '') {
                        this.payment_errors.push("Card Number is required");
                    }
                    if (card_exp_month == '' || card_exp_year == '') {
                        this.payment_errors.push("Card expiry month and year is required");
                    }
                    if (card_cvv2 == '') {
                        this.payment_errors.push("CVV number is required");
                    }
                    if (amount_to_pay == '') {
                        this.payment_errors.push("Amount to pay is required");
                    }
                }

                if (type == 2) {
                    is_paid = selected_method.data('is_paid');
                    if (is_paid) {
                        let amount_to_pay = selected_content.find('input[name="amount_to_pay"]').val();
                        if (amount_to_pay === '') {
                            this.payment_errors.push("Please enter some amount to pay");
                        }
                    }
                }

            }
            return this.payment_errors.length;
        },
        bindBillingErrors: function () {
            let list = `<ul class="rentmy-error">`;
            this.billing_error.forEach(error => {
                list += `<li>${error}</li>`;
            });
            list += `</ul>`;
            $(RentMy.globalSelector).find('.rentmy-billing-address .rentmy-billing-info-error').html(list);
        },
        bindPaymentError: function () {
            let list = `<ul class="rentmy-error">`;
            this.payment_errors.forEach(error => {
                list += `<li>${error}</li>`;
            });
            list += `</ul>`;
            $(RentMy.globalSelector).find('.rentmy-checkout-payment .rentmy-payment-error').html(list);
        },
        // Helper Functions
        priceFormat: function (priceVal) {
            return parseFloat(priceVal).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
        },
        imageLink: function (cart_item) {
            try {
                var imageLink = this.base_file_url + this.store_id + '/' + cart_item.product_id + '/' + cart_item.product.images[0].image_small;
            } catch (e) {
                var imageLink = this.asset_url + '/assets/img/default.jpg';
            }
            return imageLink;
        },
    },
    contact: {
        isInit: false,
        sendEmailFromContact: async function (data) {
            const responseRaw = await RentMy.helpers.ajaxPost(data)
            try {
                let response = JSON.parse(responseRaw);
                if (response.status === 'OK') {
                    $message = 'Thank you for contacting us.  We will be in touch shortly.';
                    RentMy.alert.successAlert($message);
                } else {
                    $message = 'The message may not have sent.  Please call us.';
                    RentMy.alert.errorAlert($message);
                }
            } catch (e) {
            }
        },
        isContactFromValid: function (data) {
            if (data.first_name.length === 0) {
                RentMy.alert.errorAlert('First name is empty');
                return false;
            }
            if (data.last_name.length === 0) {
                RentMy.alert.errorAlert('Last name is empty');
                return false;
            }
            if (data.phone.length === 0) {
                RentMy.alert.errorAlert('Phone is empty');
                return false;
            }
            if (data.phone.length) {
                let pattern = /^[- +()]*[0-9][- +()0-9]*$/;
                if (!pattern.test($('#phone').val())) {
                    RentMy.alert.errorAlert('Phone format is not valid');
                    return false;
                }
            }
            if (data.email.length === 0) {
                RentMy.alert.errorAlert('Email is empty');
                return false;
            }
            if (data.email.length) {
                let pattern = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,6})+$/;
                if (!pattern.test($('#email').val())) {
                    RentMy.alert.errorAlert('Email format is not valid');
                    return false;
                }
            }
            if (data.message.length === 0) {
                RentMy.alert.errorAlert('Message is empty');
                return false;
            }

            return true;
        },
        init: function () {
            this.isInit = true;

            //define a custom event
            window.dispatchEvent(new CustomEvent('rentMy.page.contact.init', {
                bubbles: true,
                detail: {
                    contact : this,
                }
            }))

            $(RentMy.globalSelector)
                .on('click', '#email-submit', function (e) {
                    const data = {
                        first_name: $('#first_name').val().trim(),
                        last_name: $('#last_name').val().trim(),
                        email: $('#email').val().trim(),
                        phone: $('#phone').val().trim(),
                        tempPhone: $('#phone').val().trim(),
                        message: $('#message').val().trim(),
                        action_type: 'send_contact_email',
                    };

                    if (RentMy.contact.isContactFromValid(data)) {
                        RentMy.contact.sendEmailFromContact(data)
                    }
                })
        }
    },
}

window.addEventListener('rentMy.request.details', rs => {
    console.log(rs.detail)
})

RentMy.init(PAGE || 'home')
