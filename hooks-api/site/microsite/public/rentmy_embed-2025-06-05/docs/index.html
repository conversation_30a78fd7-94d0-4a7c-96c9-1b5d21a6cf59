<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css">
</head>

<body>
    <div class="container my-5">
        <h1 class="text-center">Documentation</h1>

        <div class="accordion" id="accordionExample">
            <!-- Necessary Files -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingOne">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        <strong>Necessary Files</strong> &nbsp;cdn
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        Copy-paste these <code>&lt;script&gt;</code> into your <code>&lt;head&gt;</code> to load our
                        Functionalities.
                        <code>
                            <pre>
&lt;script src="https://embed.rentmy.co/wix/rentmy.js"&gt;&lt;/script&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Catalog with Filter Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading2">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                        <strong>Catalog with Filters</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="heading2"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyWrapperProductList RentMyWrapper" RentMyData="limit=21"&gt;
    &lt;div class="RentMyProductRow RentMyProductListRow"&gt;
        &lt;div class="RentMyFilterArea" RentMyFilterArea&gt;
            &lt;div class="RentMyFilterAreaInner"&gt;
                &lt;div class="RentMyCategory"&gt;
                    &lt;h3 class="RentMyFilterTitle"&gt;Category&lt;/h3&gt;
                    &lt;div class="CategoryMenuList scrollbar"&gt;
                        &lt;ul class="CategoryMenu" RentMyFilterByCategory&gt;
                            &lt;li&gt;&lt;a href="#"&gt;&lt;span&gt;Choose One&lt;/span&gt; &lt;i class="fa fa-angle-right"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;
                &lt;/div&gt;

                &lt;div class="RentMyFilter"&gt;
                    &lt;div class="RentMyFilterList"&gt;
                        &lt;h3 class="RentMyFilterTitle"&gt;Filter&lt;/h3&gt;
                        &lt;div class="FilterCheckbox scrollbar" RentMyFilterByTag&gt;
                            &lt;label class="RentMyCheckbox"&gt;
                                &lt;input type="checkbox"&gt;
                                &lt;span&gt;&nbsp;&nbsp;&lt;/span&gt;
                                &lt;a href="#"&gt;Price&lt;/a&gt;
                            &lt;/label&gt;
                            &lt;label class="RentMyCheckbox"&gt;
                                &lt;input type="checkbox"&gt;
                                &lt;span&gt;&nbsp;&nbsp;&lt;/span&gt;
                                &lt;a href="#"&gt;Quantity&lt;/a&gt;
                            &lt;/label&gt;
                            &lt;label class="RentMyCheckbox"&gt;
                                &lt;input type="checkbox"&gt;
                                &lt;span&gt;&nbsp;&nbsp;&lt;/span&gt;
                                &lt;a href="#"&gt;Name&lt;/a&gt;
                            &lt;/label&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;div class="RentMyPriceArea"&gt;
                        &lt;h3 class="RentMyFilterSubTitle"&gt;Price&lt;/h3&gt;
                        &lt;div class="RentMyPrice"&gt;
                            &lt;div class="RentMyRow" RentMyFilterByPrice&gt;
                                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                    &lt;label&gt;Min&lt;/label&gt;
                                    &lt;input type="text" class="RentMyInputField" RentMyMinPrice id/&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                    &lt;label&gt;Max&lt;/label&gt;
                                    &lt;input type="text" class="RentMyInputField" RentMyMaxPrice /&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyButtonGroup RentMyNotBetween"&gt;
                                    &lt;button class="RentMyBtn RentMyBtnBlack" RentMyMinMaxSubmitBtn&gt;Submit&lt;/button&gt;
                                    &lt;button class="RentMyBtn RentMyBtnRed" RentMyMinMaxClearBtn&gt;Clear&lt;/button&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="RentMyTypeArea"&gt;
                        &lt;h3 class="RentMyFilterSubTitle"&gt;Type&lt;/h3&gt;
                        &lt;div class="RentMyType" RentMyFilterByRentalType&gt;
                            &lt;label class="RentMyRadio"&gt;
                                &lt;input type="radio" name="RentalType" value="rent" /&gt;
                                Rent
                                &lt;span&gt;&lt;/span&gt;
                            &lt;/label&gt;
                            &lt;br /&gt;
                            &lt;label class="RentMyRadio"&gt;&lt;input type="radio" name="RentalType" value="buy" /&gt;
                                Buy
                                &lt;span&gt;&lt;/span&gt;
                            &lt;/label&gt;
                            &lt;br /&gt;
                            &lt;label class="RentMyRadio"&gt;
                                &lt;input type="radio" name="RentalType" value="all" /&gt;
                                All
                                &lt;span&gt;&lt;/span&gt;
                            &lt;/label&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;!-- Product Area --&gt;
        &lt;div class="RentMyProductArea"&gt;
            &lt;div class="RentMyRow SortProductRow"&gt;
                &lt;div class="SortProduct"&gt;
                    &lt;label&gt;Sort By&lt;/label&gt;
                    &lt;select class="RentMyInputField" RentMyShortByDropdown&gt;
                        &lt;option value="sort_by=product_name&sort_dir=ASC"&gt;Product name A-Z&lt;/option&gt;
                        &lt;option value="sort_by=product_name&sort_dir=DSC"&gt;Product name Z-A&lt;/option&gt;
                        &lt;option value="sort_by=rent_price&sort_dir=ASC"&gt;Rental price low to high&lt;/option&gt;
                        &lt;option value="sort_by=rent_price&sort_dir=DSC"&gt;Rental price high to low&lt;/option&gt;
                        &lt;option value="sort_by=buy_price&sort_dir=ASC"&gt;Sale price low to high&lt;/option&gt;
                        &lt;option value="sort_by=buy_price&sort_dir=DSC"&gt;Sale price high to low&lt;/option&gt;
                    &lt;/select&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyRow"&gt;
                &lt;div class="RentMyProductItem" RentMyProductItemSample&gt;
                    &lt;div class="RentMyProductItemInner"&gt;
                        &lt;div class="RentMyProductImg"&gt;
                            &lt;a href="#" RentMyProductImageUrl&gt;
                                &lt;img RentMyProductImage src="" class="ProductImg" alt="" /&gt;
                            &lt;/a&gt;
                            &lt;div class="RentMyProductOverlay"&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyProductBody"&gt;
                            &lt;h4 class="ProductName" RentMyProductName&gt;&lt;a href="#"&gt;{{ product_name }}&lt;/a&gt;&lt;/h4&gt;
                            &lt;h5 class="ProductPrice" RentMyProductPrice&gt;{{ product_price }}&lt;/h5&gt;
                            &lt;div class="ProductButton"&gt;
                                &lt;a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn&gt;View Details&lt;/a&gt;
                                &lt;button class="ProductCartBtn" href="#" RentMyAddToCartBtn&gt;Add to Cart&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Catalog without Filter Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading14">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse14" aria-expanded="false" aria-controls="collapse14">
                        <strong>Catalog without Filter</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse14" class="accordion-collapse collapse" aria-labelledby="heading14"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyWrapperProductList RentMyWrapper" RentMyData="limit=21"&gt;
    &lt;div class="RentMyProductRow RentMyProductListRow"&gt;
        &lt;!-- Product Area --&gt;
        &lt;div class="RentMyProductArea"&gt;
            &lt;div class="RentMyRow SortProductRow"&gt;
                &lt;div class="SortProduct"&gt;
                    &lt;label&gt;Sort By&lt;/label&gt;
                    &lt;select class="RentMyInputField" RentMyShortByDropdown&gt;
                        &lt;option value="sort_by=product_name&sort_dir=ASC"&gt;Product name A-Z&lt;/option&gt;
                        &lt;option value="sort_by=product_name&sort_dir=DSC"&gt;Product name Z-A&lt;/option&gt;
                        &lt;option value="sort_by=rent_price&sort_dir=ASC"&gt;Rental price low to high&lt;/option&gt;
                        &lt;option value="sort_by=rent_price&sort_dir=DSC"&gt;Rental price high to low&lt;/option&gt;
                        &lt;option value="sort_by=buy_price&sort_dir=ASC"&gt;Sale price low to high&lt;/option&gt;
                        &lt;option value="sort_by=buy_price&sort_dir=DSC"&gt;Sale price high to low&lt;/option&gt;
                    &lt;/select&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyRow"&gt;
                &lt;div class="RentMyProductItem" RentMyProductItemSample&gt;
                    &lt;div class="RentMyProductItemInner"&gt;
                        &lt;div class="RentMyProductImg"&gt;
                            &lt;a href="#" RentMyProductImageUrl&gt;
                                &lt;img RentMyProductImage src="" class="ProductImg" alt="" /&gt;
                            &lt;/a&gt;
                            &lt;div class="RentMyProductOverlay"&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyProductBody"&gt;
                            &lt;h4 class="ProductName" RentMyProductName&gt;&lt;a href="#"&gt;{{ product_name }}&lt;/a&gt;&lt;/h4&gt;
                            &lt;h5 class="ProductPrice" RentMyProductPrice&gt;{{ product_price }}&lt;/h5&gt;
                            &lt;div class="ProductButton"&gt;
                                &lt;a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn&gt;View Details&lt;/a&gt;
                                &lt;button class="ProductCartBtn" href="#" RentMyAddToCartBtn&gt;Add to Cart&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Customized Catalog Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading15">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse15" aria-expanded="false" aria-controls="collapse15">
                        <strong>Customized Catalog</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse15" class="accordion-collapse collapse" aria-labelledby="heading15"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
RentMyData Supported keys:

limit=10
products=170720,186414,193913,
search=test\sproduct
category=a311cc3b66c011ea82610212d7dcece2
tags=668,4960,164,163
sortBy=product_name|rent_price|buy_price
sort=ASC|DESC
onLoad=paginate

Example: 
&lt;div class="RentMyWrapperProductList RentMyWrapper" RentMyData="limit=10"&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Product Details Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading3">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                        <strong>Product Details</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="heading3"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyWrapperProductDetails RentMyWrapper" RentMyData=""&gt;
    &lt;div class="RentMyProductDetailsRow"&gt;
        &lt;div class="RentMyProductDetilsImg"&gt;
            &lt;div class="RentMyProductDetailsImgList"&gt;
                &lt;ul RentMyProductImages&gt;
                    &lt;li class="ActiveImg"&gt;
                        &lt;img src="" alt="" /&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;img src="" alt="" /&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;img src="" alt="" /&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;img src="" alt="" /&gt;
                    &lt;/li&gt;
                &lt;/ul&gt;
            &lt;/div&gt;
            &lt;div class="RentMyProductDetailsImgShow"&gt;
                &lt;img RentMyProductImage src="" alt="" /&gt;
            &lt;/div&gt;
        &lt;/div&gt;

        &lt;div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo&gt;
            &lt;div class="product-payment-details"&gt;

                &lt;h2 class="RentMyProductName" RentMyProductName product_name&gt;Product Name&lt;/h2&gt;

                &lt;div class="RentMyBuyRentToggle" RentMyBuyRentToggle&gt;
                    &lt;label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch"&gt;
                        &lt;input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch /&gt;
                        &lt;div class="ToggleSwitchRound"&gt;&lt;/div&gt;
                    &lt;/label&gt;
                &lt;/div&gt;

                &lt;h2 class="RentMyProductPrice" RentMyProductPrice product_price_text&gt;$0.00&lt;/h2&gt;

                &lt;div class="RentMyRecurring" RentMyRecurring&gt;
                    &lt;h6 RecurringTitle&gt;Recurring Pricing&lt;/h6&gt;
                    &lt;ul RecurringList&gt;
                        &lt;li RecurringItem&gt;
                            Recurring
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RentMyVariant" RentMyVariant&gt;
                    &lt;h6 VariantTitle&gt;Rent My Variant Sizes&lt;/h6&gt;
                    &lt;ul VariantList&gt;
                        &lt;li VariantItem&gt;Small&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RmRentalOption" RentMyRentalDateRange&gt;
                    &lt;h6 RentalDateRangeTitle&gt;Rental Date Range&lt;/h6&gt;
                    &lt;ul RentalDateRangeList&gt;
                        &lt;li class="" RentalDateRangeItem activeClass="RmDaterangeActive"&gt;
                            &lt;div class="RmRentalOptionDaytime" PricePreText&gt; 1 day &lt;/div&gt;
                            &lt;div Price&gt; $10.00 &lt;/div&gt;
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RentMyRentalDateRange mb-0" RentMyRentalEndDate&gt;
                    &lt;ul&gt;
                        &lt;li class="mt-0" RentalEndDatePicker&gt;Pick Date Range&lt;/li&gt;
                    &lt;/ul&gt;

                    &lt;span RentalEndDateSelectedLabel&gt;
                        Today 09:00 AM
                    &lt;/span&gt;
                &lt;/div&gt;

                &lt;div class="RentMyExactSelectDuration" RentMyExactSelectDuration&gt;
                    &lt;h6 RentMyExactSelectDurationTitle&gt;
                        Select Duration
                    &lt;/h6&gt;
                    &lt;ul class="mb-4" RentMyExactSelectDurationList&gt;
                        &lt;li RentMyExactSelectDurationItem&gt;
                            Exact Select Duration
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RentMyExactSelectTime" RentMyExactSelectTime&gt;
                    &lt;h6 RentMyExactSelectTimeTitle&gt;
                        Select Exact Start time
                    &lt;/h6&gt;
                    &lt;ul class="mb-4" RentMyExactSelectTimeList&gt;
                        &lt;li RentMyExactSelectTimeItem&gt;
                            Extact Times
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RentMyDeliveryOptions" RentMyDeliveryOptions&gt;
                    &lt;h6 DeliveryOptionsTitle&gt;Delivery Options&lt;/h6&gt;
                    &lt;ul DeliveryOptionsList&gt;
                        &lt;li DeliveryOptionsItem&gt;Local Move&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="RentMySelectLocation" RentMySelectLocation&gt;
                    &lt;h6 SelectLocationTitle&gt;Select Location&lt;/h6&gt;
                    &lt;ul SelectLocationList&gt;
                        &lt;li SelectLocationItem&gt;Default location&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;

                &lt;div class="QuantityContainer" RentmyQuantityContainer&gt;
                    &lt;label QuantityContainerTitle&gt;Quantity&lt;/label&gt;
                    &lt;div class="QuantityBtn"&gt;
                        &lt;button class="RentMyBtn" QuantityDecrementBtn&gt;-&lt;/button&gt;
                        &lt;input type="text" autocomplete="off" name="qty" class="InputQuantity" NumberOfQuantity /&gt;
                        &lt;button class="RentMyBtn" QuantityIncrementBtn&gt;+&lt;/button&gt;
                    &lt;/div&gt;

                    &lt;small class="info"&gt;
                        &lt;span RentmyAvailableLabel&gt;Available&lt;/span&gt;:
                        &lt;span RentmyAvailableQty&gt;17&lt;/span&gt;
                    &lt;/small&gt;
                &lt;/div&gt;

                &lt;div class="RentMyCartBtnArea" RentMyCartBtnArea&gt;
                    &lt;button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn&gt;ADD TO CART&lt;/button&gt;
                &lt;/div&gt;

            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;div class="RentMyProductDescription"&gt;
        &lt;h3 class="RentMyProductDesTitle"&gt;Product Description&lt;/h3&gt;
        &lt;div class="RentMyProductDesBody" RentMyProductDescription&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Package Details Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading4">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                        <strong>Package Details</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="heading4"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyWrapperPackageDetails RentMyWrapper" RentMyData=""&gt;
    &lt;div RentmyPackageComponent&gt;
        &lt;div class="RentMyProductPackageRow"&gt;
            &lt;div class="RentMyProductDetilsImg"&gt;
                &lt;div class="RentMyProductDetailsImgList"&gt;
                    &lt;ul RentMyProductImages&gt;
                        &lt;li class="ActiveImg"&gt;
                            &lt;img src="" alt="" /&gt;
                        &lt;/li&gt;
                        &lt;li&gt;
                            &lt;img src="" alt="" /&gt;
                        &lt;/li&gt;
                        &lt;li&gt;
                            &lt;img src="" alt="" /&gt;
                        &lt;/li&gt;
                        &lt;li&gt;
                            &lt;img src="" alt="" /&gt;
                        &lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
                &lt;div class="RentMyProductDetailsImgShow"&gt;
                    &lt;img RentMyProductImage src="" alt="" /&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo&gt;
                &lt;div class="product-payment-details"&gt;

                    &lt;h2 class="RentMyProductName" RentMyProductName&gt;{{ product_name }}&lt;/h2&gt;

                    &lt;div class="RentMyBuyRentToggle" RentMyBuyRentToggle&gt;
                        &lt;label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch"&gt;
                            &lt;input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch /&gt;
                            &lt;div class="ToggleSwitchRound"&gt;&lt;/div&gt;
                        &lt;/label&gt;
                    &lt;/div&gt;

                    &lt;h2 class="RentMyProductPrice" RentMyProductPrice&gt;{{ product_price_text }}&lt;/h2&gt;


                    &lt;div class="RentMyRecurring" RentMyRecurring&gt;
                        &lt;h6 RecurringTitle&gt;Recurring Pricing&lt;/h6&gt;
                        &lt;ul RecurringList&gt;
                            &lt;li RecurringItem&gt;
                                Recurring
                            &lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyVariant" RentMyVariant&gt;
                        &lt;h6 VariantTitle&gt;Rent My Variant Sizes&lt;/h6&gt;
                        &lt;ul VariantList&gt;
                            &lt;li VariantItem&gt;Small&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyProductOptions" RentMyProductOptions&gt;
                        &lt;div class="CustomFieldInner"&gt;
                            &lt;h6 ProductOptionsTitle&gt;RentMy Product Options&lt;/h6&gt;
                            &lt;ul ProductOptionsList&gt;
                                &lt;li ProductOptionsItem&gt;Option 001 (+$2.00)&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyRentalStartDate" RentMyRentalStartDate&gt;
                        &lt;div usualDateRange&gt;
                            &lt;h6 RentalStartDateTitle&gt;Select Rental Start Date&lt;/h6&gt;
                            &lt;ul RentalStartDateList&gt;
                                &lt;li Today&gt;Today&lt;/li&gt;
                                &lt;li Tomorrow&gt;Tomorrow&lt;/li&gt;
                                &lt;li PickDate&gt;Pick Date&lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;span RentalStartDateSelectedLabel&gt;
                                Today 08:00 AM
                            &lt;/span&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyRentalDateRange" RentMyRentalDateRange&gt;
                        &lt;h6 RentalDateRangeTitle&gt;
                            Rental Date Range
                        &lt;/h6&gt;
                        &lt;ul RentalDateRangeList&gt;
                            &lt;li RentalDateRangeItem&gt;
                                1 hour &lt;br /&gt;
                                $10.00
                            &lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyRentalDateRange" RentMyRentalEndDate&gt;
                        &lt;ul&gt;
                            &lt;li class="mt-0" RentalEndDatePicker&gt;End Date&lt;/li&gt;
                        &lt;/ul&gt;

                        &lt;span RentalEndDateSelectedLabel&gt;
                            Today 09:00 AM
                        &lt;/span&gt;
                    &lt;/div&gt;

                    &lt;div class="RentMyExactSelectDuration" RentMyExactSelectDuration&gt;
                        &lt;h6 RentMyExactSelectDurationTitle&gt;
                            Select Duration
                        &lt;/h6&gt;
                        &lt;ul RentMyExactSelectDurationList&gt;
                            &lt;li class="ExactSelectDurationActive" RentMyExactSelectDurationItem&gt;
                                Exact Select Duration
                            &lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;

                    &lt;div class="RentMyExactSelectTime" RentMyExactSelectTime&gt;
                        &lt;h6 RentMyExactSelectTimeTitle&gt;
                            Select Exact Start time
                        &lt;/h6&gt;
                        &lt;ul RentMyExactSelectTimeList&gt;
                            &lt;li class="ExactSelectTimeActive" RentMyExactSelectTimeItem&gt;
                                Extact Times
                            &lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyDeliveryOptions" RentMyDeliveryOptions&gt;
                        &lt;h6 DeliveryOptionsTitle&gt;Delivery Options&lt;/h6&gt;
                        &lt;ul DeliveryOptionsList&gt;
                            &lt;li DeliveryOptionsItem&gt;Local Move&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMySelectLocation" RentMySelectLocation&gt;
                        &lt;h6 SelectLocationTitle&gt;Select Location&lt;/h6&gt;
                        &lt;ul SelectLocationList&gt;
                            &lt;li SelectLocationItem&gt;Default location&lt;/li&gt;
                        &lt;/ul&gt;
                    &lt;/div&gt;


                    &lt;div class="QuantityContainer" RentmyQuantityContainer&gt;
                        &lt;label QuantityContainerTitle&gt;Quantity&lt;/label&gt;
                        &lt;div class="QuantityBtn"&gt;
                            &lt;button class="RentMyBtn" QuantityDecrementBtn&gt;-&lt;/button&gt;
                            &lt;input type="text" autocomplete="off" name="qty" class="InputQuantity"
                                NumberOfQuantity /&gt;
                            &lt;button class="RentMyBtn" QuantityIncrementBtn&gt;+&lt;/button&gt;
                        &lt;/div&gt;

                        &lt;div class="info"&gt;
                            &lt;p&gt;
                                &lt;span RentmyAvailableLabel&gt;Available:&lt;/span&gt;
                                &lt;span RentmyAvailableQty&gt;17&lt;/span&gt;
                            &lt;/p&gt;
                            &lt;div RentmyAvailableNotice&gt;&lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;


                    &lt;div class="RentMyCartBtnArea" RentMyCartBtnArea&gt;
                        &lt;button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn&gt;ADD TO CART&lt;/button&gt;
                    &lt;/div&gt;

                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyProductPackageArea" RentMyProductPackageArea&gt;
                &lt;div class="RentMyProductPackageAreaInner" RentMyProductPackageAreaInner&gt;
                    &lt;h6 RentMyProductPackageAreaTitle&gt;Package includes&lt;/h6&gt;
                    &lt;div RentMyProductPackageContent&gt;&lt;/div&gt;
                    &lt;div class="PackageSingleProduct" PackageSingleProduct&gt;
                        &lt;div class="PackageProductName" PackageProductName&gt;
                            &lt;h5 PackageProductNameTitle&gt;test product buy (2)&lt;/h5&gt;
                        &lt;/div&gt;
                        &lt;div class="PakageProductVarient" PakageProductVarient&gt;
                            &lt;div class="PakageProductVarientInner" PakageProductVarientInner&gt;
                                &lt;select class="form-control" PakageProductVarientInnerSelect&gt;
                                    &lt;option PakageProductVarientInnerOption value="276077"&gt; size: Blue, color:
                                        red &lt;/option&gt;
                                &lt;/select&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="RentMyProductDescription"&gt;
        &lt;h3 class="RentMyProductDesTitle"&gt;Product Description&lt;/h3&gt;
        &lt;div class="RentMyProductDesBody" RentMyProductDescription&gt;
        &lt;/div&gt;
    &lt;/div&gt;
    &lt;div class="RentMyRelatedProduct"&gt;
        &lt;h3 class="RentMyRelatedProductTitle"&gt;Related Products&lt;/h3&gt;
        &lt;div class="RentMyRelatedProductBody"&gt;
            &lt;div class="RentMyRow" RentMyRelatedProducts&gt;
                &lt;div class="RentMyProductItem" RentMyProductItem&gt;
                    &lt;div class="RentMyProductItemInner"&gt;
                        &lt;div class="RentMyProductImg"&gt;
                            &lt;a href="#" RentMyProductImageUrl&gt;
                                &lt;img RentMyProductImage src="" class="ProductImg" alt="" /&gt;
                            &lt;/a&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyProductBody"&gt;
                            &lt;h4 class="ProductName" RentMyProductName&gt; Product_name &lt;/h4&gt;
                            &lt;h5 class="ProductPrice" RentMyProductPrice&gt; product_price &lt;/h5&gt;
                            &lt;div class="ProductButton"&gt;
                                &lt;a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn&gt;View Details&lt;/a&gt;
                                &lt;button class="ProductCartBtn" href="#" RentMyAddToCartBtn&gt;Add to Cart&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Bottom Mini Cart Widget -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading7">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse7" aria-expanded="false" aria-controls="collapse7">
                        <strong>Bottom Mini Cart</strong> &nbsp;Widget
                    </button>
                </h2>
                <div id="collapse7" class="accordion-collapse collapse" aria-labelledby="heading7"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyWrapperInpageCartWidget RentMyWrapper"&gt;&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Cart Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading5">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
                        <strong>Cart</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse5" class="accordion-collapse collapse" aria-labelledby="heading5"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div class="RentMyCartWrapper RentMyWrapper"&gt;
    &lt;div class="container-fullwidth" InsideContainer&gt;
        &lt;div class="RentMyAddonProducts RentMyRelatedProduct" RentMyRelatedProducts&gt;
            &lt;h3 class="RentMyRelatedProductTitle"&gt;Add-On Products&lt;/h3&gt;
            &lt;div class="RentMyRelatedProductBody"&gt;
                &lt;div class="RentMyRow"&gt;
                    &lt;div class="RentMyProductItem" RentMyProductItem&gt;
                        &lt;div class="RentMyProductItemInner"&gt;
                            &lt;div class="RentMyProductImg"&gt;
                                &lt;a href="#" RentMyProductImageUrl&gt;
                                    &lt;img RentMyProductImage src="" class="ProductImg" alt="product img" /&gt;
                                &lt;/a&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyProductBody"&gt;
                                &lt;h4 class="ProductName" RentMyProductName&gt; Product_name &lt;/h4&gt;
                                &lt;h5 class="ProductPrice" RentMyProductPrice&gt; product_price &lt;/h5&gt;
                                &lt;div class="ProductButton"&gt;
                                    &lt;a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn&gt;View Details&lt;/a&gt;
                                    &lt;button class="ProductCartBtn" href="#" RentMyAddToCartBtn&gt;Add to
                                        Cart&lt;/button&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;

        &lt;div class="RentMyRow" RenatalDateArea&gt;
            &lt;div class="RentMyFullwidth"&gt;
                &lt;span class="RentMyCartDateRange" DateRange&gt;
                    &lt;b&gt;Rental Dates&lt;/b&gt; &lt;span DateText&gt;11/22/2023&lt;/span&gt; &lt;i class="fa fa-edit date-editicon"
                        EditDate&gt;&lt;/i&gt;
                &lt;/span&gt;
                &lt;div class="RentMyDatePicker" id="RentMyDatePicker" DatePicker&gt;
                    &lt;button class="RentMyBtn RentMyBtnBlack" BtnCancel&gt;Cancel&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class="RentMyTableResponsive"&gt;
            &lt;table class="RentMyTable RentMyTableStriped RentMyCartTable" RentMyCartTable&gt;
                &lt;thead&gt;
                    &lt;tr&gt;
                        &lt;th&gt;&lt;/th&gt;
                        &lt;th&gt;&lt;/th&gt;
                        &lt;th&gt;Product&lt;/th&gt;
                        &lt;th&gt;Unit Price&lt;/th&gt;
                        &lt;th&gt;Quantity&lt;/th&gt;
                        &lt;th&gt;Subtotal&lt;/th&gt;
                    &lt;/tr&gt;
                &lt;/thead&gt;
                &lt;tbody&gt;
                    &lt;tr CartItem&gt;
                        &lt;td DeleteIconArea&gt;
                            &lt;a class="CartRemoveProduct"&gt;&lt;i class="fa fa-trash"&gt;&lt;/i&gt;&lt;/a&gt;
                        &lt;/td&gt;
                        &lt;td ImageArea&gt;
                            &lt;img test="ImageArea" src="" class="cart product img" alt="" /&gt;
                        &lt;/td&gt;
                        &lt;td ItemNameArea&gt;
                            Product1
                        &lt;/td&gt;
                        &lt;td ItemPrice&gt;
                            $50.00
                        &lt;/td&gt;
                        &lt;td IncrDecrArea&gt;
                            &lt;div class="QuantityContainer"&gt;
                                &lt;div class="QuantityBtn"&gt;
                                    &lt;button class="RentMyBtn" DecrementBtn&gt;-&lt;/button&gt;
                                    &lt;input type="text" autocomplete="off" name="qty" class="InputQuantity"
                                        QuantityInput /&gt;
                                    &lt;button class="RentMyBtn" IncrementBtn&gt;+&lt;/button&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/td&gt;
                        &lt;td ItemPriceArea&gt;
                            $255.00
                        &lt;/td&gt;
                    &lt;/tr&gt;
                &lt;/tbody&gt;
            &lt;/table&gt;
        &lt;/div&gt;
        &lt;div class="RentMyRow RentMyCartSummery"&gt;
            &lt;div class="RentMyHalfwidth"&gt;
                &lt;div class="RentMyCouponCode RentMyFlex"&gt;
                    &lt;input type="text" class="RentMyInputField" placeholder="Enter Coupon Code" CouponInput /&gt;
                    &lt;button type="submit" class="RentMyBtn RentMyBtnBlack" ApplyCouponBtn&gt;Apply Coupon&lt;/button&gt;
                &lt;/div&gt;
                &lt;div class="RentMyButtonGroup CheckoutMakeContinueBtn"&gt;
                    &lt;button class="RentMyBtn RentMyBtnBlack" ContinueShoppingBtn&gt;Continue Shopping&lt;/button&gt;
                    &lt;div class="MakeContinue"&gt;
                        &lt;button class="RentMyBtn RentMyBtnBlack" ProceedCheckoutBtn&gt;Proceed to Checkout&lt;/button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyHalfwidth"&gt;
                &lt;h4 class="RentMyCartTotal"&gt;Cart Totals&lt;/h4&gt;
                &lt;table class="RentMyTable RentMySummeryTable" RentMySummeryTable&gt;
                    &lt;tbody&gt;
                        &lt;tr&gt;
                            &lt;td&gt;Subtotal&lt;/td&gt;
                            &lt;td&gt;
                                &lt;span&gt;
                                    &lt;b SubtotalAmount&gt;€0.00&lt;/b&gt;
                                &lt;/span&gt;
                            &lt;/td&gt;
                        &lt;/tr&gt;
                        &lt;tr&gt;
                            &lt;td&gt;Deposit Amount&lt;/td&gt;
                            &lt;td&gt;&lt;span DepositeAmount&gt; €0.00&lt;/span&gt;&lt;/td&gt;
                        &lt;/tr&gt;
                        &lt;tr&gt;
                            &lt;td&gt;Shipping Charge&lt;/td&gt;
                            &lt;td&gt;&lt;small&gt; Calculated in the next step&lt;/small&gt;&lt;/td&gt;
                        &lt;/tr&gt;
                        &lt;tr&gt;
                            &lt;td&gt;Tax&lt;/td&gt;
                            &lt;td&gt;&lt;span TaxAmount&gt; €0.00&lt;/span&gt;&lt;/td&gt;
                        &lt;/tr&gt;
                        &lt;tr&gt;
                            &lt;td&gt;Delivery Tax&lt;/td&gt;
                            &lt;td&gt;&lt;small&gt; Calculated in the next step&lt;/small&gt;&lt;/td&gt;
                        &lt;/tr&gt;
                        &lt;tr&gt;
                            &lt;td&gt;
                                &lt;h5&gt;Total&lt;/h5&gt;
                            &lt;/td&gt;
                            &lt;td&gt;
                                &lt;h5&gt;&lt;span TotalAmount&gt;€0.00&lt;/span&gt;&lt;/h5&gt;
                            &lt;/td&gt;
                        &lt;/tr&gt;
                    &lt;/tbody&gt;
                &lt;/table&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Checkout Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading6">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse6" aria-expanded="false" aria-controls="collapse6">
                        <strong>Checkout</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse6" class="accordion-collapse collapse" aria-labelledby="heading6"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                           <pre>
&lt;div id="RentMyCheckoutWrapper" class="RentMyCheckoutWrapper RentMyWrapper"&gt;
    &lt;div class="RentMyRow"&gt;
        &lt;div class="CheckoutLeftSide"&gt;
            &lt;div class="ReturningCustomerTitle" SignInPopupArea&gt;
                &lt;h5&gt; Welcome to &lt;span StoreName&gt;store&lt;/span&gt; &lt;a SignIn&gt;(Sign in)&lt;/a&gt;&lt;/h5&gt;
            &lt;/div&gt;
            &lt;div class="BillingDetailsLeftside"&gt;
                &lt;div class="BillingDetailsLeftSideInner" BillingBorder&gt;
                    &lt;span class="BillingCheckoutTitle"&gt;Billing Details&lt;/span&gt;
                    &lt;form&gt;
                        &lt;div class="RentMyRow" BillingGeneralInfo&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv&gt;
                                &lt;label&gt; First Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" FirstName /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv&gt;
                                &lt;label&gt;Last Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" LastName /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" MobileDiv&gt;
                                &lt;label&gt;Mobile Number&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" Mobile /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" EmailDiv&gt;
                                &lt;label&gt;Email Name &lt;/label&gt;
                                &lt;input type="email" class="RentMyInputField" Email /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyFullwidth" CompanyDiv&gt;
                                &lt;label&gt;Company Name(Optional)&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" Company /&gt;
                            &lt;/div&gt;

                            &lt;div class="RentMyRow billing-details-checkbox" AllSavedBililingAddress&gt;
                                &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                    &lt;label class="RentMyRadio" SingleAddress&gt;
                                        This is a save address
                                        &lt;input type="radio" name="select_address" value="rent"&gt;
                                        &lt;span&gt;&lt;/span&gt;
                                    &lt;/label&gt;
                                    &lt;!-- Create New --&gt;
                                    &lt;label class="RentMyRadio" CreateAddress&gt;
                                        Create New
                                        &lt;input type="radio" name="select_address" value="rent"&gt;
                                        &lt;span&gt;&lt;/span&gt;
                                    &lt;/label&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;

                            &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                &lt;label&gt;Country&lt;/label&gt;
                                &lt;select class="RentMyInputField" Country&gt;
                                    &lt;option value=""&gt; Country Name &lt;/option&gt;
                                &lt;/select&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label&gt;Address Line 1&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" placeholder="Enter a location"
                                    AddressLine1 /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label&gt;Address Line 2&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" AddressLine2 /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label&gt;City&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" City /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label&gt;State&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" State /&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label&gt; Zipcode&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" Zipcode /&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;

                        &lt;div class="RentMyRow" BillingAdditionalInfo&gt;
                            &lt;div class="RentMyFullwidth"&gt;
                                &lt;div class="BillingCheckoutSubTitle"&gt;
                                    &lt;h5 Title&gt;
                                        &lt;i class="fa fa-plus"&gt;&lt;/i&gt;
                                        Additional Information
                                    &lt;/h5&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" SpecialComments&gt;
                                &lt;label&gt;Special Instructions/Comments&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" SpecialRequest&gt;
                                &lt;label&gt;Special Request&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" DrivingLicence&gt;
                                &lt;label&gt;Driving Licence&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField"&gt;
                            &lt;/div&gt;

                        &lt;/div&gt;
                        &lt;div class="RentMyRow" BillingCustomCheckoutInfo&gt;
                            &lt;div class="RentMyFullwidth"&gt;
                                &lt;div class="BillingCheckoutSubTitle"&gt;
                                    &lt;h5&gt;
                                        &lt;i class="fa fa-plus"&gt;&lt;/i&gt;
                                        Custom checkout information
                                    &lt;/h5&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth" CustomField&gt;&lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;
                &lt;/div&gt;
                &lt;div class="BillingDetailsLeftSideInner FullfillmentArea" FullfillmentArea FullfillmentBorder&gt;
                    &lt;h2 class="BillingCheckoutTitle" Title&gt;Fulfillment&lt;/h2&gt;
                    &lt;div class="FullfillmentTabArea"&gt;
                        &lt;div class="FullfillmentTabList"&gt;
                            &lt;ul&gt;
                                &lt;li PickupTab&gt;&lt;/li&gt;
                                &lt;li ShippingTab&gt;&lt;/li&gt;
                                &lt;li DeliveryTab&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/div&gt;
                        &lt;div class="FullfillmentTabContent"&gt;

                            &lt;div class="FullfillmentTabBody FullfillmentPickup" PickupLocations&gt;
                                &lt;div class="PickupLocationList" Location&gt;&lt;/div&gt;
                            &lt;/div&gt;

                            &lt;div class="FullfillmentTabBody FullfillmentShippingAndDelivery" ShipAndDelivery&gt;
                                &lt;div class="RentMyRow" SameAsAboveArea&gt;
                                    &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                        &lt;label class="RentMyCheckbox"&gt;
                                            &lt;input type="checkbox" SameAsAbove&gt;
                                            Same Address as Above
                                            &lt;span&gt;&lt;/span&gt;
                                        &lt;/label&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;

                                &lt;div class="FullfillmentTabBody FullfillmentPickup" ShippingAddressList&gt;
                                    &lt;div class="PickupLocationList" Address&gt;
                                        &lt;label class="RentMyRadio"&gt;
                                            &lt;input type="radio" name="shipping_address" value="rent"&gt;
                                            Default location (5627 Covehaven Dr, Dallas, TX, US)
                                            &lt;span&gt;&lt;/span&gt;
                                        &lt;/label&gt;
                                    &lt;/div&gt;
                                    &lt;div class="PickupLocationList" CreateNew&gt;
                                        &lt;label class="RentMyRadio"&gt;
                                            &lt;input type="radio" name="shipping_address" value="rent"&gt;
                                            Create New
                                            &lt;span&gt;&lt;/span&gt;
                                        &lt;/label&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;

                                &lt;form class="mt-4"&gt;
                                    &lt;div class="RentMyRow"&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv&gt;
                                            &lt;label&gt; First Name&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" FirstName /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv&gt;
                                            &lt;label&gt;Last Name&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" LastName /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth" MobileDiv&gt;
                                            &lt;label&gt;Mobile Number&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" Mobile /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth" EmailDiv&gt;
                                            &lt;label&gt;Email Name &lt;/label&gt;
                                            &lt;input type="email" class="RentMyInputField" Email /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyFullwidth" CompanyDiv&gt;
                                            &lt;label&gt;Company Name(Optional)&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" Company /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                            &lt;label&gt;Country&lt;/label&gt;
                                            &lt;select class="RentMyInputField" Country&gt;
                                                &lt;option value=""&gt; Country Name &lt;/option&gt;
                                            &lt;/select&gt;
                                        &lt;/div&gt;

                                        &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                            &lt;label&gt;Address Line 1&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField"
                                                placeholder="Enter a location" AddressLine1 /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                            &lt;label&gt;Address Line 2&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" AddressLine2 /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                            &lt;label&gt;City&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" City /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                            &lt;label&gt;State&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" State /&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                            &lt;label&gt; Zipcode&lt;/label&gt;
                                            &lt;input type="text" class="RentMyInputField" Zipcode /&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/form&gt;

                                &lt;div class="ShippingMethodArea" ShippingMethods&gt;
                                    &lt;h5 Title&gt;Select Shipping Method&lt;/h5&gt;
                                    &lt;div AllMethods&gt;
                                        &lt;div class="PickupLocationList" Method&gt;
                                            &lt;label class="RentMyRadio"&gt;
                                                &lt;input type="radio" name="shipping_methods" value="rent"&gt;
                                                Flat Rate
                                                &lt;b&gt;$33.99&lt;/b&gt;
                                                &lt;span&gt;&lt;/span&gt;
                                            &lt;/label&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;

                                &lt;div class="ShippingCostArea" DeliveryCosts&gt;
                                    &lt;h5 Title&gt;Delivery Cost&lt;/h5&gt;
                                    &lt;div class="PickupLocationList" Cost&gt;
                                        &lt;label class="RentMyRadio"&gt;
                                            &lt;input type="radio" name="delivery_cost" value="rent"&gt;
                                            Zone A
                                            &lt;b&gt;$33.99&lt;/b&gt;
                                            &lt;span&gt;&lt;/span&gt;
                                        &lt;/label&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;

                                &lt;div class="DeliveryAddressMsg" DeliveryOutsideAreaMsg&gt;
                                    Your address is outside of our delivery area. Please contact us to make
                                    other arrangements.
                                &lt;/div&gt;
                                &lt;div class="DeliveryAddressErrorMsg" DeliveryAddressErrorMsg&gt;
                                    Delivery is not possible for your address
                                &lt;/div&gt;

                                &lt;div class="RentMyRow mt-3"&gt;
                                    &lt;div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea"&gt;
                                        &lt;button type="button" class="RentMyBtn RentMyGetShippingBtn"
                                            GetShippingMethodsBtn&gt;Get shipping method &lt;i
                                                class="fa fa-arrow-right"&gt;&lt;/i&gt;&lt;/button&gt;
                                    &lt;/div&gt;
                                    &lt;div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea"&gt;
                                        &lt;button type="button" class="RentMyBtn RentMyGetShippingBtn"
                                            GetDeliveryCostBtn&gt;Get delivery cost &lt;i
                                                class="fa fa-arrow-right"&gt;&lt;/i&gt;&lt;/button&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class="CheckoutRightSide" OrderSummary&gt;
            &lt;div class="OrderReviewWrapper" Contents&gt;
                &lt;span class="OrderReviewTitle"&gt;Your Order Summary&lt;/span&gt;
                &lt;div class="OrderReviewWrapperBody"&gt;
                    &lt;div class="OrderSummery"&gt;
                        &lt;div class="CheckoutDatetimeShow" CheckoutDatetime&gt;
                            12/21/2023 - 12/21/2023
                        &lt;/div&gt;
                        &lt;div class="CheckoutOrderList" CartItems&gt;
                            &lt;div class="CheckoutOrderItem" Item&gt;
                                &lt;div class="OrderItemImg"&gt;
                                    &lt;img src="" alt="order itemimg" class="img-fluid" /&gt;
                                &lt;/div&gt;
                                &lt;div class="OrderItemContent"&gt;
                                    &lt;div class="OrderName" ProductName&gt;AA&lt;/div&gt;
                                    &lt;div class="OrderQuantity" ProductQuantity&gt;( qty : 1 )&lt;/div&gt;
                                    &lt;div class="OrderOtherInfo"&gt;
                                        &lt;p class="qty" ProductVaraint&gt;test: 1&lt;/p&gt;
                                        &lt;p class="OrderItemPrice" ProductPrice&gt;
                                            Price: €0.00
                                        &lt;/p&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    &lt;table class="RentMyTable OrderSummaryTable"&gt;
                        &lt;tfoot&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Subtotal&lt;/th&gt;
                                &lt;td Subtotal&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Optional Services&lt;/th&gt;
                                &lt;td OptionalService&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Deposit Amount&lt;/th&gt;
                                &lt;td DepositAmount&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Tax&lt;/th&gt;
                                &lt;td TaxAmount&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Shipping Charge&lt;/th&gt;
                                &lt;td ShippingCharge&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="cart-subtotal"&gt;
                                &lt;th&gt;Delivery Tax&lt;/th&gt;
                                &lt;td DeliveryTax&gt;
                                    $0.00
                                &lt;/td&gt;
                            &lt;/tr&gt;
                            &lt;tr class="order-total"&gt;
                                &lt;th&gt;Total&lt;/th&gt;
                                &lt;td&gt;
                                    &lt;strong TotalAmount&gt;
                                        $0.00
                                    &lt;/strong&gt;
                                &lt;/td&gt;
                            &lt;/tr&gt;
                        &lt;/tfoot&gt;
                    &lt;/table&gt;

                    &lt;div class="RentMyOptionalService" AdditionalCharges&gt;
                        &lt;div class="RentMyRow"&gt;
                            &lt;div class="RentMyFullwidth"&gt;
                                &lt;div class="RentMyAdditionalChargeTitle"&gt;Optional Services&lt;/div&gt;
                            &lt;/div&gt;
                            &lt;!-- Start --&gt;
                            &lt;div class="RentMyFullwidth" SingleCharge&gt;
                                &lt;div class="RentMyRow"&gt;
                                    &lt;div class="RentMyCheckboxInline"&gt;
                                        &lt;label class="RentMyCheckbox" Title&gt;
                                            &lt;input type="checkbox"&gt;
                                            give donate
                                            &lt;span&gt;&lt;/span&gt;
                                        &lt;/label&gt;
                                    &lt;/div&gt;
                                    &lt;div class="RentMyOptionalServiceContent" FeeAmountsAndShowInputAmout&gt;
                                        &lt;div class="RentMyBtnToolbar"&gt;
                                            &lt;div class="RentMyBtnGroup"&gt;
                                                &lt;button type="button" class="RentMyGroupBtn"
                                                    FeeAmounts&gt;10%&lt;/button&gt;
                                                &lt;button type="button"
                                                    class="RentMyGroupBtn RentMyInputAmountBtn"
                                                    ShowInputAmount&gt;Input Amount&lt;/button&gt;
                                            &lt;/div&gt;
                                            &lt;select class="RentMyInputField" SelectOptions&gt;
                                                &lt;option&gt;op, op2&lt;/option&gt;
                                            &lt;/select&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyInputAmountArea" InputAmountArea&gt;
                                            &lt;div class="RentMyInputGroup"&gt;
                                                &lt;input class="RentMyInputField" type="text" InputAmount /&gt;
                                                &lt;div class="RentMyInputGroupAppend"&gt;
                                                    &lt;button type="button"
                                                        class="RentMyGroupBtn RentMyOptionalOkBtn" OkButton&gt;&lt;i
                                                            class="fa fa-check"&gt;&lt;/i&gt;&lt;/button&gt;
                                                    &lt;button type="button"
                                                        class="RentMyGroupBtn RentMyOptionalCancelBtn"
                                                        CancelButton&gt;&lt;i class="fa fa-times"&gt;&lt;/i&gt;&lt;/button&gt;
                                                &lt;/div&gt;
                                            &lt;/div&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;!-- End --&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;div class="checkout-checkbox"&gt;
                        &lt;div class="CreateCustomerCheckbox" IsCustomerAccountDiv&gt;
                            &lt;label class="RentMyCheckbox"&gt;
                                &lt;input type="checkbox" IsCustomerAccount&gt;
                                Create an account to make ordering faster in the future
                                &lt;span&gt;&lt;/span&gt;
                            &lt;/label&gt;
                        &lt;/div&gt;
                        &lt;div class="TermsConditionsCheckbox" TermsConditions&gt;
                            &lt;label class="RentMyCheckbox"&gt;
                                &lt;input type="checkbox"&gt;
                                I have read and agree with the &lt;a style="cursor: pointer;" ShowPopup&gt;terms &amp;
                                    conditions&lt;/a&gt;
                                &lt;span&gt;&lt;/span&gt;
                            &lt;/label&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;div class="SignatureContainer" SingnatureArea&gt;
                        &lt;div class="SignaturePad"&gt;
                            &lt;div class="SignaturePadBody"&gt;
                                &lt;canvas&gt;&lt;/canvas&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="SignatureFooter"&gt;
                            &lt;p&gt;Signature&lt;/p&gt;
                            &lt;div class="UndoClear"&gt;
                                &lt;a Clear&gt;Clear&lt;/a&gt;
                                &lt;a Undo&gt;Undo&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;div class="CheckoutBackcartPlaceorder"&gt;
                        &lt;a class="RentMyBtn RentMyBackCartBtn" href="#" BackToCartBtn&gt;&lt;i
                                class="fa fa-backward"&gt;&lt;/i&gt;
                            &nbsp;Back to Cart&lt;/a&gt;
                        &lt;button type="button" class="RentMyBtn RentMyPlaceOrder" PlaceOrderBtn successMessage=""
                            errorMessage=""&gt;Place
                            Order&lt;/button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                           </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Login Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading8">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse8" aria-expanded="false" aria-controls="collapse8">
                        <strong>Login</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse8" class="accordion-collapse collapse" aria-labelledby="heading8"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                           <pre>
&lt;div id="RentMyCustomerLoginContainer" class="RentMyWrapper"&gt;
    &lt;div class="LoginElement"&gt;
        &lt;h3 class="LoginTitle"&gt;Already Registered?&lt;/h3&gt;
        &lt;div class="RentMyAlertMessage"&gt;&lt;/div&gt;
        &lt;form class="RentMyFrom" id="RentMyCustomerLoginForm"&gt;
            &lt;div class="RentMyInputGroup"&gt;
                &lt;input type="text" name="email" class="RentMyInputField" placeholder="Email" /&gt;
            &lt;/div&gt;
            &lt;div class="RentMyInputGroup"&gt;
                &lt;input type="password" name="password" class="RentMyInputField" placeholder="Password" /&gt;
            &lt;/div&gt;
            &lt;div class="RentMyButtonGroup"&gt;
                &lt;button type="submit" class="RentMyBtn LoginBtn"&gt;Log in&lt;/button&gt;
                &lt;div class="RentMyButtonGroup"&gt;
                    &lt;a href="#" class="ForgotPassword" RentMyPageLink="reset_password"&gt;Forgot Password?&lt;/a&gt;
                    &lt;a href="#" class="NewAccount" RentMyPageLink="registration"&gt;Sign Up&lt;/a&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/div&gt;
                           </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Registration Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading9">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse9" aria-expanded="false" aria-controls="collapse9">
                        <strong>Registration</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse9" class="accordion-collapse collapse" aria-labelledby="heading9"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                           <pre>
&lt;div id="RentMyCustomerRegistrationContainer" class="RentMyWrapper"&gt;
    &lt;div class="RegistrationElement"&gt;
        &lt;h3 class="RegistrationTitle"&gt;Signup&lt;/h3&gt;
        &lt;div class="RentMyAlertMessage"&gt;&lt;/div&gt;
        &lt;form class="RentMyFrom" id="RentMyCustomerRegistrationForm"&gt;
            &lt;div class="RentMyRow"&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="first_name" class="RentMyInputField" placeholder="First name"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="last_name" class="RentMyInputField" placeholder="Last name"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                    &lt;input type="email" name="email" class="RentMyInputField" placeholder="Email"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="company_name" class="RentMyInputField" placeholder="Company name"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="contact" class="RentMyInputField" placeholder="Contact name"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="password" name="password" class="RentMyInputField" placeholder="Password"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="password" name="confirm_password" class="RentMyInputField"
                        placeholder="Password Confirm"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="address_line1" class="RentMyInputField"
                        placeholder="Enter a location"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="address_line2" class="RentMyInputField"
                        placeholder="Address line2"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;select class="RentMyInputField" name="country"&gt;
                        &lt;option value=""&gt; Country &lt;/option&gt;
                    &lt;/select&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="city" class="RentMyInputField" placeholder="City"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="state" class="RentMyInputField" placeholder="State"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                    &lt;input type="text" name="zipcode" class="RentMyInputField" placeholder="Zip code"&gt;
                &lt;/div&gt;
                &lt;div class="RentMyButtonGroup RentMyHalfwidth"&gt;
                    &lt;button type="submit" class="RentMyBtn RegistrationBtn"&gt;Sign up&lt;/button&gt;
                    &lt;a href="#" class="LoginHere" RentMyPageLink="login"&gt;Login here&lt;/a&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/form&gt;
    &lt;/div&gt;
&lt;/div&gt;
                           </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Order Complete Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading20">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse20" aria-expanded="false" aria-controls="collapse20">
                        <strong>Order Complete</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse20" class="accordion-collapse collapse" aria-labelledby="heading20"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                           <pre>
&lt;div class="RentMyOrderCompleteWrapper RentMyWrapper" id="RentMyOrderCompleteWrapper"&gt;
    &lt;div Tabs&gt;
        &lt;div class="RentMyRow"&gt;
            &lt;div class="RentMyFullwidth MessageTitle"&gt;
                &lt;h1&gt; Thank You &lt;br /&gt;
                    &lt;span&gt;for your order&lt;/span&gt;
                &lt;/h1&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class="RentMyRow OrderCompleteRow"&gt;
            &lt;div class="OrderCompleteBox" href="#" PdfImageDiv&gt;
                &lt;img src="" alt="Download Receipt" ImageDownloadPDF /&gt;
                &lt;h3&gt;Download Receipt&lt;/h3&gt;
            &lt;/div&gt;
            &lt;div class="OrderCompleteBox" href="#" CalendarImageDiv&gt;
                &lt;img src="" alt="Save to Calender" ImageAddToCalendar /&gt;
                &lt;h3&gt;Add to calendar&lt;/h3&gt;
            &lt;/div&gt;
            &lt;div class="OrderCompleteBox" href="#" DetailsImageDiv&gt;
                &lt;img src="" alt="View order details" ImageViewDetails /&gt;
                &lt;h3&gt;View Details&lt;/h3&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;div class="RentMySummaryWrapper" OrderSummary&gt;
        &lt;div class="container-fullwidth"&gt;
            &lt;div class="RentMyRow"&gt;
                &lt;div class="RentMyColumn8"&gt;
                    &lt;h3 class="OrderSummaryTitle"&gt;Order Summary&lt;/h3&gt;
                    &lt;div class="RentMyTableResponsive"&gt;
                        &lt;table class="RentMyTable RentMyTableStriped RentMyCartTable"&gt;
                            &lt;thead&gt;
                                &lt;tr&gt;
                                    &lt;th&gt;&lt;/th&gt;
                                    &lt;th&gt;Product&lt;/th&gt;
                                    &lt;th&gt;Unit Price&lt;/th&gt;
                                    &lt;th&gt;Quantity&lt;/th&gt;
                                    &lt;th&gt;Subtotal&lt;/th&gt;
                                &lt;/tr&gt;
                            &lt;/thead&gt;
                            &lt;tbody&gt;
                                &lt;tr OrderItem&gt;
                                    &lt;td ImageArea&gt;
                                        &lt;img src="" class="cart product img" alt="" /&gt;
                                    &lt;/td&gt;
                                    &lt;td ItemNameArea&gt;
                                        Product1
                                    &lt;/td&gt;
                                    &lt;td ItemPrice&gt;
                                        $50.00
                                    &lt;/td&gt;
                                    &lt;td Quantity&gt;
                                        2
                                    &lt;/td&gt;
                                    &lt;td ItemPriceArea&gt;
                                        $255.00
                                    &lt;/td&gt;
                                &lt;/tr&gt;
                            &lt;/tbody&gt;
                        &lt;/table&gt;
                    &lt;/div&gt;
                    &lt;div class="RentMyTableResponsive"&gt;
                        &lt;table class="RentMyTable RentMyTableStriped RentMyOptionalService" OptionalService&gt;
                            &lt;thead&gt;
                                &lt;tr&gt;
                                    &lt;th colspan="2"&gt;Optional Services&lt;/th&gt;
                                &lt;/tr&gt;
                            &lt;/thead&gt;
                            &lt;tbody&gt;
                                &lt;tr Service&gt;
                                    &lt;td Name&gt;
                                        Description
                                    &lt;/td&gt;
                                    &lt;td Price&gt;
                                        $255.00
                                    &lt;/td&gt;
                                &lt;/tr&gt;
                            &lt;/tbody&gt;
                        &lt;/table&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="RentMyColumn4"&gt;
                    &lt;div class="RentMyRow RentMyCartSummery"&gt;
                        &lt;div class="RentMyFullwidth"&gt;
                            &lt;span class="RentMySummaryDateRange" RentalDate&gt;11/22/2023 - 11/22/2023&lt;/span&gt;
                            &lt;h4 class="RentMyCartTotal"&gt;Total&lt;/h4&gt;
                            &lt;table class="RentMyTable RentMySummeryTable" SummaryTable&gt;
                                &lt;tbody&gt;
                                    &lt;tr&gt;
                                        &lt;td&gt;Subtotal&lt;/td&gt;
                                        &lt;td&gt;
                                            &lt;span&gt;
                                                &lt;b SubtotalAmount&gt;€0.00&lt;/b&gt;
                                            &lt;/span&gt;
                                        &lt;/td&gt;
                                    &lt;/tr&gt;
                                    &lt;tr&gt;
                                        &lt;td&gt;Optional Service&lt;/td&gt;
                                        &lt;td&gt;
                                            &lt;span&gt;
                                                &lt;b OptionalServices&gt;€0.00&lt;/b&gt;
                                            &lt;/span&gt;
                                        &lt;/td&gt;
                                    &lt;/tr&gt;
                                    &lt;tr&gt;
                                        &lt;td&gt;Deposit Amount&lt;/td&gt;
                                        &lt;td&gt;&lt;span DepositeAmount&gt; €0.00&lt;/span&gt;&lt;/td&gt;
                                    &lt;/tr&gt;
                                    &lt;tr TaxAmountRow&gt;
                                        &lt;td TaxName&gt;Tax Amount&lt;/td&gt;
                                        &lt;td&gt;&lt;span TaxAmount&gt; €0.00&lt;/span&gt;&lt;/td&gt;
                                    &lt;/tr&gt;
                                    &lt;tr&gt;
                                        &lt;td&gt;Shipping Charge&lt;/td&gt;
                                        &lt;td&gt;&lt;span ShippingCharge&gt; €0.00&lt;/span&gt;&lt;/td&gt;
                                    &lt;/tr&gt;
                                    &lt;tr&gt;
                                        &lt;td&gt;
                                            &lt;h5&gt;Total&lt;/h5&gt;
                                        &lt;/td&gt;
                                        &lt;td&gt;
                                            &lt;h5&gt;&lt;span TotalAmount&gt;€0.00&lt;/span&gt;&lt;/h5&gt;
                                        &lt;/td&gt;
                                    &lt;/tr&gt;
                                &lt;/tbody&gt;
                            &lt;/table&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;

        &lt;div class="AfterOrderPageFooter" AfterOrderPageFooter&gt; &lt;/div&gt;

        &lt;div class="OrderDetailsBack"&gt;
            &lt;button Back&gt;Back&lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                           </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Customer Profile Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading10">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse10" aria-expanded="false" aria-controls="collapse10">
                        <strong>Customer Profile</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse10" class="accordion-collapse collapse" aria-labelledby="heading10"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerProfileContainer" class="RentMyWrapper RentMyCustomerPortalWrapper"&gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Profile Information&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;
                    &lt;div class="RentMyCustomerInfo" id="RentmyCustomerDetailsSection"&gt;
                        &lt;div class="RentmyCustomerDetails"&gt;
                            &lt;h5&gt;{{ customer_name }}&lt;/h5&gt;
                            &lt;span&gt;{{ customer_email }}&lt;/span&gt;&lt;br&gt;
                            &lt;span&gt;{{ customer_company_name }} &lt;/span&gt;&lt;br&gt;
                            &lt;span&gt;{{ customer_phone }}&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyEditArea"&gt;
                            &lt;a id="RentmyCustomerEditBtn" href="#" class="RentMyBtn RentMyEditBtn" EditButton&gt;Edit&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    

                    &lt;form id="RentmyCustomerEditForm"&gt;
                        &lt;div class="RentMyRow"&gt;
                            &lt;div class="RentMyAlertMessage RentMyFullwidth"&gt;&lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="first_name"&gt;First Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="first_name" id="first_name" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="last_name"&gt;Last Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="last_name" id="last_name" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="email"&gt;Email&lt;/label&gt;
                                &lt;input type="email" class="RentMyInputField" name="email" id="email" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="mobile"&gt;Mobile Number&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="mobile" id="mobile" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="company"&gt;Company Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="company" id="company" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyButtonGroup RentMyNotBetween RentMyHalfwidth"&gt;
                                &lt;button id="RentmyCustomerSubmitBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Submit&lt;/button&gt;
                                &lt;button id="RentmyCustomerCancelBtn" type="button" class="RentMyBtn RentMyCustomerInfoCancel"&gt;Cancel&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;

                    &lt;div id="RentmyCustomerAddessList" class="RentmyCustomerAddessList"&gt;                                
                        &lt;div class="FlexHeader"&gt;
                            &lt;h5 class="AddressHeader"&gt;Address&lt;/h5&gt;
                            &lt;a class="RentMyBtn RentMyEditBtn addAddress" tooltip="Add Address" AddAddressBtn&gt; &lt;i class="fa fa-plus"&gt;&lt;/i&gt; &lt;/a&gt;
                        &lt;/div&gt;
                        &lt;div class="AddressBody" AddressBody&gt;                          
                            &lt;div AddressGroup&gt;
                                &lt;h5 class="AddressSubHeader" AddressType&gt;Primary&lt;/h5&gt;
                                &lt;div class="Address" Address&gt;
                                    &lt;label&gt;Address 1, Address 2  &lt;/label&gt;
                                    &lt;div class="Actions"&gt;
                                        &lt;button class="btn btn-sm biling-address-edit float-right" tooltip="Delete" IconTrash&gt;&lt;i class="bx bxs-trash-alt" &gt;&lt;/i&gt;&lt;/button&gt;
                                        &lt;button class="btn btn-sm biling-address-edit float-right" tooltip="Update" IconEdit&gt;&lt;i class="bx bxs-pencil"&gt;&lt;/i&gt;&lt;/button&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;                                                     
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;form id="RentmyAddressAddEditForm"&gt;
                        &lt;div class="row"&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Address type (Primary/Office/Home)&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="type"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Mobile&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="mobile"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Select country&lt;/label&gt;
                                &lt;select class="RentMyInputField" name="country"&gt;
                                    &lt;option value=""&gt; Country &lt;/option&gt; 
                                &lt;/select&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Address&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="address_line1"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;City&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="city"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;State&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="state"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;zipcode&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="zipcode"&gt;
                            &lt;/div&gt;
                            &lt;div class="col-12"&gt;
                                &lt;button id="RentmyAddressAddBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Add Address&lt;/button&gt;
                                &lt;button id="RentmyAddressUpdateBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Update Address&lt;/button&gt;
                                &lt;button id="RentmyAddressCancelBtn" type="button" class="RentMyBtn RentMyCustomerInfoCancel"&gt;Cancel&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Customer Profile With Sidebar Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading10">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse10" aria-expanded="false" aria-controls="collapse10">
                        <strong>Customer Profile With Sidebar</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse10" class="accordion-collapse collapse" aria-labelledby="heading10"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerProfileContainer" class="RentMyWrapper RentMyCustomerPortalWrapper"&gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyLeftSidebarmenu"&gt;
            &lt;div class="RentMyLeftSidebarmenuInner" SideBar&gt;
                &lt;div class="RentMyProfileImge"&gt;
                    &lt;img ProfileImage src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/embed/images/icons/profile.png" alt=""&gt;
                &lt;/div&gt;
                &lt;h5 class="RentMyProfileName"&gt;{{ customer_name }}&lt;/h5&gt;
                &lt;div class="RentMySideMenu"&gt;
                    &lt;ul&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_profile" class="active"&gt;Profile&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_password"&gt;Change Password&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_avatar"&gt;Change Avatar&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_order_history"&gt;Order History&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class="rentmy_logout_btn"&gt;Logout&lt;/a&gt;&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt; 
        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Profile Information&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;
                    &lt;div class="RentMyCustomerInfo" id="RentmyCustomerDetailsSection"&gt;
                        &lt;div class="RentmyCustomerDetails"&gt;
                            &lt;h5&gt;{{ customer_name }}&lt;/h5&gt;
                            &lt;span&gt;{{ customer_email }}&lt;/span&gt;&lt;br&gt;
                            &lt;span&gt;{{ customer_company_name }} &lt;/span&gt;&lt;br&gt;
                            &lt;span&gt;{{ customer_phone }}&lt;/span&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyEditArea"&gt;
                            &lt;a id="RentmyCustomerEditBtn" href="#" class="RentMyBtn RentMyEditBtn" EditButton&gt;Edit&lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                    

                    &lt;form id="RentmyCustomerEditForm"&gt;
                        &lt;div class="RentMyRow"&gt;
                            &lt;div class="RentMyAlertMessage RentMyFullwidth"&gt;&lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="first_name"&gt;First Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="first_name" id="first_name" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="last_name"&gt;Last Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="last_name" id="last_name" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="email"&gt;Email&lt;/label&gt;
                                &lt;input type="email" class="RentMyInputField" name="email" id="email" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="mobile"&gt;Mobile Number&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="mobile" id="mobile" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyHalfwidth"&gt;
                                &lt;label for="company"&gt;Company Name&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="company" id="company" required&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyButtonGroup RentMyNotBetween RentMyHalfwidth"&gt;
                                &lt;button id="RentmyCustomerSubmitBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Submit&lt;/button&gt;
                                &lt;button id="RentmyCustomerCancelBtn" type="button" class="RentMyBtn RentMyCustomerInfoCancel"&gt;Cancel&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;

                    &lt;div id="RentmyCustomerAddessList" class="RentmyCustomerAddessList"&gt;                                
                        &lt;div class="FlexHeader"&gt;
                            &lt;h5 class="AddressHeader"&gt;Address&lt;/h5&gt;
                            &lt;a class="RentMyBtn RentMyEditBtn addAddress" tooltip="Add Address" AddAddressBtn&gt; &lt;i class="fa fa-plus"&gt;&lt;/i&gt; &lt;/a&gt;
                        &lt;/div&gt;
                        &lt;div class="AddressBody" AddressBody&gt;                          
                            &lt;div AddressGroup&gt;
                                &lt;h5 class="AddressSubHeader" AddressType&gt;Primary&lt;/h5&gt;
                                &lt;div class="Address" Address&gt;
                                    &lt;label&gt;Address 1, Address 2  &lt;/label&gt;
                                    &lt;div class="Actions"&gt;
                                        &lt;button class="btn btn-sm biling-address-edit float-right" tooltip="Delete" IconTrash&gt;&lt;i class="bx bxs-trash-alt" &gt;&lt;/i&gt;&lt;/button&gt;
                                        &lt;button class="btn btn-sm biling-address-edit float-right" tooltip="Update" IconEdit&gt;&lt;i class="bx bxs-pencil"&gt;&lt;/i&gt;&lt;/button&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;                                                     
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;form id="RentmyAddressAddEditForm"&gt;
                        &lt;div class="row"&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Address type (Primary/Office/Home)&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="type"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Mobile&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="mobile"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Select country&lt;/label&gt;
                                &lt;select class="RentMyInputField" name="country"&gt;
                                    &lt;option value=""&gt; Country &lt;/option&gt; 
                                &lt;/select&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;Address&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="address_line1"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;City&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="city"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;State&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="state"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup col-md-6"&gt;
                                &lt;label&gt;zipcode&lt;/label&gt;
                                &lt;input type="text" class="RentMyInputField" name="zipcode"&gt;
                            &lt;/div&gt;
                            &lt;div class="col-12"&gt;
                                &lt;button id="RentmyAddressAddBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Add Address&lt;/button&gt;
                                &lt;button id="RentmyAddressUpdateBtn" type="submit" class="RentMyBtn RentMyCustomerInfoSubmit"&gt;Update Address&lt;/button&gt;
                                &lt;button id="RentmyAddressCancelBtn" type="button" class="RentMyBtn RentMyCustomerInfoCancel"&gt;Cancel&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Reset Password Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading11">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse11" aria-expanded="false" aria-controls="collapse11">
                        <strong>Reset Password</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse11" class="accordion-collapse collapse" aria-labelledby="heading11"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyResetPasswordContainer" class="RentMyWrapper"&gt;
    &lt;div class="Contents"&gt;
        &lt;div class="card-body pb-4"&gt;
            &lt;div class="userlogin-box"&gt;
                &lt;div class="Describer"&gt;
                    &lt;img src="https://teststore05.rentmy.shop/assets/img/home/<USER>" alt="forgot password"&gt;
                    &lt;h1 class="Title" Title&gt;It happens to all of us.&lt;/h1&gt;
                    &lt;p SubTitle&gt;Enter your email to reset your password&lt;/p&gt;
                &lt;/div&gt;
                &lt;div class="RentMyAlertMessage RentMyFullwidth" AlertMessage&gt;&lt;/div&gt;               
                &lt;form&gt;                      
                    &lt;input type="email" class="RentMyInputField" placeholder="Email*" Email/&gt;

                    &lt;input type="password" class="RentMyInputField" placeholder="New Password" Password/&gt; 
                    &lt;input type="password" class="RentMyInputField" placeholder="Confirm Password" ConfirmPassword/&gt;
                    
                    &lt;div class="d-flex justify-content-center RentMyButtonGroup"&gt;
                        &lt;button type="submit" class="RentMyBtn RentMyBackBtn RentMySubmitBtn" RentMySubmitBtn&gt;Submit&lt;/button&gt;
                        &lt;button type="submit" class="RentMyBtn RentMyBackBtn RentMyReturnBtn" RentMyReturnBtn&gt;Return&lt;/button&gt;
                    &lt;/div&gt;                            
                &lt;/form&gt;
                
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Change Password Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading12">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse12" aria-expanded="false" aria-controls="collapse12">
                        <strong>Change Password</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse12" class="accordion-collapse collapse" aria-labelledby="heading12"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerChangePasswordContainer" class="RentMyWrapper RentMyCustomerPortalWrapper"&gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Change Password&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt; &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;
                    &lt;form id="RentMyChangePasswordForm"&gt;
                        &lt;div class="RentMyRow"&gt;
                        &lt;div class="RentMyAlertMessage RentMyFullwidth"&gt;&lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                &lt;label for="old_password"&gt;Old Password&lt;/label&gt;
                                &lt;input type="password" class="RentMyInputField" name="old_password" id="old_password"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                &lt;label for="password"&gt;New Password&lt;/label&gt;
                                &lt;input type="password" class="RentMyInputField" name="password" id="password"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                &lt;label for="confirm_password"&gt;Confirm Password&lt;/label&gt;
                                &lt;input type="password" class="RentMyInputField" name="confirm_password" id="confirm_password"&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyButtonGroup RentMyNotBetween"&gt;
                                &lt;button id="RentMyChangePasswordSubmit" type="button" class="RentMyBtn RentMyPassSubmitBtn"&gt;Submit&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Change Avatar Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading13">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse13" aria-expanded="false" aria-controls="collapse13">
                        <strong>Change Avatar</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse13" class="accordion-collapse collapse" aria-labelledby="heading13"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerChangeAvatarContainer" class="RentMyWrapper RentMyCustomerPortalWrapper"&gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Change Avatar&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt;

                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;
                    &lt;form id="RentMyFileUploadForm"&gt;
                        &lt;div class="RentMyRow"&gt;
                            &lt;div class="RentMyAlertMessage RentMyFullwidth"&gt;&lt;/div&gt;
                            &lt;div class="RentMyInputGroup RentMyFullwidth"&gt;
                                &lt;label for="file"&gt;Upload Image (Maximum file size 2MB)&lt;/label&gt;
                                &lt;div class="RentMyFileUpload" FileUploadArea&gt;
                                    &lt;div class="FileSelect"&gt;
                                        &lt;div class="FileSelectButton"&gt;Choose File&lt;/div&gt;
                                        &lt;div class="FileSelectName" FileName&gt;No file chosen...&lt;/div&gt; 
                                        &lt;input type="file" name="chooseFile"&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                            &lt;div class="RentMyButtonGroup RentMyNotBetween"&gt;
                                &lt;button id="RentMyFileUploadSubmitBtn" type="button" class="RentMyBtn RentMyUploadBtn"&gt;Upload&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/form&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Customer Order History Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading18">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse18" aria-expanded="false" aria-controls="collapse18">
                        <strong>Customer Order History</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse18" class="accordion-collapse collapse" aria-labelledby="heading18"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerOrderHistory" class="RentMyWrapper RentMyCustomerPortalWrapper" &gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyLeftSidebarmenu"&gt;
            &lt;div class="RentMyLeftSidebarmenuInner"&gt;
                &lt;div class="RentMyProfileImge"&gt;
                    &lt;img rentmy_customer_profile_image="true" src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/embed/images/icons/profile.png" alt=""&gt;
                &lt;/div&gt;
                &lt;h5 class="RentMyProfileName"&gt;{{ customer_name }}&lt;/h5&gt;
                &lt;div class="RentMySideMenu"&gt;
                    &lt;ul&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_profile"&gt;Profile&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_password"&gt;Change Password&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_avatar"&gt;Change Avatar&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_order_history" class="active"&gt;Order History&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class="rentmy_logout_btn"&gt;Logout&lt;/a&gt;&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;

        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Order History&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt;

                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;
                    &lt;div id="RentMyOrderHistoryTableData" class="RentMyTableResponsive"&gt;
                        &lt;table class="RentMyTable RentMyTableStriped"&gt;
                            &lt;thead&gt;
                                &lt;tr&gt;
                                    &lt;th&gt;Order ID&lt;/th&gt;
                                    &lt;th&gt;Address&lt;/th&gt;
                                    &lt;th&gt;Quantity&lt;/th&gt;
                                    &lt;th&gt;Price&lt;/th&gt;
                                    &lt;th&gt;Status&lt;/th&gt;
                                    &lt;th&gt;Action&lt;/th&gt;
                                &lt;/tr&gt;
                            &lt;/thead&gt;
                            &lt;tbody&gt;
                                &lt;tr&gt;
                                    &lt;td&gt;{{id}}&lt;/td&gt;
                                    &lt;td&gt;{{order_address}}&lt;/td&gt;
                                    &lt;td&gt;{{total_quantity}}&lt;/td&gt;
                                    &lt;td&gt;{{total_price}}&lt;/td&gt;
                                    &lt;td&gt;{{status}}&lt;/td&gt;
                                    &lt;td&gt;&lt;/td&gt;
                                &lt;/tr&gt;
                            &lt;/tbody&gt;
                        &lt;/table&gt;
                    &lt;/div&gt;
                    &lt;div id="RentMyPagination"&gt;&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Customer Order Details Page -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading19">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse19" aria-expanded="false" aria-controls="collapse19">
                        <strong>Customer Order Details</strong> &nbsp;Page
                    </button>
                </h2>
                <div id="collapse19" class="accordion-collapse collapse" aria-labelledby="heading19"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
&lt;div id="RentMyCustomerOrderDetails" class="RentMyWrapper RentMyCustomerPortalWrapper"&gt;
    &lt;div class="RentMyCustomPortalRow"&gt;
        &lt;div class="RentMyLeftSidebarmenu"&gt;
            &lt;div class="RentMyLeftSidebarmenuInner"&gt;
                &lt;div class="RentMyProfileImge"&gt;
                    &lt;img rentmy_customer_profile_image="true" src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/embed/images/icons/profile.png" alt=""&gt;
                &lt;/div&gt;
                &lt;h5 class="RentMyProfileName"&gt;{{ customer_name }}&lt;/h5&gt;
                &lt;div class="RentMySideMenu"&gt;
                    &lt;ul&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_profile"&gt;Profile&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_password"&gt;Change Password&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_change_avatar"&gt;Change Avatar&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a RentMyPageLink="customer_order_history" class="active"&gt;Order History&lt;/a&gt;&lt;/li&gt;
                        &lt;li&gt;&lt;a class="rentmy_logout_btn"&gt;Logout&lt;/a&gt;&lt;/li&gt;
                    &lt;/ul&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class="RentMyRightContent"&gt;
            &lt;div class="RentMyPageHeader"&gt;
                &lt;h3&gt;Order Details: #&lt;span&gt;{{order_id}}&lt;/span&gt;&lt;/h3&gt;
                &lt;div class="RentMyPageHeaderRightSide"&gt;
                    &lt;span&gt;Status: &lt;a href="#" class="RentMyBtn RentMyStatusBtn rounded"&gt;{{order_status}}&lt;/a&gt;&lt;/span&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="RentMyContentBody"&gt;
                &lt;div class="RentMyContentBodyInner"&gt;

                    &lt;!-- Summery and Billing Details css  --&gt;
                    &lt;div class="RentMyRow SummeryBillingDetails"&gt;
                        &lt;div class="RentMyHalfwidth"&gt;
                            &lt;div class="RentMyCard"&gt;
                                &lt;div class="RentMyCardHeader"&gt;
                                    &lt;h3&gt;Summary&lt;/h3&gt;
                                    &lt;div class="RentMyCardRightSide"&gt;&lt;/div&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyCardBody"&gt;
                                    &lt;div class="RentMyRow"&gt;
                                        &lt;div class="RentMyHalfwidth"&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Order ID:&lt;/strong&gt; &lt;span&gt;{{order_id}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Quantity:&lt;/strong&gt; &lt;span&gt;{{order_quantity}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Payment Status:&lt;/strong&gt; &lt;span&gt;{{order_payment_status}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Status:&lt;/strong&gt; &lt;span&gt;{{order_status}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Order Date & Time:&lt;/strong&gt; &lt;span&gt;{{order_date_time}}&lt;/span&gt;&lt;/p&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyHalfwidth"&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Subtotal:&lt;/strong&gt; &lt;span&gt;{{order_subtotal}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Delivery Charge Total:&lt;/strong&gt; &lt;span&gt;{{order_delivery_charge_total}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Total Discount:&lt;/strong&gt; &lt;span&gt;{{order_total_discount}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Sales Tax:&lt;/strong&gt; &lt;span&gt;{{order_sales_tax}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;DE (8%):&lt;/strong&gt; &lt;span&gt;{{order_de}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Grand Total:&lt;/strong&gt;  &lt;span&gt;{{order_grand_total}}&lt;/span&gt;&lt;/p&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyHalfwidth"&gt;
                            &lt;div class="RentMyCard"&gt;
                                &lt;div class="RentMyCardHeader"&gt;
                                    &lt;h3&gt;Billing & Shipping Details&lt;/h3&gt;
                                    &lt;div class="RentMyCardRightSide"&gt;&lt;/div&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyCardBody"&gt;
                                    &lt;div class="RentMyRow"&gt;
                                        &lt;div class="RentMyHalfwidth"&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Name:&lt;/strong&gt; &lt;span&gt;{{billing_name}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Mobile:&lt;/strong&gt; &lt;span&gt;{{billing_mobile}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Address Line 1:&lt;/strong&gt; &lt;span&gt;{{billing_address_line_1}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;City:&lt;/strong&gt; &lt;span&gt;{{billing_city}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;State:&lt;/strong&gt; &lt;span&gt;{{billing_state}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Company:&lt;/strong&gt; &lt;span&gt;{{billing_company}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Address Info:&lt;/strong&gt; &lt;span&gt;{{billing_address_info}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Signature:&lt;/strong&gt; &lt;span&gt;{{signature}}&lt;/span&gt;&lt;/p&gt;
                                        &lt;/div&gt;
                                        &lt;div class="RentMyHalfwidth"&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Email:&lt;/strong&gt; &lt;span&gt;{{billing_email}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Country:&lt;/strong&gt; &lt;span&gt;{{billing_country}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Address Line 2:&lt;/strong&gt; &lt;span&gt;{{billing_address_line_2}}&lt;/span&gt;&lt;/p&gt;
                                            &lt;p class="RentMyDestitle"&gt;&lt;strong&gt;Zipcode:&lt;/strong&gt; &lt;span&gt;{{billing_zip}}&lt;/span&gt;&lt;/p&gt;
                                        &lt;/div&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;!-- Inventory Items area  --&gt;
                    &lt;div class="RentMyCard InventoryRentMyCard"&gt;
                        &lt;div class="RentMyCardHeader"&gt;
                            &lt;h3&gt;Inventory Items&lt;/h3&gt;
                            &lt;div class="RentMyCardRightSide"&gt;
                                &lt;button id="RentMyAddInventoryItemBtn" class="RentMyBtn RentMyAddItem"&gt;Add Item&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyCardBody"&gt;
                            &lt;div rentmy-add-inventory-search="true"&gt;&lt;/div&gt;   
                            &lt;div rentmy-order-product-details="true"&gt;&lt;/div&gt;
                            &lt;div id="RentMyOrderInventoryTableData" class="RentMyTableResponsive"&gt;
                                &lt;table class="RentMyTable RentMyTableStriped"&gt;
                                    &lt;thead&gt;
                                        &lt;tr&gt;
                                            &lt;th width="10%"&gt;Image&lt;/th&gt;
                                            &lt;th width="25%"&gt;Description&lt;/th&gt;
                                            &lt;th width="15%"&gt;Price&lt;/th&gt;
                                            &lt;th width="20%"&gt;Quantity&lt;/th&gt;
                                            &lt;th width="10%"&gt;Subtotal&lt;/th&gt;
                                            &lt;th width="10%"&gt;Action&lt;/th&gt;
                                        &lt;/tr&gt;
                                    &lt;/thead&gt;
                                    &lt;tbody&gt;
                                        &lt;tr&gt;
                                            &lt;td&gt;{{product_images}}&lt;/td&gt;
                                            &lt;td&gt;{{product_discription}}&lt;/td&gt;
                                            &lt;td&gt; {{subtotal}} &lt;/td&gt;
                                            &lt;td&gt;
                                                {{quantity_functions}}
                                            &lt;/td&gt;
                                            &lt;td&gt;{{price}}&lt;/td&gt;
                                            &lt;td class="TextCenter"&gt;
                                                &lt;a href="#" class="TextDanger"&gt;&lt;i class="fa fa-trash"&gt;&lt;/i&gt;&lt;/a&gt;
                                            &lt;/td&gt;
                                        &lt;/tr&gt;
                                    &lt;/tbody&gt;
                                &lt;/table&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;!-- Payments area  --&gt;
                    &lt;div class="RentMyCard PaymentsRentMyCard"&gt;
                        &lt;div class="RentMyCardHeader"&gt;
                            &lt;h3&gt;Payments&lt;/h3&gt;
                            &lt;div class="RentMyCardRightSide"&gt;
                                &lt;div class="RentMyCardRightSide"&gt;
                                    
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyCardBody"&gt;
                            &lt;div class="RentMyPaymentOption"&gt;
                                &lt;h5&gt;&lt;strong&gt;Total Amount:&lt;/strong&gt; &lt;span&gt;{{total_payment_amount}}&lt;/span&gt; &lt;/h5&gt;
                                &lt;h5&gt;&lt;strong&gt;Total Paid:&lt;/strong&gt; &lt;span&gt;{{total_paid_amount}}&lt;/span&gt; &lt;/h5&gt;
                                &lt;h5&gt;&lt;strong&gt;Due Amount:&lt;/strong&gt; &lt;span&gt;{{total_due_amount}}&lt;/span&gt; &lt;/h5&gt;
                            &lt;/div&gt;
                            &lt;div id="RentMyOrderPaymentTableData" class="RentMyTableResponsive"&gt;
                                &lt;table class="RentMyTable RentMyTableStriped"&gt;
                                    &lt;thead&gt;
                                        &lt;tr&gt;
                                            &lt;th width="30%"&gt;Payment Type&lt;/th&gt;
                                            &lt;th width="15%"&gt;Payment Status&lt;/th&gt;
                                            &lt;th width="15%"&gt;Paid Amount&lt;/th&gt;
                                            &lt;th width="40%"&gt;Note&lt;/th&gt;
                                        &lt;/tr&gt;
                                    &lt;/thead&gt;
                                    &lt;tbody&gt;
                                        &lt;tr&gt;
                                            &lt;td&gt;{{content_method}}&lt;/td&gt;
                                            &lt;td&gt;{{status}}&lt;/td&gt;
                                            &lt;td&gt;{{payment_amount}}&lt;/td&gt;
                                            &lt;td&gt; {{note}} &lt;/td&gt;
                                        &lt;/tr&gt;
                                    &lt;/tbody&gt;
            
                                &lt;/table&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;

                    &lt;!-- Notes area  --&gt;
                    &lt;div class="RentMyCard NotesRentMyCard"&gt;
                        &lt;div class="RentMyCardHeader"&gt;
                            &lt;h3&gt;Notes&lt;/h3&gt;
                            &lt;div class="RentMyCardRightSide"&gt;
                                &lt;button id="RentMyNoteAddBtn" class="RentMyBtn RentMyAddNoteBtn"&gt;Add Note&lt;/button&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="RentMyCardBody"&gt;
                            &lt;form id="RentMyNoteAddForm"&gt;
                                &lt;div class="RentMyInputGroup"&gt;
                                    &lt;label for="RentMyNote"&gt;Note&lt;/label&gt;
                                    &lt;textarea row="10" id="RentMyNote" name="note" placeholder="Enter note" autocomplete="off" class="RentMyInputField"&gt;&lt;/textarea&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyInputGroup"&gt;
                                   &lt;div class="RentMyCustomFile"&gt;
                                        &lt;input type="file" id="RentMyFile" name="file" class="RentMyCustomFileInput"&gt;
                                        &lt;label for="RentMyFile" class="RentMyCustomFileLabel"&gt; Choose file &lt;/label&gt;
                                    &lt;/div&gt;
                                &lt;/div&gt;
                                &lt;div class="RentMyButtonGroup RentMyNotBetween"&gt;
                                    &lt;button id="RentMyNoteSubmitBtn" type="button" class="RentMyBtn RentMyNoteBtn"&gt;
                                        Submit
                                    &lt;/button&gt;
                                    &lt;button id="RentMyNoteCancelBtn" type="button" class="RentMyBtn RentMyNoteCancelBtn"&gt;Cancel&lt;/button&gt;
                                &lt;/div&gt;
                            &lt;/form&gt;
                            &lt;div id="RentMyOrderNoteTableData" class="RentMyTableResponsive"&gt;
                                &lt;table class="RentMyTable RentMyTableStriped"&gt;
                                    &lt;tbody&gt;
                                        &lt;tr&gt;
                                            &lt;td width="50%"&gt;
                                                &lt;p&gt;{{note}}&lt;/p&gt;
                                                &lt;img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/orders/65004/xgl51xn_1701758812_xf14i7o.jpg" alt="OrderNotes"&gt;
                                            &lt;/td&gt;
                                            &lt;td width="30%"&gt;
                                                &lt;span&gt; Mohsin Kabir&lt;/span&gt;
                                                &lt;span&gt;Today 12:48 AM &lt;/span&gt;
                                            &lt;/td&gt;
                                            &lt;td class="TextCenter" width="10%"&gt;
                                                &lt;a href="#" class="TextDanger"&gt;&lt;i class="fa fa-trash"&gt;&lt;/i&gt;&lt;/a&gt;
                                            &lt;/td&gt;
                                        &lt;/tr&gt;
                                        &lt;tr&gt;
                                            &lt;td width="50%"&gt;
                                                &lt;p&gt;{{note}}&lt;/p&gt;
                                                &lt;img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/orders/65004/xgl51xn_1701758812_xf14i7o.jpg" alt="OrderNotes"&gt;
                                            &lt;/td&gt;
                                            &lt;td width="30%"&gt;
                                                &lt;span&gt; Mohsin Kabir&lt;/span&gt;
                                                &lt;span&gt;Today 12:48 AM &lt;/span&gt;
                                            &lt;/td&gt;
                                            &lt;td class="TextCenter" width="10%"&gt;
                                                &lt;a href="#" class="TextDanger"&gt;&lt;i class="fa fa-trash"&gt;&lt;/i&gt;&lt;/a&gt;
                                            &lt;/td&gt;
                                        &lt;/tr&gt;
                                    &lt;/tbody&gt;
                                &lt;/table&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Search Widget -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading16">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse16" aria-expanded="false" aria-controls="collapse16">
                        <strong>Search</strong> &nbsp;Widget
                    </button>
                </h2>
                <div id="collapse16" class="accordion-collapse collapse" aria-labelledby="heading16"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
Add this attribute to your search bar: rentmy-search-widget

Example:
&lt;div rentmy-search-widget style=”width:100%”&gt; &lt;/div&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>

            <!-- Logout Function -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading17">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapse17" aria-expanded="false" aria-controls="collapse17">
                        <strong>Logout</strong> &nbsp;Function
                    </button>
                </h2>
                <div id="collapse17" class="accordion-collapse collapse" aria-labelledby="heading17"
                    data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <code>
                            <pre>
Add this class to your logout button: rentmy_logout_btn

Example:
&lt;a class="rentmy_logout_btn"&gt;Logout&lt;/a&gt;
                            </pre>
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>