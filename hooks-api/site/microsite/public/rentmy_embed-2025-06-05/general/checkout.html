<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <script src="config.js"> </script>
</head>

<body>
    <main class="container my-5">
        <div id="RentMyCheckoutWrapper" class="RentMyCheckoutWrapper RentMyWrapper">
            <div class="RentMyRow">
                <div class="CheckoutLeftSide">
                    <div class="ReturningCustomerTitle" SignInPopupArea>
                        <h5> Welcome to <span StoreName>store</span> <a SignIn>(Sign in)</a></h5>
                    </div>
                    <div class="BillingDetailsLeftside">
                        <div class="BillingDetailsLeftSideInner" BillingBorder>
                            <span class="BillingCheckoutTitle">Billing Details</span>
                            <form>
                                <div class="RentMyRow" BillingGeneralInfo>
                                    <div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv>
                                        <label> First Name</label>
                                        <input type="text" class="RentMyInputField" FirstName />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv>
                                        <label>Last Name</label>
                                        <input type="text" class="RentMyInputField" LastName />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" MobileDiv>
                                        <label>Mobile Number</label>
                                        <input type="text" class="RentMyInputField" Mobile />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" EmailDiv>
                                        <label>Email Name </label>
                                        <input type="email" class="RentMyInputField" Email />
                                    </div>
                                    <div class="RentMyInputGroup RentMyFullwidth" CompanyDiv>
                                        <label>Company Name(Optional)</label>
                                        <input type="text" class="RentMyInputField" Company />
                                    </div>

                                    <div class="RentMyRow billing-details-checkbox" AllSavedBililingAddress>
                                        <div class="RentMyInputGroup RentMyFullwidth">
                                            <label class="RentMyRadio" SingleAddress>
                                                This is a save address
                                                <input type="radio" name="select_address" value="rent">
                                                <span></span>
                                            </label>
                                            <!-- Create New -->
                                            <label class="RentMyRadio" CreateAddress>
                                                Create New
                                                <input type="radio" name="select_address" value="rent">
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>

                                    <div class="RentMyInputGroup RentMyFullwidth">
                                        <label>Country</label>
                                        <select class="RentMyInputField" Country>
                                            <option value=""> Country Name </option>
                                        </select>
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label>Address Line 1</label>
                                        <input type="text" class="RentMyInputField" placeholder="Enter a location"
                                            AddressLine1 />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label>Address Line 2</label>
                                        <input type="text" class="RentMyInputField" AddressLine2 />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label>City</label>
                                        <input type="text" class="RentMyInputField" City />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label>State</label>
                                        <input type="text" class="RentMyInputField" State />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label> Zipcode</label>
                                        <input type="text" class="RentMyInputField" Zipcode />
                                    </div>
                                </div>

                                <div class="RentMyRow" BillingAdditionalInfo>
                                    <div class="RentMyFullwidth">
                                        <div class="BillingCheckoutSubTitle">
                                            <h5 Title>
                                                <i class="fa fa-plus"></i>
                                                Additional Information
                                            </h5>
                                        </div>
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" SpecialComments>
                                        <label>Special Instructions/Comments</label>
                                        <input type="text" class="RentMyInputField">
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" SpecialRequest>
                                        <label>Special Request</label>
                                        <input type="text" class="RentMyInputField">
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" DrivingLicence>
                                        <label>Driving Licence</label>
                                        <input type="text" class="RentMyInputField">
                                    </div>

                                </div>
                                <div class="RentMyRow" BillingCustomCheckoutInfo>
                                    <div class="RentMyFullwidth">
                                        <div class="BillingCheckoutSubTitle">
                                            <h5>
                                                <i class="fa fa-plus"></i>
                                                Custom checkout information
                                            </h5>
                                        </div>
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth" CustomField></div>
                                </div>
                            </form>
                        </div>
                        <div class="BillingDetailsLeftSideInner FullfillmentArea" FullfillmentArea FullfillmentBorder>
                            <h2 class="BillingCheckoutTitle" Title>Fulfillment</h2>
                            <div class="FullfillmentTabArea">
                                <div class="FullfillmentTabList">
                                    <ul>
                                        <li PickupTab></li>
                                        <li ShippingTab></li>
                                        <li DeliveryTab></li>
                                    </ul>
                                </div>
                                <div class="FullfillmentTabContent">

                                    <div class="FullfillmentTabBody FullfillmentPickup" PickupLocations>
                                        <div class="PickupLocationList" Location></div>
                                    </div>

                                    <div class="FullfillmentTabBody FullfillmentShippingAndDelivery" ShipAndDelivery>
                                        <div class="RentMyRow" SameAsAboveArea>
                                            <div class="RentMyInputGroup RentMyFullwidth">
                                                <label class="RentMyCheckbox">
                                                    <input type="checkbox" SameAsAbove>
                                                    Same Address as Above
                                                    <span></span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="FullfillmentTabBody FullfillmentPickup" ShippingAddressList>
                                            <div class="PickupLocationList" Address>
                                                <label class="RentMyRadio">
                                                    <input type="radio" name="shipping_address" value="rent">
                                                    Default location (5627 Covehaven Dr, Dallas, TX, US)
                                                    <span></span>
                                                </label>
                                            </div>
                                            <div class="PickupLocationList" CreateNew>
                                                <label class="RentMyRadio">
                                                    <input type="radio" name="shipping_address" value="rent">
                                                    Create New
                                                    <span></span>
                                                </label>
                                            </div>
                                        </div>

                                        <form class="mt-4">
                                            <div class="RentMyRow">
                                                <div class="RentMyInputGroup RentMyHalfwidth" FirstNameDiv>
                                                    <label> First Name</label>
                                                    <input type="text" class="RentMyInputField" FirstName />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth" LastNameDiv>
                                                    <label>Last Name</label>
                                                    <input type="text" class="RentMyInputField" LastName />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth" MobileDiv>
                                                    <label>Mobile Number</label>
                                                    <input type="text" class="RentMyInputField" Mobile />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth" EmailDiv>
                                                    <label>Email Name </label>
                                                    <input type="email" class="RentMyInputField" Email />
                                                </div>
                                                <div class="RentMyInputGroup RentMyFullwidth" CompanyDiv>
                                                    <label>Company Name(Optional)</label>
                                                    <input type="text" class="RentMyInputField" Company />
                                                </div>
                                                <div class="RentMyInputGroup RentMyFullwidth">
                                                    <label>Country</label>
                                                    <select class="RentMyInputField" Country>
                                                        <option value=""> Country Name </option>
                                                    </select>
                                                </div>

                                                <div class="RentMyInputGroup RentMyHalfwidth">
                                                    <label>Address Line 1</label>
                                                    <input type="text" class="RentMyInputField"
                                                        placeholder="Enter a location" AddressLine1 />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth">
                                                    <label>Address Line 2</label>
                                                    <input type="text" class="RentMyInputField" AddressLine2 />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth">
                                                    <label>City</label>
                                                    <input type="text" class="RentMyInputField" City />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth">
                                                    <label>State</label>
                                                    <input type="text" class="RentMyInputField" State />
                                                </div>
                                                <div class="RentMyInputGroup RentMyHalfwidth">
                                                    <label> Zipcode</label>
                                                    <input type="text" class="RentMyInputField" Zipcode />
                                                </div>
                                            </div>
                                        </form>

                                        <div class="ShippingMethodArea" ShippingMethods>
                                            <h5 Title>Select Shipping Method</h5>
                                            <div AllMethods>
                                                <div class="PickupLocationList" Method>
                                                    <label class="RentMyRadio">
                                                        <input type="radio" name="shipping_methods" value="rent">
                                                        Flat Rate
                                                        <b>$33.99</b>
                                                        <span></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="ShippingCostArea" DeliveryCosts>
                                            <h5 Title>Delivery Cost</h5>
                                            <div class="PickupLocationList" Cost>
                                                <label class="RentMyRadio">
                                                    <input type="radio" name="delivery_cost" value="rent">
                                                    Zone A
                                                    <b>$33.99</b>
                                                    <span></span>
                                                </label>
                                            </div>
                                        </div>

                                        <div class="DeliveryAddressMsg" DeliveryOutsideAreaMsg>
                                            Your address is outside of our delivery area. Please contact us to make
                                            other arrangements.
                                        </div>
                                        <div class="DeliveryAddressErrorMsg" DeliveryAddressErrorMsg>
                                            Delivery is not possible for your address
                                        </div>

                                        <div class="RentMyRow mt-3">
                                            <div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea">
                                                <button type="button" class="RentMyBtn RentMyGetShippingBtn"
                                                    GetShippingMethodsBtn>Get shipping method <i
                                                        class="fa fa-arrow-right"></i></button>
                                            </div>
                                            <div class="RentMyButtonGroup RentMyFullwidth RentMyGetShippingArea">
                                                <!-- <button type="button" class="RentMyBtn RentMyGetShippingBtn"
                                                    GetDeliveryCostBtn>Get delivery cost <i
                                                        class="fa fa-arrow-right"></i></button> -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="CheckoutRightSide" OrderSummary>
                    <div class="OrderReviewWrapper" Contents>
                        <span class="OrderReviewTitle">Your Order Summary</span>
                        <div class="OrderReviewWrapperBody">
                            <div class="OrderSummery">
                                <div class="CheckoutDatetimeShow" CheckoutDatetime>
                                    12/21/2023 - 12/21/2023
                                </div>
                                <div class="CheckoutOrderList" CartItems>
                                    <div class="CheckoutOrderItem" Item>
                                        <div class="OrderItemImg">
                                            <img src="" alt="order itemimg" class="img-fluid" />
                                        </div>
                                        <div class="OrderItemContent">
                                            <div class="OrderName" ProductName>AA</div>
                                            <div class="OrderQuantity" ProductQuantity>( qty : 1 )</div>
                                            <div class="OrderOtherInfo">
                                                <p class="qty" ProductVaraint>test: 1</p>
                                                <p class="OrderItemPrice" ProductPrice>
                                                    Price: €0.00
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <table class="RentMyTable OrderSummaryTable">
                                <tfoot>
                                    <tr class="cart-subtotal">
                                        <th>Subtotal</th>
                                        <td Subtotal>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="cart-subtotal">
                                        <th>Optional Services</th>
                                        <td OptionalService>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="cart-subtotal">
                                        <th>Deposit Amount</th>
                                        <td DepositAmount>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="cart-subtotal">
                                        <th>Tax</th>
                                        <td TaxAmount>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="cart-subtotal">
                                        <th lbl_shipping>Shipping Charge</th>
                                        <td ShippingCharge>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="cart-subtotal">
                                        <th LblDeliveryTax>Delivery Tax</th>
                                        <td DeliveryTax>
                                            $0.00
                                        </td>
                                    </tr>
                                    <tr class="order-total">
                                        <th>Total</th>
                                        <td>
                                            <strong TotalAmount>
                                                $0.00
                                            </strong>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>

                            <div class="RentMyOptionalService" AdditionalCharges>
                                <div class="RentMyRow">
                                    <div class="RentMyFullwidth">
                                        <div class="RentMyAdditionalChargeTitle">Optional Services</div>
                                    </div>
                                    <!-- Start -->
                                    <div class="RentMyFullwidth" SingleCharge>
                                        <div class="RentMyRow">
                                            <div class="RentMyCheckboxInline">
                                                <label class="RentMyCheckbox" Title>
                                                    <input type="checkbox">
                                                    give donate
                                                    <span></span>
                                                </label>
                                            </div>
                                            <div class="RentMyOptionalServiceContent" FeeAmountsAndShowInputAmout>
                                                <div class="RentMyBtnToolbar">
                                                    <div class="RentMyBtnGroup me-0">
                                                        <button type="button" class="RentMyGroupBtn"
                                                            FeeAmounts>10%</button>
                                                        <button type="button"
                                                            class="RentMyGroupBtn RentMyInputAmountBtn"
                                                            ShowInputAmount>Input Amount</button>
                                                    </div>
                                                    <select class="RentMyInputField mt-0" SelectOptions>
                                                        <option>op, op2</option>
                                                    </select>
                                                </div>
                                                <div class="RentMyInputAmountArea" InputAmountArea>
                                                    <div class="RentMyInputGroup">
                                                        <input class="RentMyInputField" type="text" InputAmount />
                                                        <div class="RentMyInputGroupAppend">
                                                            <button type="button"
                                                                class="RentMyGroupBtn RentMyOptionalOkBtn" OkButton><i
                                                                    class="fa fa-check"></i></button>
                                                            <button type="button"
                                                                class="RentMyGroupBtn RentMyOptionalCancelBtn"
                                                                CancelButton><i class="fa fa-times"></i></button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- End -->
                                </div>
                            </div>

                            <div class="checkout-checkbox">
                                <div class="CreateCustomerCheckbox" IsCustomerAccountDiv>
                                    <label class="RentMyCheckbox">
                                        <input type="checkbox" IsCustomerAccount>
                                        Create an account to make ordering faster in the future
                                        <span></span>
                                    </label>
                                </div>
                                <div class="TermsConditionsCheckbox" TermsConditions>
                                    <label class="RentMyCheckbox">
                                        <input type="checkbox">
                                        I have read and agree with the <a style="cursor: pointer;" ShowPopup>terms &amp;
                                            conditions</a>
                                        <span></span>
                                    </label>
                                </div>
                            </div>

                            <div class="SignatureContainer" SingnatureArea>
                                <div class="SignaturePad">
                                    <div class="SignaturePadBody">
                                        <canvas></canvas>
                                    </div>
                                </div>
                                <div class="SignatureFooter">
                                    <p>Signature</p>
                                    <div class="UndoClear">
                                        <a Clear>Clear</a>
                                        <a Undo>Undo</a>
                                    </div>
                                </div>
                            </div>

                            <div class="CheckoutBackcartPlaceorder">
                                <a class="RentMyBtn RentMyBackCartBtn" href="#" BackToCartBtn><i
                                        class="fa fa-backward"></i>
                                    &nbsp;Back to Cart</a>
                                <button type="button" class="RentMyBtn RentMyPlaceOrder" PlaceOrderBtn successMessage=""
                                    errorMessage="">Place
                                    Order</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="rentmy.js" defer></script>
</body>

</html>