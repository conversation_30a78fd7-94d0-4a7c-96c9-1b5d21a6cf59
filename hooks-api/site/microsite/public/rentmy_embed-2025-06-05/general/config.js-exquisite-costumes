(function (d) {
    window.DOMAIN = 'http://microsite.localhost/rentmy_embed/general/';
    window.RENTMY_GLOBAL = {
      store_id: 2162,
      locationId: 2307,
      store_name: "exquisite-costumes",
      access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.uZYWMPfvMSPSuhrCfEBcycpqgDphtAnPol4NI1LZtNQ",
      hide_checkoutPage_createAccount_checkbox: true,
      afterOrder: {
        paymentSuccessUrl: 'order-complete.html',
        paymentCancelUrl: 'index.html',
        forIframe_topMode: true,
      },
      emDateTimePicker: {
        detailsPage_useRangePicker_for_endDate: true,
      },
      home_url: 'index.html',
      page: {
        products_list: "index.html",
        product_details: "product-details.html?uid={uuid}",
        package_details: "package-details.html?uid={uuid}",
        cart: "cart.html",
        checkout: "checkout.html",
        login: "login.html",
        logout: "login.html",
        registration: "register.html",
        reset_password: "customer-reset-password.html",
        customer_profile: "customer-profile.html",
        customer_change_password: "customer-change-password.html",
        customer_change_avatar: "customer-change-avatar.html",
        customer_order_history: "customer-order-history.html",
        order_details: "customer-order-details.html",
      },
      detailsPage_priceLimitShowFirst: 5,
    }
    window.addEventListener('message', function (e) {
      if (e.data?.RENTMY_GLOBAL) e.source.postMessage({ outside: true, RENTMY_GLOBAL }, e.origin);
      if (e.data.action === 'goRentMyPaymentPage') { window.open(e.data.url, '_self') }
    });
  })(document);