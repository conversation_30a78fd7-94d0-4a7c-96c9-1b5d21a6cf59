(function (d) {
    window.DOMAIN = '';
    window.RENTMY_GLOBAL = {
      store_id: 2970,
      locationId: 3178,
      store_name: "fashion2",
      access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0yNiAxMToxMzo0MyIsInN0b3JlX2lkIjoyOTcwLCJsb2NhdGlvbiI6MzE3OCwiZXhwaXJlIjoiMjAyNC0wOC0yNyAxMToxMzowMCIsImlzX29ubGluZSI6MCwic291cmNlIjoid2l4IiwiZGlzYWJsZV9kZWZhdWx0X3RpbWUiOmZhbHNlfQ.fNIPB-42RfBE6TLS-0L4d8sx92lIhCl2-jevmHPsnTg",
      afterOrder: {
        paymentSuccessUrl: 'order-complete.html',
        paymentCancelUrl: 'index.html',
        forIframe_topMode: true,
      },
      emDateTimePicker: {
        detailsPage_useRangePicker_for_endDate: true,
      },
      home_url: 'index.html',
      page: {
        products_list: "index.html",
        product_details: "product-details.html?uid={uuid}",
        package_details: "package-details?uid={uuid}",
        cart: "cart.html",
        checkout: "checkout.html",
      },
      detailsPage_priceLimitShowFirst: 5,
    }
    window.addEventListener('message', function (e) {
      if (e.data?.RENTMY_GLOBAL) e.source.postMessage({ outside: true, RENTMY_GLOBAL }, e.origin);
      if (e.data.action === 'goRentMyPaymentPage') { window.open(e.data.url, '_self') }
    });
  })(document);