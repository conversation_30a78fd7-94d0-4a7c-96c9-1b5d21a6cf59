<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="config.js"> </script>
</head>

<body>
    <main class="container my-5">
        <div id="RentMyCustomerChangeAvatarContainer" class="RentMyWrapper RentMyCustomerPortalWrapper">
            <div class="RentMyCustomPortalRow">
                <div class="RentMyLeftSidebarmenu">
                    <div class="RentMyLeftSidebarmenuInner" SideBar>
                        <div class="RentMyProfileImge">
                            <img src="" alt="" ProfileImage>
                        </div>
                        <h5 class="RentMyProfileName">{{ customer_name }}</h5>
                        <div class="RentMySideMenu">
                            <ul>
                                <li><a RentMyPageLink="customer_profile">Profile</a></li>
                                <li><a RentMyPageLink="customer_change_password">Change Password</a></li>
                                <li><a RentMyPageLink="customer_change_avatar" class="active">Change Avatar</a></li>
                                <li><a RentMyPageLink="customer_order_history">Order History</a></li>
                                <li><a class="rentmy_logout_btn">Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="RentMyRightContent">
                    <div class="RentMyPageHeader">
                        <h3>Change Avatar</h3>
                        <div class="RentMyPageHeaderRightSide">
        
                        </div>
                    </div>
                    <div class="RentMyContentBody">
                        <div class="RentMyContentBodyInner">
                            <form id="RentMyFileUploadForm">
                                <div class="RentMyRow">
                                    <div class="RentMyAlertMessage RentMyFullwidth"></div>
                                    <div class="RentMyInputGroup RentMyFullwidth">
                                        <label for="file">Upload Image (Maximum file size 2MB)</label>
                                        <div class="RentMyFileUpload" FileUploadArea>
                                            <div class="FileSelect">
                                                <div class="FileSelectButton">Choose File</div>
                                                <div class="FileSelectName" FileName>No file chosen...</div> 
                                                <input type="file" name="chooseFile">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="RentMyButtonGroup RentMyNotBetween">
                                        <button id="RentMyFileUploadSubmitBtn" type="button" class="RentMyBtn RentMyUploadBtn">Upload</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="rentmy.js" defer></script>
</body>

</html>