<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="config.js"> </script>
</head>

<body>
    <main class="container my-5">
        <div id="RentMyNoticeArea"></div>
        <div id="RentMyCustomerOrderDetails" class="RentMyWrapper RentMyCustomerPortalWrapper">
            <div class="RentMyCustomPortalRow">
                <div class="RentMyLeftSidebarmenu">
                    <div class="RentMyLeftSidebarmenuInner" SideBar>
                        <div class="RentMyProfileImge">
                            <img rentmy_customer_profile_image="true" src="" alt="">
                        </div>
                        <h5 class="RentMyProfileName">{{ customer_name }}</h5>
                        <div class="RentMySideMenu">
                            <ul>
                                <li><a RentMyPageLink="customer_profile">Profile</a></li>
                                <li><a RentMyPageLink="customer_change_password">Change Password</a></li>
                                <li><a RentMyPageLink="customer_change_avatar">Change Avatar</a></li>
                                <li><a RentMyPageLink="customer_order_history" class="active">Order History</a></li>
                                <li><a class="rentmy_logout_btn">Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="RentMyRightContent">
                    <div class="RentMyPageHeader">
                        <h3>Order Details: #<span>{{order_id}}</span></h3>
                        <div class="RentMyPageHeaderRightSide">
                            <span><a href="#" class="RentMyBtn RentMyStatusBtn rounded">{{order_status}}</a></span>
                        </div>
                    </div>
                    <div class="RentMyContentBody">
                        <div class="RentMyContentBodyInner">

                            <!-- Summery and Billing Details css  -->
                            <div class="RentMyRow SummeryBillingDetails">
                                <div class="RentMyHalfwidth">
                                    <div class="RentMyCard">
                                        <div class="RentMyCardHeader">
                                            <h3>Summary</h3>
                                            <div class="RentMyCardRightSide"></div>
                                        </div>
                                        <div class="RentMyCardBody">
                                            <div class="RentMyRow">
                                                <div class="RentMyHalfwidth">
                                                    <p class="RentMyDestitle"><strong>Order ID:</strong> <span>{{order_id}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Quantity:</strong> <span>{{order_quantity}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Payment Status:</strong> <span>{{order_payment_status}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Status:</strong> <span>{{order_status}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Order Date & Time:</strong> <span>{{order_date_time}}</span></p>
                                                </div>
                                                <div class="RentMyHalfwidth">
                                                    <p class="RentMyDestitle"><strong>Subtotal:</strong> <span>{{order_subtotal}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Delivery Charge Total:</strong> <span>{{order_delivery_charge_total}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Total Discount:</strong> <span>{{order_total_discount}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Sales Tax:</strong> <span>{{order_sales_tax}}</span></p>
                                                    <p class="RentMyDestitle"><strong>DE (8%):</strong> <span>{{order_de}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Grand Total:</strong>  <span>{{order_grand_total}}</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="RentMyHalfwidth">
                                    <div class="RentMyCard">
                                        <div class="RentMyCardHeader">
                                            <h3>Billing & Shipping Details</h3>
                                            <div class="RentMyCardRightSide"></div>
                                        </div>
                                        <div class="RentMyCardBody">
                                            <div class="RentMyRow">
                                                <div class="RentMyHalfwidth">
                                                    <p class="RentMyDestitle"><strong>Name:</strong> <span>{{billing_name}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Mobile:</strong> <span>{{billing_mobile}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Address Line 1:</strong> <span>{{billing_address_line_1}}</span></p>
                                                    <p class="RentMyDestitle"><strong>City:</strong> <span>{{billing_city}}</span></p>
                                                    <p class="RentMyDestitle"><strong>State:</strong> <span>{{billing_state}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Company:</strong> <span>{{billing_company}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Address Info:</strong> <span>{{billing_address_info}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Signature:</strong> <span>{{signature}}</span></p>
                                                </div>
                                                <div class="RentMyHalfwidth">
                                                    <p class="RentMyDestitle"><strong>Email:</strong> <span>{{billing_email}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Country:</strong> <span>{{billing_country}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Address Line 2:</strong> <span>{{billing_address_line_2}}</span></p>
                                                    <p class="RentMyDestitle"><strong>Zipcode:</strong> <span>{{billing_zip}}</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Inventory Items area  -->
                            <div class="RentMyCard InventoryRentMyCard">
                                <div class="RentMyCardHeader">
                                    <h3>Inventory Items</h3>
                                    <div class="RentMyCardRightSide">
                                        <button id="RentMyAddInventoryItemBtn" class="RentMyBtn RentMyAddItem">Add Item</a>
                                    </div>
                                </div>
                                <div class="RentMyCardBody">
                                    <div rentmy-add-inventory-search="true"></div>   
                                    <div rentmy-order-product-details="true"></div>
                                    <div rentmy-order-date-time="true" class="mb-2">
                                        <strong>Rental Dates:</strong> <span>22 Mar 2024 - 24 Mar 2024</span>
                                    </div>
                                    <div id="RentMyOrderInventory" class="RentMyTableResponsive">
                                        <table class="RentMyTable RentMyTableStriped">
                                            <thead>
                                                <tr>
                                                    <th width="10%">Image</th>
                                                    <th width="25%">Description</th>
                                                    <th width="15%">Price</th>
                                                    <th width="20%">Quantity</th>
                                                    <th width="10%">Subtotal</th>
                                                    <th width="10%">Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{product_images}}</td>
                                                    <td>{{product_discription}}</td>
                                                    <td> {{subtotal}} </td>
                                                    <td>
                                                        {{quantity_functions}}
                                                    </td>
                                                    <td>{{price}}</td>
                                                    <td class="TextCenter">
                                                        <a href="#" class="TextDanger"><i class="fa fa-trash"></i></a>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Payments area  -->
                            <div class="RentMyCard PaymentsRentMyCard">
                                <div class="RentMyCardHeader">
                                    <h3>Payments</h3>
                                    <div class="RentMyCardRightSide">
                                        <button id="RentMyAddPaymentBtn" class="RentMyBtn RentMyAddPaymentBtn">Add Payment</a>
                                    </div>
                                </div>
                                <div class="RentMyCardBody">
                                    <div class="RentMyPaymentOption">
                                        <h5><strong>Total Amount:</strong> <span>{{total_payment_amount}}</span> </h5>
                                        <h5><strong>Total Paid:</strong> <span>{{total_paid_amount}}</span> </h5>
                                        <h5><strong>Due Amount:</strong> <span>{{total_due_amount}}</span> </h5>
                                    </div>
                                    <div id="RentMyOrderPayment" class="RentMyTableResponsive">
                                        <table class="RentMyTable RentMyTableStriped">
                                            <thead>
                                                <tr>
                                                    <th width="30%">Payment Type</th>
                                                    <th width="15%">Payment Status</th>
                                                    <th width="15%">Paid Amount</th>
                                                    <th width="40%">Note</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{content_method}}</td>
                                                    <td>{{status}}</td>
                                                    <td>{{payment_amount}}</td>
                                                    <td> {{note}} </td>
                                                </tr>
                                            </tbody>
                    
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes area -->
                            <div class="RentMyCard NotesRentMyCard">
                                <div class="RentMyCardHeader">
                                    <h3>Notes</h3>
                                    <div class="RentMyCardRightSide">
                                        <button id="RentMyNoteAddBtn" class="RentMyBtn RentMyAddNoteBtn">Add Note</button>
                                    </div>
                                </div>
                                <div class="RentMyCardBody">
                                    <form id="RentMyNoteAddForm">
                                        <div class="RentMyInputGroup">
                                            <label for="note">Note</label>
                                            <textarea row="10" id="RentMyNote" name="note" placeholder="Enter note" autocomplete="off" class="RentMyInputField"></textarea>
                                        </div>
                                        <!-- <div class="RentMyInputGroup">
                                            <div class="RentMyCustomFile">
                                                <input type="file" id="RentMyFile" name="file" class="RentMyCustomFileInput">
                                                <label for="file" class="RentMyCustomFileLabel"> Choose file </label>
                                            </div>
                                        </div> -->
                                        <div class="RentMyButtonGroup RentMyNotBetween">
                                            <button id="RentMyNoteSubmitBtn" type="button" class="RentMyBtn RentMyNoteBtn">
                                                Submit
                                            </button>
                                            <button id="RentMyNoteCancelBtn" type="button" class="RentMyBtn RentMyNoteCancelBtn">Cancel</button>
                                        </div>
                                    </form>
                                    <div id="RentMyOrderNotes" class="RentMyTableResponsive">
                                        <table class="RentMyTable RentMyTableStriped">
                                            <thead>
                                                <tr>
                                                    <th width="90%">Note</th>
                                                    <th width="10%">Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>{{note}}</td>
                                                    <td class="TextCenter">
                                                        <a href="#" class="TextDanger"><i class="fa fa-trash"></i></a>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                    </div>


                </div>
            </div>
                </div>
            </div>
        </div>
    </main>

    <script src="rentmy.js" defer></script>
</body>

</html>