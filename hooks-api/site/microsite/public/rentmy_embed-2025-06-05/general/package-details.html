<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="config.js"> </script>
</head>

<body>
    <main class="container my-5">
        <div class="RentMyWrapperPackageDetails RentMyWrapper" RentMyData="">
            <div RentmyPackageComponent>
                <div class="RentMyProductPackageRow">
                    <div class="RentMyProductDetilsImg">
                        <div class="RentMyProductDetailsImgList">
                            <ul RentMyProductImages>
                                <li class="ActiveImg">
                                    <img src="" alt="" />
                                </li>
                                <li>
                                    <img src="" alt="" />
                                </li>
                                <li>
                                    <img src="" alt="" />
                                </li>
                                <li>
                                    <img src="" alt="" />
                                </li>
                            </ul>
                        </div>
                        <div class="RentMyProductDetailsImgShow">
                            <img RentMyProductImage src="" alt="" />
                        </div>
                    </div>
                    <div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo>
                        <div class="product-payment-details">

                            <h2 class="RentMyProductName" RentMyProductName>{{ product_name }}</h2>

                            <div class="RentMyBuyRentToggle" RentMyBuyRentToggle>
                                <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                                    <input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch />
                                    <div class="ToggleSwitchRound"></div>
                                </label>
                            </div>

                            <h2 class="RentMyProductPrice" RentMyProductPrice>{{ product_price_text }}</h2>


                            <div class="RentMyRecurring" RentMyRecurring>
                                <h6 RecurringTitle>Recurring Pricing</h6>
                                <ul RecurringList>
                                    <li RecurringItem>
                                        Recurring
                                    </li>
                                </ul>
                            </div>


                            <div class="RentMyVariant" RentMyVariant>
                                <h6 VariantTitle>Rent My Variant Sizes</h6>
                                <ul VariantList>
                                    <li VariantItem>Small</li>
                                </ul>
                            </div>


                            <div class="RentMyProductOptions" RentMyProductOptions>
                                <div class="CustomFieldInner">
                                    <h6 ProductOptionsTitle class="mt-3" >Product Options</h6>
                                    <ul ProductOptionsItem>
                                        
                                        <div type-select >
                                            <p fieldLabel class="m-0 mt-2" ></p>
                                            <select ></select>
                                        </div>
                                        <div type-button >
                                            <p fieldLabel class="m-0 mt-2" ></p>
                                            <li fieldValue ></li>
                                        </div>
                                        <div type-radio >
                                            <!-- <p fieldLabel class="m-0 mt-2" ></p>
                                            <li fieldValue ></li> -->
                                        </div>
                                        <div type-richtext >
                                            <p fieldLabel class="m-0 mt-2" ></p>
                                            <li fieldValue ></li>
                                        </div>
                                    </ul>
                                </div>
                            </div>


                            <div class="RentMyRentalStartDate" RentMyRentalStartDate>
                                <div usualDateRange>
                                    <h6 RentalStartDateTitle>Select Rental Start Date</h6>
                                    <ul RentalStartDateList>
                                        <li Today>Today</li>
                                        <li Tomorrow>Tomorrow</li>
                                        <li PickDate>Pick Date</li>
                                    </ul>
                                    <span RentalStartDateSelectedLabel>
                                        Today 08:00 AM
                                    </span>
                                </div>
                            </div>


                            <div class="RentMyRentalDateRange" RentMyRentalDateRange>
                                <h6 RentalDateRangeTitle>
                                    Rental Date Range
                                </h6>
                                <ul RentalDateRangeList>
                                    <li RentalDateRangeItem>
                                        1 hour <br />
                                        $10.00
                                    </li>
                                </ul>
                            </div>


                            <div class="RentMyRentalDateRange mb-3" RentMyRentalEndDate>
                                <ul>
                                    <li class="mt-0" RentalEndDatePicker>
                                        <span rangePickerLabel >Pick Date Range</span>
                                    </li>
                                </ul>
        
                                <span RentalEndDateSelectedLabel>
                                    Today 09:00 AM
                                </span>
                            </div>

                            <div class="RentMyExactSelectDuration" RentMyExactSelectDuration>
                                <h6 RentMyExactSelectDurationTitle>
                                    Select Duration
                                </h6>
                                <ul RentMyExactSelectDurationList>
                                    <li class="ExactSelectDurationActive" RentMyExactSelectDurationItem>
                                        Exact Select Duration
                                    </li>
                                </ul>
                            </div>

                            <div class="RentMyExactSelectTime" RentMyExactSelectTime>
                                <h6 RentMyExactSelectTimeTitle>
                                    Select Exact Start time
                                </h6>
                                <ul RentMyExactSelectTimeList>
                                    <li class="ExactSelectTimeActive" RentMyExactSelectTimeItem>
                                        Extact Times
                                    </li>
                                </ul>
                            </div>


                            <div class="RentMyDeliveryOptions" RentMyDeliveryOptions>
                                <h6 DeliveryOptionsTitle>Delivery Options</h6>
                                <ul DeliveryOptionsList>
                                    <li DeliveryOptionsItem>Local Move</li>
                                </ul>
                            </div>

                            <div class="RentMySelectLocation" RentMySelectLocation>
                                <h6 SelectLocationTitle>Select Location</h6>
                                <ul SelectLocationList>
                                    <li SelectLocationItem>Default location</li>
                                </ul>
                            </div>

                            <div class="QuantityContainer" RentmyQuantityContainer>
                                <label QuantityContainerTitle>Quantity</label>
                                <div class="QuantityBtn">
                                    <button class="RentMyBtn" QuantityDecrementBtn>-</button>
                                    <input type="text" autocomplete="off" name="qty" class="InputQuantity" NumberOfQuantity />
                                    <button class="RentMyBtn" QuantityIncrementBtn>+</button>
                                </div>
        
                                <small class="info">
                                    <span RentmyAvailableLabel>Available:</span>
                                    <span RentmyAvailableQty class="ms-1" >17</span>
                                </small>
                            </div>


                            <div class="RentMyCartBtnArea" RentMyCartBtnArea>
                                <button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn>ADD TO CART</button>
                            </div>

                        </div>
                    </div>
                    <div class="RentMyProductPackageArea" RentMyProductPackageArea>
                        <div class="RentMyProductPackageAreaInner" RentMyProductPackageAreaInner>
                            <h6 RentMyProductPackageAreaTitle>Package includes</h6>
                            <div RentMyProductPackageContent></div>
                            <div class="PackageSingleProduct" PackageSingleProduct>
                                <div class="PackageProductName" PackageProductName>
                                    <h5 PackageProductNameTitle>test product buy (2)</h5>
                                </div>
                                <div class="PakageProductVarient" PakageProductVarient>
                                    <div class="PakageProductVarientInner" PakageProductVarientInner>
                                        <select class="form-control" PakageProductVarientInnerSelect>
                                            <option PakageProductVarientInnerOption value="276077"> size: Blue, color:
                                                red </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="RentMyProductDescription">
                <h3 class="RentMyProductDesTitle">Product Description</h3>
                <div class="RentMyProductDesBody" RentMyProductDescription>
                </div>
            </div>
            <div class="RentMyRelatedProduct">
                <h3 class="RentMyRelatedProductTitle">Related Products</h3>
                <div class="RentMyRelatedProductBody">
                    <div class="RentMyRow" RentMyRelatedProducts>
                        <div class="RentMyProductItem" RentMyProductItem>
                            <div class="RentMyProductItemInner">
                                <div class="RentMyProductImg">
                                    <a href="#" RentMyProductImageUrl>
                                        <img RentMyProductImage src="" class="ProductImg" alt="" />
                                    </a>
                                </div>
                                <div class="RentMyProductBody">
                                    <h4 class="ProductName" RentMyProductName> Product_name </h4>
                                    <h5 class="ProductPrice" RentMyProductPrice> product_price </h5>
                                    <div class="ProductButton">
                                        <a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn>View Details</a>
                                        <button class="ProductCartBtn" href="#" RentMyAddToCartBtn>Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Mini Cart Widget -->
        <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div>
    </main>

    <script src="rentmy.js" defer></script>
</body>

</html>