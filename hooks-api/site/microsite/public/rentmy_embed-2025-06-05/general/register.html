<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="config.js"> </script>
</head>

<body>
    <main class="container my-5">
        <div id="RentMyCustomerRegistrationContainer" class="RentMyWrapper">
            <div class="RegistrationElement">
                <h3 class="RegistrationTitle">Signup</h3>
                <div class="RentMyAlertMessage"></div>
                <form class="RentMyFrom" id="RentMyCustomerRegistrationForm">
                    <div class="RentMyRow">
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="first_name" class="RentMyInputField" placeholder="First name">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="last_name" class="RentMyInputField" placeholder="Last name">
                        </div>
                        <div class="RentMyInputGroup RentMyFullwidth">
                            <input type="email" name="email" class="RentMyInputField" placeholder="Email">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="company_name" class="RentMyInputField" placeholder="Company name">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="contact" class="RentMyInputField" placeholder="Contact name">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="password" name="password" class="RentMyInputField" placeholder="Password">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="password" name="confirm_password" class="RentMyInputField"
                                placeholder="Password Confirm">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="address_line1" class="RentMyInputField"
                                placeholder="Enter a location">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="address_line2" class="RentMyInputField"
                                placeholder="Address line2">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <select class="RentMyInputField" name="country">
                                <option value=""> Country </option>
                            </select>
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="city" class="RentMyInputField" placeholder="City">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="state" class="RentMyInputField" placeholder="State">
                        </div>
                        <div class="RentMyInputGroup RentMyHalfwidth">
                            <input type="text" name="zipcode" class="RentMyInputField" placeholder="Zip code">
                        </div>
                        <div class="RentMyButtonGroup RentMyHalfwidth">
                            <button type="submit" class="RentMyBtn RegistrationBtn">Sign up</button>
                            <a href="#" class="LoginHere" RentMyPageLink="login">Login here</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script src="rentmy.js" defer></script>
</body>

</html>