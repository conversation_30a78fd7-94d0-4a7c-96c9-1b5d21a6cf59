<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" href="preloader.css">
    <link rel="stylesheet" href="outhouse.css">
    <script src="global.js"></script>
</head>
<body>
    <main class="container my-5">
        <div class="RentMyWrapperProductList RentMyWrapper" RentMyData="limit=21">
            <div class="RentMyProductRow RentMyProductListRow">
                <div class="RentMyFilterArea" RentMyFilterArea>
                    <div class="RentMyFilterAreaInner">
                        <div class="RentMyCategory">
                            <h3 class="RentMyFilterTitle">Category</h3>
                            <div class="CategoryMenuList scrollbar">
                                <ul class="CategoryMenu" RentMyFilterByCategory>
                                    <li><a href="#"><span>Choose One</span> <i class="fa fa-angle-right"></i></a></li>
                                </ul>
                            </div>
                        </div>
    
                        <div class="RentMyFilter">
                            <div class="RentMyFilterList">
                                <h3 class="RentMyFilterTitle">Filter</h3>
                                <div class="FilterCheckbox scrollbar" RentMyFilterByTag>
                                    <label class="RentMyCheckbox">
                                        <input type="checkbox">
                                        <span>&nbsp;&nbsp;</span>
                                        <a href="#">Price</a>
                                    </label>
                                    <label class="RentMyCheckbox">
                                        <input type="checkbox">
                                        <span>&nbsp;&nbsp;</span>
                                        <a href="#">Quantity</a>
                                    </label>
                                    <label class="RentMyCheckbox">
                                        <input type="checkbox">
                                        <span>&nbsp;&nbsp;</span>
                                        <a href="#">Name</a>
                                    </label>
                                </div>
                            </div>
    
                            <div class="RentMyPriceArea">
                                <h3 class="RentMyFilterSubTitle">Price</h3>
                                <div class="RentMyPrice">
                                    <div class="RentMyRow" RentMyFilterByPrice>
                                        <div class="RentMyInputGroup RentMyHalfwidth">
                                            <label>Min</label>
                                            <input type="text" class="RentMyInputField" RentMyMinPrice id/>
                                        </div>
                                        <div class="RentMyInputGroup RentMyHalfwidth">
                                            <label>Max</label>
                                            <input type="text" class="RentMyInputField" RentMyMaxPrice />
                                        </div>
                                        <div class="RentMyButtonGroup RentMyNotBetween">
                                            <button class="RentMyBtn RentMyBtnBlack" RentMyMinMaxSubmitBtn>Submit</button>
                                            <button class="RentMyBtn RentMyBtnRed" RentMyMinMaxClearBtn>Clear</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="RentMyTypeArea">
                                <h3 class="RentMyFilterSubTitle">Type</h3>
                                <div class="RentMyType" RentMyFilterByRentalType>
                                    <label class="RentMyRadio">
                                        <input type="radio" name="RentalType" value="rent" />
                                        Rent
                                        <span></span>
                                    </label>
                                    <br />
                                    <label class="RentMyRadio"><input type="radio" name="RentalType" value="buy" />
                                        Buy
                                        <span></span>
                                    </label>
                                    <br />
                                    <label class="RentMyRadio">
                                        <input type="radio" name="RentalType" value="all" />
                                        All
                                        <span></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Product Area -->
                <div class="RentMyProductArea">
                    <div class="RentMyRow SortProductRow">
                        <div class="SortProduct">
                            <label>Sort By</label>
                            <select class="RentMyInputField" RentMyShortByDropdown>
                                <option value="sort_by=product_name&sort_dir=ASC">Product name A-Z</option>
                                <option value="sort_by=product_name&sort_dir=DSC">Product name Z-A</option>
                                <option value="sort_by=rent_price&sort_dir=ASC">Rental price low to high</option>
                                <option value="sort_by=rent_price&sort_dir=DSC">Rental price high to low</option>
                                <option value="sort_by=buy_price&sort_dir=ASC">Sale price low to high</option>
                                <option value="sort_by=buy_price&sort_dir=DSC">Sale price high to low</option>
                            </select>
                        </div>
                    </div>
                    <div class="RentMyRow">
                        <div class="RentMyProductItem" RentMyProductItemSample>
                            <div class="RentMyProductItemInner">
                                <div class="RentMyProductImg">
                                    <a href="#" RentMyProductImageUrl>
                                        <img RentMyProductImage src="" class="ProductImg" alt="" />
                                    </a>
                                    <div class="RentMyProductOverlay">
                                    </div>
                                </div>
                                <div class="RentMyProductBody">
                                    <h4 class="ProductName" RentMyProductName><a href="#">{{ product_name }}</a></h4>
                                    <h5 class="ProductPrice" RentMyProductPrice>{{ product_price }}</h5>
                                    <div class="ProductButton">
                                        <a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn>View Details</a>
                                        <button class="ProductCartBtn" href="#" RentMyAddToCartBtn>Add to Cart</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="rentmy.js" defer></script>
</body>
</html>

