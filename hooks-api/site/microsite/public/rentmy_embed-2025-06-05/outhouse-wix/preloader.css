.RentMyWrapper:not([wrapper-is-ready]) {
    position: relative;
}

.RentMyWrapper:not([wrapper-is-ready]) * {
    visibility: hidden;
}

.RentMyWrapper:not([wrapper-is-ready])::after,
.RentMyWrapper:not([wrapper-is-ready])::before {
    position: absolute;
    left: var(--wraper-loader-left, calc(50% - 30px));
    top: var(--wraper-loader-top, calc(100px));
    z-index: 9999999999999;
    content: "";  
    box-sizing: border-box;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 4px solid var(--rentmy-preloader-color, #888);
    position: absolute;
    animation: animloader 1.8s linear infinite;
}

.RentMyWrapper:not([wrapper-is-ready])::after{
    animation-delay: 0.9s;
}

@keyframes animloader {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.RentMyWrapper:not(.usingInModal)[wrapper-is-ready] {
    animation: page-fade var(--rentmy-page-fade-duration, 1s) ease-in;
}

.RentMyWrapper.usingInModal[wrapper-is-ready] {
    animation: page-fade 0.1s ease-in !important;
}

@keyframes page-fade {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

[temp-hidden] {
    visibility: hidden;
}


.RentMy-login-page:not([show]),
.RentMy-client-portal-page:not([show]) {
    display: none;
}

.RentMy-login-page[show],
.RentMy-client-portal-page[show] {
    display: block !important;
}

a{
    cursor: pointer;
}