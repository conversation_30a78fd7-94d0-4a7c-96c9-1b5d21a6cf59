<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link rel="stylesheet" href="preloader.css">
    <link rel="stylesheet" href="outhouse.css">
    <script src="global.js"></script>
</head>
<body>
    <main class="container my-5">
        <div class="RentMyWrapperProductDetails RentMyWrapper" RentMyData="uid=11445a623ee211ef8f0d023d0c634269">
            <div class="RentMyProductDetailsRow">
                <div class="RentMyProductDetilsImg">
                    <div class="RentMyProductDetailsImgList">
                        <ul RentMyProductImages>
                            <li class="ActiveImg">
                                <img src="" alt="" />
                            </li>
                            <li>
                                <img src="" alt="" />
                            </li>
                            <li>
                                <img src="" alt="" />
                            </li>
                            <li>
                                <img src="" alt="" />
                            </li>
                        </ul>
                    </div>
                    <div class="RentMyProductDetailsImgShow">
                        <img RentMyProductImage src="" alt="" />
                    </div>
                </div>
    
                <div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo>
                    <div class="product-payment-details">
    
                        <h2 class="RentMyProductName" RentMyProductName product_name>Product Name</h2>
    
                        <div class="RentMyBuyRentToggle" RentMyBuyRentToggle>
                            <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                                <input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch />
                                <div class="ToggleSwitchRound"></div>
                            </label>
                        </div>
    
                        <h2 class="RentMyProductPrice" RentMyProductPrice product_price_text>$0.00</h2>
    
                        <div class="RentMyRecurring" RentMyRecurring>
                            <h6 RecurringTitle>Recurring Pricing</h6>
                            <ul RecurringList>
                                <li RecurringItem>
                                    Recurring
                                </li>
                            </ul>
                        </div>
    
                        <div class="RentMyVariant" RentMyVariant>
                            <h6 VariantTitle>Rent My Variant Sizes</h6>
                            <ul VariantList>
                                <li VariantItem>Small</li>
                            </ul>
                        </div>
    
                        <div class="BundleItemsArea mb-3">
                            <div class="BundleItemRow">
                                <div class="BundleItemRowLeft">
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/61ed848f-aa49-465c-b1d9-29ce379a479d/large_photo.jpg" alt="" />
                                        </div>
                                        <!-- <div class="BundleItemQty">1x</div> -->
                                    </div>
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/26c5ae2f-9db4-43f7-9e95-c5c427640bd8/large_photo.jpg" alt="" />
                                        </div>
                                        <!-- <div class="BundleItemQty">1x</div> -->
                                    </div>
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/96a7b6b2-663d-44f5-8048-7a3036161a95/large_photo.jpg" alt="" />
                                        </div>
                                        <!-- <div class="BundleItemQty">1x</div> -->
                                    </div>
                                    <!-- <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/2a9c3330-b309-4887-817a-30205c946058/large_photo.jpg" alt="" />
                                        </div>
                                    </div> -->
                                </div>
                                <div class="BundleItemRowRight" RentMyProductOptions>
    
                                    <div class="BundleItemText">Luxury Two-Stall Restroom Trailer</div>
    
                                    <div class="BundleItemText" ProductOptionsItem>
                                        <div type-select >
                                            <select ></select>
                                        </div>
                                        <div type-button >
                                            <li fieldValue ></li>
                                        </div>
                                        <div type-radio >
                                        </div>
                                    </div>
    
                                </div>
                            </div>
                        </div>
    
                        <!-- <div class="RmRentalOption" RentMyRentalDateRange>
                            <h6 RentalDateRangeTitle>Rental Date Range</h6>
                            <ul RentalDateRangeList>
                                <li class="" RentalDateRangeItem activeClass="RmDaterangeActive">
                                    <div class="RmRentalOptionDaytime" PricePreText> 1 day </div>
                                    <div Price> $10.00 </div>
                                </li>
                            </ul>
                        </div> -->
    
                        <div class="RentMyRentalDateRange mb-0" RentMyRentalEndDate>
                            <ul>
                                <li class="mt-0" RentalEndDatePicker>Pick Date Range</li>
                            </ul>
    
                            <span RentalEndDateSelectedLabel>
                                Today 09:00 AM
                            </span>
                        </div>
    
                        <div class="RentMyExactSelectDuration" RentMyExactSelectDuration>
                            <h6 RentMyExactSelectDurationTitle>
                                Select Duration
                            </h6>
                            <ul class="mb-4" RentMyExactSelectDurationList>
                                <li RentMyExactSelectDurationItem>
                                    Exact Select Duration
                                </li>
                            </ul>
                        </div>
    
                        <div class="RentMyExactSelectTime" RentMyExactSelectTime>
                            <h6 RentMyExactSelectTimeTitle>
                                Select Exact Start time
                            </h6>
                            <ul class="mb-4" RentMyExactSelectTimeList>
                                <li RentMyExactSelectTimeItem>
                                    Extact Times
                                </li>
                            </ul>
                        </div>
    
                        <div class="RentMyDeliveryOptions" RentMyDeliveryOptions>
                            <h6 DeliveryOptionsTitle>Delivery Options</h6>
                            <ul DeliveryOptionsList>
                                <li DeliveryOptionsItem>Local Move</li>
                            </ul>
                        </div>
    
                        <div class="RentMySelectLocation" RentMySelectLocation>
                            <h6 SelectLocationTitle>Select Location</h6>
                            <ul SelectLocationList>
                                <li SelectLocationItem>Default location</li>
                            </ul>
                        </div>
    
                        <div class="QuantityContainer" RentmyQuantityContainer>
                            <label QuantityContainerTitle>Quantity</label>
                            <div class="QuantityBtn">
                                <button class="RentMyBtn" QuantityDecrementBtn>-</button>
                                <input type="text" autocomplete="off" name="qty" class="InputQuantity" NumberOfQuantity />
                                <button class="RentMyBtn" QuantityIncrementBtn>+</button>
                            </div>
    
                            <small class="info">
                                <span RentmyAvailableLabel>Available</span>:
                                <span RentmyAvailableQty>17</span>
                            </small>
                        </div>
    
                        <div class="RentMyCartBtnArea" RentMyCartBtnArea>
                            <button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn>ADD TO CART</button>
                        </div>
    
                    </div>
                </div>
            </div>
    
            <div class="RentMyProductDescription">
                <h3 class="RentMyProductDesTitle">Product Description</h3>
                <div class="RentMyProductDesBody" RentMyProductDescription>
                </div>
            </div>
        </div>
    
        <!-- In page cart widget -->
        <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div>
    </main>
    
    <script src="rentmy.js" defer></script>
</body>
</html>