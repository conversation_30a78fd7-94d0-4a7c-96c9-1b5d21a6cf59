

var DOMAIN = 'www.example.com';

var RENTMY_GLOBAL = {
    store_id: "",
    locationId: "",
    store_name: "",
    access_token: "",
 
    emDateTimePicker: {

      // time picker ui
      timePickerUi: 'standard', // optional, Note: To change time picker view
      timePickerButtons: true, // optional, Note: To view ok and cancel button in time picker
      
      /* ------------------------------------------- */
      /*                 Details Page                */
      /* ------------------------------------------- */      

      // Start Date config
      detailsPage_startDatePicker_ajdustX: -12, //required for wix
      detailsPage_startDatePicker_ajdustY: 8, //required for wix
      detailsPage_startDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_startDate: false, //required for wix
      detailsPage_startDate_allowRightSideTimePicker: true, //required for wix

      // End Date config
      detailsPage_endDatePicker_ajdustX: -13, //required for wix
      detailsPage_endDatePicker_ajdustY: -26, //required for wix
      detailsPage_endDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_endDate: true, //required for wix
      detailsPage_endDate_allowRightSideTimePicker: true, //required for wix
      
      /* ------------------------------------------- */
      /*                   Cart Page                 */
      /* ------------------------------------------- */ 
      cartPage_datePicker_ajdustX: 0, // optional
      cartPage_datePicker_ajdustY: 0, // optional
      
      /* ------------------------------------------- */
      /*               In Page Cart Widget           */
      /* ------------------------------------------- */ 
      afterAddtoCart_open_widget: true, // optional
      afterAddtoCart_open_widget_datePicker: false, // optional
    },

    afterOrder: {
      paymentSuccessUrl: '', //required for wix
      paymentCancelUrl: '', //required for wix
      forIframe_topMode: true, //required for wix

      justEmit: false, // optional
    },

    using_in_cli_project: "",
    ajax_url: "",
    home_url: "",

    is_login_page: false, // optional
    wp_current_user: "1", // optional
    after_add_to_cart_redirecto_cart: true, // optional    
    detailsPage_priceLimitShowFirst: 3, // optional

    images: {
      default_profile_image: "",
      default_product_image: "",
      emptybag_image: "",
      forgot_password_image: "", 
      
      pickup_image: "",
      pickup_white_image: "",
      shipping_image: "",
      shipping_white_image: "",
      delivery_image: "",
      delivery_white_image: "",

      download_pdf_image: "",
      download_calendar_image: "",
      check_list_image: "",
    },

    page: {
        products_list: "",
        product_details: "",
        package_details: "",
        cart: "",
        checkout: "",
        login: "",
        logout: "",
        registration: "",
        reset_password: "",
        customer_profile: "",
        customer_change_password: "",
        customer_change_avatar: "",
        customer_order_history: "",
        order_details: "",
        event_management: "",
        rentmy_dashboard: ""
    },

    "is_varified_rentmy_event_nonce": "", // optional
}
