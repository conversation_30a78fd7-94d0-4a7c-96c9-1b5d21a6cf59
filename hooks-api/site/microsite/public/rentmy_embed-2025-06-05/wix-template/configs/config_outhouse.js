

var DOMAIN = 'https://embed.rentmy.co/outhouseaz';

var RENTMY_GLOBAL = {
    store_id: "3431",
    locationId: "3674",
    store_name: "outhouse",
    access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.y5vwsr6wzDgL9-XC5X5DIUphQRi0XqBn4LM3Gww0fTw",
 
    emDateTimePicker: {

      // time picker ui
      timePickerUi: 'standard', // optional, Note: To change time picker view
      timePickerButtons: true, // optional, Note: To view ok and cancel button in time picker
      
      /* ------------------------------------------- */
      /*                 Details Page                */
      /* ------------------------------------------- */      

      // Start Date config
      detailsPage_startDatePicker_ajdustX: -12, //required for wix
      detailsPage_startDatePicker_ajdustY: 8, //required for wix
      detailsPage_startDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_startDate: false, //required for wix
      detailsPage_startDate_allowRightSideTimePicker: true, //required for wix

      // End Date config
      detailsPage_endDatePicker_ajdustX: -13, //required for wix
      detailsPage_endDatePicker_ajdustY: -26, //required for wix
      detailsPage_endDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_endDate: true, //required for wix
      detailsPage_endDate_allowRightSideTimePicker: true, //required for wix
      
      /* ------------------------------------------- */
      /*                   Cart Page                 */
      /* ------------------------------------------- */ 
      cartPage_datePicker_ajdustX: 0, // optional
      cartPage_datePicker_ajdustY: 0, // optional
      
      /* ------------------------------------------- */
      /*               In Page Cart Widget           */
      /* ------------------------------------------- */ 
      afterAddtoCart_open_widget: true, // optional
      afterAddtoCart_open_widget_datePicker: false, // optional
    },

    afterOrder: {
      paymentSuccessUrl: 'https://www.mytexastotes.com/order-complete', //required for wix
      paymentCancelUrl: 'https://www.mytexastotes.com/catalog', //required for wix
      forIframe_topMode: true, //required for wix

      justEmit: false, // optional
    },

    using_in_cli_project: "",
    ajax_url: "",
    home_url: "product-details.html?uid=11445a623ee211ef8f0d023d0c634269",

    is_login_page: false, // optional
    wp_current_user: "1", // optional
    after_add_to_cart_redirecto_cart: true, // optional    
    detailsPage_priceLimitShowFirst: 3, // optional

    page: {
        products_list: "catalog.html",
        product_details: "product-details.html?uid={uuid}",
        package_details: "package-details?uid={uuid}",
        cart: "cart.html",
        checkout: "checkout.html",
    },

    "is_varified_rentmy_event_nonce": "", // optional
}
