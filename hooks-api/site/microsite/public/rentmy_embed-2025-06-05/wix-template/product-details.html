<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="index.css">
    <link rel="stylesheet" href="wix.css">
    <link rel="stylesheet" href="responsive.css">

    <script src="EventEmitter.js"></script>
    <script src="config.js"> </script>
    <script src="script_prod.js"></script>
    <script>
        RentMyEvent.add_filter('detailsPage:endDate:useRangePicker', (bool) => true);
    </script>

    <style>
        :root{
            /* --rentmy-primary-color: rgb(27, 27, 27); */
            /* --rentmy-cart-widget-color: rgb(219, 144, 4); */
        }
    </style>
    
</head>

<body class="container">
    <main class="my-5">

        <div class="RentMyWrapperProductDetails RentMyWrapper" RentMyData="uid=11445a623ee211ef8f0d023d0c634269">
            <div class="RentMyProductDetailsRow">
                <div class="RentMyProductDetilsImg">
                    <div class="RentMyProductDetailsImgList">
                        <ul RentMyProductImages>
                            <li class="ActiveImg">
                                <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                                    alt="product img" />
                            </li>
                            <li>
                                <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg"
                                    alt="product img" />
                            </li>
                            <li>
                                <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                                    alt="product img" />
                            </li>
                            <li>
                                <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg"
                                    alt="product img" />
                            </li>
                        </ul>
                    </div>
                    <div class="RentMyProductDetailsImgShow">
                        <img RentMyProductImage
                            src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                            alt="product show img" />
                    </div>
                </div>

                <div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo>
                    <div class="product-payment-details">

                        <h2 class="RentMyProductName" RentMyProductName product_name>Product Name</h2>

                        <div class="RentMyBuyRentToggle" RentMyBuyRentToggle>
                            <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                                <input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch />
                                <div class="ToggleSwitchRound"></div>
                            </label>
                        </div>

                        <h2 class="RentMyProductPrice" RentMyProductPrice product_price_text>$0.00</h2>


                        <div class="RentMyRecurring" RentMyRecurring>
                            <h6 RecurringTitle>Recurring Pricing</h6>
                            <ul RecurringList>
                                <li RecurringItem>
                                    Recurring
                                </li>
                            </ul>
                        </div>


                        <div class="RentMyVariant" RentMyVariant>
                            <h6 VariantTitle>Rent My Variant Sizes</h6>
                            <ul VariantList>
                                <li VariantItem>Small</li>
                            </ul>
                        </div>




                        <!-- <div  RentMyProductOptions>
                        <div class="RentMyProductOptions" ProductOptionsItem>
                            <div class="CustomFieldInner">
                                <h6 ProductOptionsTitle>RentMy Product Options</h6>
                                <select></select>
                                <h6 class="product-option-predefined-radio" others ></h6>
                            </div>
                        </div>
                    </div> -->


                        <div class="BundleItemsArea mb-3">
                            <div class="BundleItemRow">
                                <div class="BundleItemRowLeft">
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/61ed848f-aa49-465c-b1d9-29ce379a479d/large_photo.jpg"
                                                alt="bundleitems img" />
                                        </div>
                                        <div class="BundleItemQty">1x</div>
                                    </div>
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/26c5ae2f-9db4-43f7-9e95-c5c427640bd8/large_photo.jpg"
                                                alt="bundleitems img" />
                                        </div>
                                        <div class="BundleItemQty">1x</div>
                                    </div>
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/96a7b6b2-663d-44f5-8048-7a3036161a95/large_photo.jpg"
                                                alt="bundleitems img" />
                                        </div>
                                        <div class="BundleItemQty">1x</div>
                                    </div>
                                    <div class="BundleItemImgArea">
                                        <div class="BundleItemImg">
                                            <img src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/2a9c3330-b309-4887-817a-30205c946058/large_photo.jpg"
                                                alt="bundleitems img" />
                                        </div>
                                        <div class="BundleItemQty">1x</div>
                                    </div>
                                </div>
                                <div class="BundleItemRowRight" RentMyProductOptions>

                                    <div class="BundleItemText">Luxury Two-Stall Restroom Trailer</div>

                                    <div class="BundleItemText" ProductOptionsItem>
                                        <!-- <h6 ProductOptionsTitle>Option Title</h6> -->
                                        <div class="BundleItemSelect">
                                            <!-- select sample -->
                                            <select class="BundleSelect"> </select>
                                            <!-- Other Sample -->
                                            <h6 class="product-option-predefined-radio" others></h6>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>



                        <div class="RmRentalOption" RentMyRentalDateRange>
                            <h6 RentalDateRangeTitle>Rental Date Range</h6>
                            <ul RentalDateRangeList>
                                <li class="" RentalDateRangeItem activeClass="RmDaterangeActive">
                                    <div class="RmRentalOptionDaytime" PricePreText> 1 day </div>
                                    <div Price> $10.00 </div>
                                </li>
                            </ul>
                        </div>


                        <div class="RentMyRentalDateRange mb-0" RentMyRentalEndDate>
                            <ul>
                                <li class="mt-0" RentalEndDatePicker>Pick Date Range</li>
                            </ul>

                            <span RentalEndDateSelectedLabel>
                                Today 09:00 AM
                            </span>
                        </div>

                        <!-- <div class="RmRentalDateRange" RentMyRentalEndDate>
                        <h6 >Rental date range</h6>
                        <ul>
                            <li RentalEndDatePicker>
                                <div RentalEndDateSelectedLabel> 12/12/-2024 </div>
                                <div class="RmRentalDateRangeIcon"> Date<i></i> </div>
                            </li>
                        </ul>
                    </div> -->



                        <div class="RentMyExactSelectDuration" RentMyExactSelectDuration>
                            <h6 RentMyExactSelectDurationTitle>
                                Select Duration
                            </h6>
                            <ul class="mb-4" RentMyExactSelectDurationList>
                                <li RentMyExactSelectDurationItem>
                                    Exact Select Duration
                                </li>
                            </ul>
                        </div>

                        <div class="RentMyExactSelectTime" RentMyExactSelectTime>
                            <h6 RentMyExactSelectTimeTitle>
                                Select Exact Start time
                            </h6>
                            <ul class="mb-4" RentMyExactSelectTimeList>
                                <li RentMyExactSelectTimeItem>
                                    Extact Times
                                </li>
                            </ul>
                        </div>


                        <div class="RentMyDeliveryOptions" RentMyDeliveryOptions>
                            <h6 DeliveryOptionsTitle>Delivery Options</h6>
                            <ul DeliveryOptionsList>
                                <li DeliveryOptionsItem>Local Move</li>
                            </ul>
                        </div>


                        <div class="RentMySelectLocation" RentMySelectLocation>
                            <h6 SelectLocationTitle>Select Location</h6>
                            <ul SelectLocationList>
                                <li SelectLocationItem>Default location</li>
                            </ul>
                        </div>


                        <div class="QuantityContainer" RentmyQuantityContainer>
                            <label QuantityContainerTitle>Quantity</label>
                            <div class="QuantityBtn">
                                <button class="RentMyBtn" QuantityDecrementBtn>-</button>
                                <input type="text" autocomplete="off" name="qty" class="InputQuantity"
                                    NumberOfQuantity />
                                <button class="RentMyBtn" QuantityIncrementBtn>+</button>
                            </div>

                            <small class="info">
                                <span RentmyAvailableLabel>Available</span>:
                                <span RentmyAvailableQty>17</span>
                            </small>
                        </div>


                        <div class="RentMyCartBtnArea" RentMyCartBtnArea>
                            <button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn>ADD TO CART</button>
                        </div>

                    </div>
                </div>
            </div>

            <div class="RentMyProductDescription">
                <h3 class="RentMyProductDesTitle">Product Description</h3>
                <div class="RentMyProductDesBody" RentMyProductDescription>
                    <p>
                        This complete backpacking kit rental has everything two people need for an hiking trip in the
                        backcountry! Whether it is for a weekend trek or that bucket list thru-hike, this kit has more
                        award-winning gear than anywhere
                        else. You have everything you need other than the food and fuel.
                        <br />
                        <br />
                        Only Mountain Side includes this much gear in one package. Our kits are based on the 10
                        essentials,
                        required gear for any backcountry trip. Not sure what to pack? Checkout our backpackers
                        checklist to
                        ensure you don’t forget
                        anything important. Upgrades to ultralight tents are available.
                        <br />
                        <br />
                        Trail weight per person is 14-15.5lbs, depending on how you divvy up the gear.
                        <br />
                        <br />
                        Is your hiking trip a cold weather adventure? If so, consider adding a foam sleeping pad rental
                        to
                        your
                        backpacking kit. The closed cell construction make for a fantastic thermal barrier when placed
                        between
                        your air pad and
                        sleeping bag.
                        <br />
                        <br />
                        Mountain Side offers a 20 degree synthetic sleeping bag. If preferred to down, just tell us in
                        the
                        order
                        notes.
                    </p>
                </div>
            </div>
        </div>


        <!-- In page cart widget -->
        <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div>

    </main>
    <script>

    </script>
</body>

</html>