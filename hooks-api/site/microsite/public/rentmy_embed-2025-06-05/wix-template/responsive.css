@media (max-width:767px) {
    .RentMyProductDetilsImg {
        flex-wrap: wrap;
    }
    .RentMyProductDetailsImgShow {
        order: -1;
        margin-bottom: 20px;
    }
    .RentMyProductDetailsImgList {
        width: 100%;
        margin-right: 0;
    }
    .RentMyProductDetailsImgList ul {
        display: flex;
        flex-wrap: wrap;
    }
    .RentMyProductDetailsImgList ul li {
        margin-right: 5px;
    }
    .RentMyProductDetailsImgList ul li {
        border: 2px solid #ddd !important;
        border-radius: 5px;
    }
    .RentMyProductDetailsImgList ul li img {
        width: 50px;
        height: 50px;
    }
    .RentMyProductDetailsImgList ul li.ActiveImg {
        border: 2px solid #333 !important;
    }
    .RentMyProductDetilsInfo {
        margin-top: 0px;
    }
    .RentMyProductName {
        font-size: 22px;
    }
    .RentMyProductPrice {
        font-size: 35px;
    }
    .BundleSelect {
        width: 100%;
    }
    .RentMyRentalDateRange {
        margin-bottom: 12px !important;
    }
    .RentMyTable tr td, 
    .RentMyTable tr th {
        font-size: 14px;
        padding: .75rem;
        vertical-align: middle;
    }
    .RentMyTable.RentMyCartTable tr td, 
    .RentMyTable.RentMyCartTable tr th {
        font-size: 12px;
        padding-left: 5px;
        padding-right: 0;
    }
    .RentMyTable.RentMyCartTable tr td:nth-of-type(6), 
    .RentMyTable.RentMyCartTable tr th:nth-of-type(6) {
        padding-right: 5px;
    }
    .RentMyCartTable tr td .CartItemTitle {
        color: #444;
        font-size: 12px;
    }
    .CartRemoveProduct {
        width: 22px;
        height: 22px;
    }
    .CartRemoveProduct i {
        font-size: 8px;
    }
    .RentMyWrapper .RentMyCartTable .QuantityBtn .RentMyBtn {
        padding: 0 6px;
        height: 22px;
    }
    .RentMyWrapper .RentMyCartTable .QuantityBtn .InputQuantity {
        width: 30px;
        height: 22px;
    }
    .RentMyCartTable tr td img {
        width: 25px;
        height: 25px;
        display: none;
    }
    .RentMyCouponCode .RentMyBtn, 
    .CheckoutMakeContinueBtn .RentMyBtn {
        text-transform: unset;
    }
    .RentMyCartDateRange {
        font-size: 14px;
    }
    .container.border {
        padding-left: 15px;
        padding-right: 15px;
    }
    .RentMyCartTotal {
        font-size: 18px;
    }
    .CheckoutMakeContinueBtn {
        flex-wrap: unset;
    }
    .MakeContinue {
        width: unset;
        display: unset;
        justify-content: unset;
        margin-top: 0;
    }
    .RentMyWrapper .RentMyRow .RentMyHalfwidth {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .CheckoutLeftSide {
        padding-left: 7px;
        padding-right: 7px;
    }
    .PickupLocationList {
        padding-top: 10px;
        padding-bottom: 5px;
    }
    .OrderName {
        font-weight: 600;
        font-size: 15px;
        line-height: 15px;
    }
    .OrderSummaryTable tr th,
    .OrderSummaryTable tr td {
        padding: .35rem 0px;
    }
    .BillingCheckoutTitle {
        font-weight: 600;
        font-size: 19px;
        top: -12px;
    }
    .OrderReviewTitle {
        font-size: 19px;
    }
    .RentMyCartWrapper .ProductCustomFieldsInCart {
        display: none;
    }
}