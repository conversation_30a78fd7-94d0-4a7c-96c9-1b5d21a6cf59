.react-datepicker-wrapper {
    /* margin-top: 10px; */
    width: 100%;
 }
 
 .react-datepicker {
    font-family: -apple-system, BlinkMacSystemFont, 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    border: none;
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
 }
 
 .react-datepicker__input-container {
    border-color: #2D3748;
    width: 100%;
    height: 100%;
    outline: 0;
    border-radius: 2px;
 }
 
 .react-datepicker__input-container > input {
    color: #2D3748;
    font-size: 15px;
    width: 100%;
    height: 100%;
    outline: 0;
    border: 1px solid #E2E8F0;
    padding: 2px 10px;
    border-radius: 2px;
    -webkit-transition:border-color 300ms ease;    
    -moz-transition:border-color 300ms ease;      
    -o-transition:border-color 300ms ease;   
    transition: border-color 300ms ease; 
 }
 
 .react-datepicker__input-container > input:focus{
    border-color: #467fe2;
 }
 
 .react-datepicker__header  {
    background-color: #EDF2F9;
 }
 
 .react-datepicker__day--selected, 
 .react-datepicker__day--selected:hover, 
 .react-datepicker__day--selected:focus {
    background-color: rgb(97, 145, 233);
    border-radius: 4px;
    border:none;
 }

 .react-datepicker__day--today {
   background-color: rgb(200, 195, 241);
   border-radius: 4px;
   border:none;
}

 .react-datepicker__triangle {
    display: none !important;
 }
 
 .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle {
    margin-top: 0;
 }
 
 .react-datepicker__close-icon::after {
    background-color: unset;
    border-radius: unset;
    font-size: 1.5rem;
    font-weight: bold;
    color: hsl(0,0%,80%);
    height: 20px;
    width: 20px;
 }
 
 .react-datepicker__close-icon::after:hover {
    color: hsl(0,0%,70%)
 }
 
 
 