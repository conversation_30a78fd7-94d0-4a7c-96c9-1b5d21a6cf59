import { DOTS, usePagination } from './useNumberPagination.js';

import { Button, Flex, Icon, Text } from '@chakra-ui/react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';


const NumberPagination = ({
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
}) => {


    const paginationRange = usePagination({
        currentPage,
        totalCount,
        siblingCount,
        pageSize
    });

    // if (currentPage === 0 || paginationRange.length < 2) {
    //     return null;
    // }

    const onNext = () => {
        onPageChange(currentPage + 1);
    };

    const onPrevious = () => {
        onPageChange(currentPage - 1);
    };

    let lastPage = paginationRange[paginationRange?.length - 1];


    return (
        <Flex
            gap={2} alignItems={'center'}
            flexWrap={'wrap'}
        >
            <Button
                variant='unstyled'
                disabled={currentPage === 1}
                onClick={onPrevious}
            >

                <Icon as={FaChevronLeft} color='blue.400' />

            </Button>
            {paginationRange.map(pageNumber => {
                if (pageNumber === DOTS) {
                    return <Text>&#8230;</Text>;
                }

                return (
                    <Text
                        cursor={'pointer'}
                        textAlign={'center'}
                        bg={pageNumber === currentPage ? 'blue.400' : 'white'}
                        p='0 7px' // '0 5px'
                        // w='25px
                        // h='25px'
                        color={pageNumber === currentPage ? '#ffffff' : '#000000'}
                        borderRadius={'20%'}
                        onClick={() => onPageChange(pageNumber)}
                    >
                        {pageNumber}
                    </Text>
                );
            })}
            <Button
                variant='unstyled'
                disabled={currentPage === lastPage}

                onClick={onNext}
            >

                <Icon as={FaChevronRight} color='blue.400' />

            </Button>
        </Flex>
    );
};

export default NumberPagination;
