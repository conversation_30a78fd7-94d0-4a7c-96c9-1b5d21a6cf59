

import { Button, <PERSON>lex, <PERSON>D<PERSON><PERSON><PERSON><PERSON>tepper, NumberIncrementStepper, NumberInput, NumberInputField, NumberInputStepper, Text } from "@chakra-ui/react";
import { useState } from "react";

export const PaginationText = ({ totalPages,
    isFetching,
    page, limit, setPage, paginationStyle }) => {
    const [activeBtn, setActiveBtn] = useState("none");

    return (
        <Flex alignItems={"center"} gap={3} my={4} flexWrap={"wrap"}>
            <Text>
                Current Page: {page ? page : 1} of {totalPages}
            </Text>
            <Button
                variant={"outline"}
                size='xs'
                colorScheme={'blue'}

                onClick={() => {
                    setActiveBtn("firstPage");
                    setPage(1)
                }}

                isLoading={activeBtn === "firstPage" && isFetching}
                disabled={+page === 1 || isFetching}
            >
                First Page
            </Button>

            <Button
                size='xs'
                colorScheme={'blue'}

                variant={"outline"}
                onClick={() => {
                    setActiveBtn("prevPage");
                    setPage(page - 1)
                }}
                disabled={+page === 1 || isFetching}

                isLoading={activeBtn === "prevPage" && isFetching}>
                Previous Page
            </Button>



            <Button
                size='xs'
                colorScheme={'blue'}

                variant={


                    "outline"
                }
                disabled={isFetching || totalPages === +page}
                onClick={() => {
                    setActiveBtn("nextPage");
                    setPage(page + 1)

                }}

                isLoading={activeBtn === "nextPage" && isFetching}>
                Next Page
            </Button>

            <Button
                size='xs'
                colorScheme={'blue'}
                disabled={totalPages === +page || isFetching}
                variant={"outline"}
                onClick={() => {
                    setActiveBtn("lastPage");
                    setPage(totalPages)

                }}
                isLoading={activeBtn === "lastPage" && isFetching}>
                Last Page
            </Button>

            <Flex alignItems='center' gap={2}>

                <Text color="blue.500" fontSize="14px"> Go to page</Text>

                <NumberInput
                    colorScheme={'blue'}
                    value={page}
                    min={1} max={totalPages}
                    onChange={(value) => {
                        if(value < 1){
                            setPage(1)
                        }else{
                            setPage(value)
                        }

                    }}
                    w="80px" size="xs" mr="20px">
                    <NumberInputField />
                    <NumberInputStepper>
                        <NumberIncrementStepper />
                        <NumberDecrementStepper />
                    </NumberInputStepper>
                </NumberInput>
            </Flex>

            {/* <Select
                bg={totalPages === page || isFetching ? "#EDF2F7" : "none"}
                border={"1px solid"}
                borderColor={
                    totalPages === page || isFetching ? "grey.600" : "brand.500"
                }
                borderRadius={"6px"}
                size={"xs"}
                w={"120px"}
                // p={0.5}
                isDisabled={totalPages === page || isFetching}
                value={limit}
                onChange={(e) => {
                    // updateParamsDetails({ limit: Number(e.target.value) });
                }}
            >
                {[5, 10, 20, 30, 40, 50].map((pageSize) => (
                    <option key={pageSize} value={pageSize}>
                        Show {pageSize}
                    </option>
                ))}
            </Select> */}
        </Flex>
    )
}
