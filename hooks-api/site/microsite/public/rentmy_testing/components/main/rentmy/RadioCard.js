import {
  Box, useRadio
} from '@chakra-ui/react'

function RadioCard(props) {
    const { getInputProps, getCheckboxProps } = useRadio(props)
  
    const input = getInputProps()
    const checkbox = getCheckboxProps()
  
    return (
      <Box as='label' >
        <input {...input} />
        <Box
          {...checkbox}
          fontSize={props.fontSize ? props.fontSize : '14px'}
          cursor='pointer'
          borderWidth='2px'
          borderRadius='lg'
          boxShadow='sm'
          _checked={{
            bg: 'teal.50',
            color: 'gray.700',
            borderColor: 'teal.600',
            outline: 'none'
          }}
          _focus={{
            outline: 'none'
          }}
          px="30px"
          py={2}
        >
          {props.children}
        </Box>
      </Box>
    )
  }

  export default RadioCard;