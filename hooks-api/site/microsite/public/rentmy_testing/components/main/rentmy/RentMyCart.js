import {
  Box,
  Button,
  Container,
  Flex,
  Icon,
  Image,
  Input,
  Table,
  Tbody,
  Td,
  Text,
  Tfoot,
  Th,
  Thead,
  Tr,
  useToast,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { Scrollbars } from 'react-custom-scrollbars';
import { FaRegTrashAlt } from 'react-icons/fa';
import { useDispatch } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { decrementCartItem } from '../../app/rentmy/rentmySlice';
import imagePlaceholder from '../../builder/components/rentmy/product-image-placeholder.jpg';
import {
  applyCouponHandler,
  deleteCartItem,
  getCartItems,
  updateCartItem,
} from './service';
import rentmyLogo from 'https://b1547765.smushcdn.com/1547765/wp-content/uploads/2019/11/logo.png?lossy=1&strip=1&webp=1';
// import rentmyLogo from '/static/media/rentmy.png';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyCart() {
  const [cart, setCart] = useState(null);
  const [coupon, setCoupon] = useState('');
  const { id } = useParams();
  const toast = useToast();
  const history = useHistory();
  const dispatch = useDispatch();

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
          '/products/' +
          rentMyStoreId +
          '/' +
          product.id +
          '/' +
          product.images[0]?.image_small
        : imagePlaceholder;
      return src;
    },
    [cart]
  );

  const deleteCartItemHandle = useCallback(
    (itemId, product) => {
      const cartToken = localStorage.getItem('rentMyCartToken');
      deleteCartItem(cartToken, itemId, product.id)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            dispatch(decrementCartItem(1));
            toast({
              title: 'Delete',
              description: 'Item deleted successfully',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            const rentMyCart = JSON.stringify(result.data);
            localStorage.setItem('rentMyUserCart', rentMyCart);
            setCart(result.data);
          }
        })
        .catch((error) => console.log(error.message));
    },
    [cart]
  );

  const cartItemQuantityUpdate = useCallback(
    (prevQuantity, type, cartItem) => {
      if (type === 'dec' && prevQuantity === 1) {
        toast({
          title: 'Error',
          description: "Quantity can't be zero",
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else {
        const rentMyCartToken = localStorage.getItem('rentMyCartToken');
        if (rentMyCartToken) {
          const postData = {
            id: cartItem.id,
            increment: type === 'inc' ? 1 : 0,
            price: cartItem.price,
            sales_tax: cartItem.sales_tax,
            token: rentMyCartToken,
            view_token: '',
          };

          updateCartItem(postData)
            .then((response) => {
              const { result } = response.data;
              if (result.hasOwnProperty('data')) {
                toast({
                  title: 'Update',
                  description: 'Item quantity has been updated',
                  status: 'success',
                  duration: 5000,
                  isClosable: true,
                });
                const rentMyCart = JSON.stringify(result.data);
                localStorage.setItem('rentMyUserCart', rentMyCart);
                setCart(result.data);
              }
            })
            .catch((error) => console.log(error.message));
        }
      }
    },
    [cart]
  );

  const couponHandler = useCallback(
    (couponCode) => {
      if (!couponCode) return;

      applyCouponHandler(couponCode)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            toast({
              title: 'Success',
              description: 'Coupon accepted successfully',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            const rentMyCart = JSON.stringify(result.data);
            localStorage.setItem('rentMyUserCart', rentMyCart);
            setCart(result.data);
            setCoupon('');
          }
        })
        .catch((error) => console.log(error.message));
    },
    [cart]
  );

  useEffect(() => {
    getCartItems(id)
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          const rentMyCart = JSON.stringify(result.data);
          localStorage.setItem('rentMyUserCart', rentMyCart);
          setCart(result.data);
        }
      })
      .catch((error) => error.message);
  }, []);

  return (
    <Box bg="gray.100">
      <Container maxW={1240} py={4}>
        <Box p={5} bg="white" rounded={8}>
          <Box display="flex" bg="gray.100" mb={4}>
            <Image src={rentmyLogo} width="150px" mr={4} alt='rentmy logo' />
            <Text
              fontSize="1.4rem"
              textTransform="uppercase"
              fontWeight={600}
              my={2}
            >
              Cart Details
            </Text>
          </Box>

          {cart && cart?.cart_items.length ? (
            <Box>
              <Scrollbars
                autoHide
                autoHeight
                autoHeightMin={0}
                autoHeightMax={500}
              >
                <Table variant="simple" size="lg">
                  <Thead>
                    <Tr>
                      <Th></Th>
                      <Th>Product</Th>
                      <Th>Unit Price</Th>
                      <Th>Quantity</Th>
                      <Th isNumeric>Sub Total</Th>
                      <Th></Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {cart.cart_items.map((cartItem) => (
                      <Tr key={cartItem.id}>
                        <Td>
                          <Image
                            src={getImage(cartItem.product)}
                            priority
                            alt='product image'
                            width="60px"
                            height="auto"
                          />
                        </Td>
                        <Td>{cartItem.product.name}</Td>
                        <Td>{cartItem.price}</Td>
                        <Td>
                          {cartItem.product_type == 1 ? (
                            <Flex>
                              <Button
                                size="xs"
                                rounded={2}
                                onClick={() =>
                                  cartItemQuantityUpdate(
                                    cartItem.quantity,
                                    'dec',
                                    cartItem
                                  )
                                }
                              >
                                -
                              </Button>
                              <Text px="10px">{cartItem.quantity}</Text>
                              <Button
                                size="xs"
                                rounded={2}
                                onClick={() =>
                                  cartItemQuantityUpdate(
                                    cartItem.quantity,
                                    'inc',
                                    cartItem
                                  )
                                }
                              >
                                +
                              </Button>
                            </Flex>
                          ) : (
                            cartItem.quantity
                          )}
                        </Td>
                        <Td isNumeric>{cartItem.sub_total}</Td>
                        <Td>
                          <Icon
                            as={FaRegTrashAlt}
                            w={3}
                            h={3}
                            color="gray.400"
                            ml="10px"
                            cursor="pointer"
                            _hover={{ color: 'gray.500' }}
                            onClick={() =>
                              deleteCartItemHandle(
                                cartItem.id,
                                cartItem.product
                              )
                            }
                          />
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Scrollbars>

              <Box
                display="flex"
                flexWrap="wrap"
                mt={4}
                justifyContent="space-between"
              >
                <Box
                  flex="0 0"
                  flexBasis="auto"
                  width={['100%', '100%', '50%', '50%']}
                >
                  <Text fontSize="1.1rem" fontWeight={700} mb={2}>
                    Cart Totals
                  </Text>
                  <Table variant="simple" size="sm">
                    <Tbody>
                      {cart.coupon_id ? (
                        <Tr>
                          <Td>Coupon Applied</Td>
                          <Td textAlign="right" isNumeric>
                            {cart.coupon_amount}
                          </Td>
                        </Tr>
                      ) : null}
                      <Tr>
                        <Td>Subtotal</Td>
                        <Td textAlign="right" isNumeric>
                          {cart.sub_total}
                          <Text as="span">(inc. tax)</Text>
                        </Td>
                      </Tr>
                      <Tr>
                        <Td>Deposit Amount</Td>
                        <Td textAlign="right" isNumeric>
                          {cart.deposit_amount}
                        </Td>
                      </Tr>
                      <Tr>
                        <Td>Shipping Charge</Td>
                        <Td textAlign="right">Calculated in the next step</Td>
                      </Tr>
                      <Tr>
                        <Td>Tax</Td>
                        <Td textAlign="right" isNumeric>
                          $0
                        </Td>
                      </Tr>
                      <Tr>
                        <Td>Delivery Tax</Td>
                        <Td textAlign="right">Calculated in the next step</Td>
                      </Tr>
                    </Tbody>
                    <Tfoot>
                      <Tr>
                        <Th fontSize="1.2rem" py={4}>
                          Total
                        </Th>
                        <Th fontSize="1.2rem" py={4} isNumeric>
                          <Text>{cart.total}</Text>
                          <Text fontSize=".7rem" fontWeight={300}>
                            (includes)
                          </Text>
                        </Th>
                      </Tr>
                    </Tfoot>
                  </Table>
                </Box>
                <Box
                  flex="0 0"
                  flexBasis="auto"
                  width={['100%', '100%', '45%', '45%']}
                  mb={4}
                >
                  <Flex mb={4}>
                    <Input
                      size="sm"
                      placeholder="Enter coupon code"
                      value={coupon}
                      onChange={(e) => setCoupon(e.target.value)}
                    />
                    <Button
                      onClick={() => couponHandler(coupon)}
                      size="sm"
                      ml="10px"
                      px="30px"
                    >
                      Apply Coupon
                    </Button>
                  </Flex>
                  <Flex justifyContent="center" py="20px" flexWrap="wrap">
                    <Button
                      flexGrow="1"
                      px="30px"
                      mb="10px"
                      onClick={() => history.push('/')}
                    >
                      Continue Shopping
                    </Button>
                    <Button
                      flexGrow="1"
                      ml="10px"
                      px="30px"
                      onClick={() =>
                        history.push('/rentmy/checkout/single-page-checkout')
                      }
                    >
                      Proceed Checkout
                    </Button>
                  </Flex>
                </Box>
              </Box>
            </Box>
          ) : null}
        </Box>
      </Container>
    </Box>
  );
}

export default RentMyCart;
