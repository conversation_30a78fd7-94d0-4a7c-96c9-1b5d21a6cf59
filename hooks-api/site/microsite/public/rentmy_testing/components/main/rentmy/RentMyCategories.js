import {
    Box,
    Flex,
    Accordion,
    AccordionIcon,
    AccordionItem,
    AccordionButton,
    AccordionPanel,
    Button
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { globalSettingData } from 'store/slices/globalSettingSlice';
import { useSelector } from 'react-redux';
import { FaAngleRight, FaChevronRight, } from 'react-icons/fa';
import { FiChevronDown, FiChevronRight } from 'react-icons/fi';
import { getRentMyToken } from 'service/rentmyAuth';
import axios from 'axios';


const RentMyCategories = ({
    categories,
    setCategories,
    selectedCate,
    setSelectedCate,
    onResetCategories,
    data,
    apiCallStat,
    setApiCallStat,
    setPage,
}) => {
    // console.log('categories ==>', categories);

    const settings =
    {
        "settingsId": null,
        "showSignIn": true,
        "primaryColor": "#fb0303",
        "secondaryColor": "rgba(126,211,33,1)",
        "accentColor": "rgba(65,117,5,1)",
        "logo": {
            "_id": "651a4f6597fee0e6e785085b",
            "altText": "",
            "filePath": "i8s2uzjm/image/tea-49982469-of02u7w7-removebg-preview-24684430.webp"
        },
        "favIcon": {
            "_id": "651a4f5797fee0e6e785083e",
            "altText": "",
            "filePath": "i8s2uzjm/image/finger.webp"
        },
        "brand": "Jahid brand",
        "siteFont": "ABeeZee",
        "advanceSeoSettings": {
            "requestAudit": {
                "isRequested": false,
                "auditLogs": []
            },
            "progress": 0,
            "overallProgress": "0.00",
            "homepageProgress": "0.00",
            "incompleteMeta": "0.00",
            "isRobotTxtEnable": true,
            "focusKeywords": [],
            "isAllowIndexing": true,
            "robotsTxt": "Robot.Txt 12345"
        },
        "siteName": "",
        "seo": {
            "adsense": {
                "_id": "636e2a7fb1804d5a06f5e2ac",
                "active": true,
                "content": "ca-pub-9775757577653408"
            },
            "searchConsole": {
                "_id": "6371c3afb1804d5a06f781b7",
                "active": false,
                "content": "aA2M-HM29FrEbNU913HLd1QCC4TbV9bZFCcCUkGOYxg"
            },
            "pinterest": {
                "_id": "63b56ba2b1804d5a0614ec37",
                "active": true,
                "content": "2f758c3c494a6a15b347f5bfb36363d6"
            },
            "facebookPixel": {
                "_id": "651fdbbcc6dc7baeae7906d5",
                "active": true,
                "content": "1486704542175999"
            },
            "activeBlogPage": {
                "active": false,
                "_id": "6528d3f3b7f1a6cdde649fa2",
                "content": {
                    "name": "blog101",
                    "generation": "1st",
                    "section": {
                        "ids": [
                            "b29aa84f-a797-4148-8dc6-88bd8f18abdf"
                        ],
                        "entries": {
                            "b29aa84f-a797-4148-8dc6-88bd8f18abdf": {
                                "id": "b29aa84f-a797-4148-8dc6-88bd8f18abdf",
                                "type": "uncategorized",
                                "style": {
                                    "padding": {
                                        "top": {
                                            "base": 50,
                                            "md": 50,
                                            "lg": 100
                                        },
                                        "right": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "bottom": {
                                            "base": 50,
                                            "md": 50,
                                            "lg": 50
                                        },
                                        "left": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        }
                                    },
                                    "margin": {
                                        "top": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "right": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "bottom": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "left": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        }
                                    },
                                    "isGradient": false,
                                    "isGradientAnimated": false,
                                    "bgColor": "transparent",
                                    "gradientDirection": "to right",
                                    "gradientColorOne": "#37d5d6",
                                    "gradientColorTwo": "#36096d",
                                    "gradientColorThree": "",
                                    "bgBlendMode": "normal",
                                    "isTintActive": false,
                                    "bgImage": {
                                        "path": "",
                                        "altText": "",
                                        "id": ""
                                    },
                                    "border": {
                                        "top": 0,
                                        "right": 0,
                                        "bottom": 0,
                                        "left": 0,
                                        "color": "#3F3047",
                                        "style": "solid"
                                    },
                                    "rounded": {
                                        "topLeft": 0,
                                        "topRight": 4,
                                        "bottomLeft": 4,
                                        "bottomRight": 4
                                    }
                                },
                                "fullWidth": false,
                                "columnGap": 10,
                                "children": [
                                    "583e9ea9-b20f-43a3-9ba8-4af7a0b66057"
                                ]
                            }
                        }
                    },
                    "column": {
                        "583e9ea9-b20f-43a3-9ba8-4af7a0b66057": {
                            "id": "583e9ea9-b20f-43a3-9ba8-4af7a0b66057",
                            "style": {
                                "width": {
                                    "base": 100
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "rounded": {
                                    "topLeft": 0,
                                    "topRight": 0,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "boxShadow": "none",
                                "bgColor": "#ffffff",
                                "hover": {
                                    "backgroundColor": "transparent",
                                    "color": "#212121",
                                    "transformValue": "none",
                                    "transform": {
                                        "scaleX": 1,
                                        "scaleY": 1,
                                        "skewX": 0,
                                        "skewY": 0,
                                        "rotateX": 0,
                                        "rotateY": 0,
                                        "rotateZ": 0,
                                        "translateX": 0,
                                        "translateY": 0
                                    }
                                },
                                "animation": {
                                    "animation": false,
                                    "value": "fadeIn",
                                    "onScroll": false,
                                    "isRepeat": false,
                                    "transition": {
                                        "type": "spring",
                                        "duration": 0.5,
                                        "delay": 0,
                                        "bounce": 0.4,
                                        "repeat": 1,
                                        "repeatType": "mirror",
                                        "repeatDelay": 0
                                    }
                                },
                                "bgImage": {
                                    "path": "",
                                    "altText": "",
                                    "id": ""
                                },
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "#3F3047",
                                    "style": "solid"
                                }
                            },
                            "children": [
                                "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67"
                            ]
                        }
                    },
                    "component": {
                        "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67": {
                            "id": "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67",
                            "type": "blog-grid",
                            "cardBgColor": "red",
                            "style": {
                                "margin": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0
                                },
                                "numberOfColumn": {
                                    "base": 1,
                                    "md": 2,
                                    "lg": 4
                                },
                                "columnGap": 10,
                                "rowGap": 20,
                                "animation": {
                                    "animation": false,
                                    "value": "fadeIn",
                                    "onScroll": false,
                                    "isRepeat": false,
                                    "transition": {
                                        "type": "spring",
                                        "duration": 0.5,
                                        "delay": 0,
                                        "bounce": 0.4,
                                        "repeat": 1,
                                        "repeatType": "mirror",
                                        "repeatDelay": 0
                                    }
                                },
                                "layout": "list"
                            },
                            "cardStyle": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 10
                                    },
                                    "right": {
                                        "base": 10
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 10
                                    }
                                },
                                "border": {
                                    "top": 1,
                                    "right": 1,
                                    "bottom": 1,
                                    "left": 1,
                                    "color": "gray.300",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 8,
                                    "topRight": 8,
                                    "bottomLeft": 8,
                                    "bottomRight": 8
                                },
                                "bgColor": "#FFFFFF",
                                "boxShadow": "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
                                "position": "relative",
                                "textAlign": "left",
                                "showTags": true,
                                "showTitle": true,
                                "showExcerpt": true,
                                "showComments": true,
                                "showAuthor": true
                            },
                            "searchStyle": {
                                "showSearchBar": true,
                                "justifyContent": {
                                    "base": "flex-start"
                                },
                                "height": "auto",
                                "width": "60%",
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "#666666",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 4,
                                    "topRight": 4,
                                    "bottomLeft": 4,
                                    "bottomRight": 4
                                },
                                "bgColor": "#CBD5E0",
                                "text": {
                                    "size": {
                                        "base": "16"
                                    },
                                    "color": "#212121",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "searchText": "Search",
                                "searchContainerWidth": "40%"
                            },
                            "paginationStyle": {
                                "showPagination": true,
                                "paginateView": "button",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "selectedPageColor": "blue.400",
                                "buttonVariant": "outline",
                                "color": "blue.400"
                            },
                            "filterStyle": {
                                "showFilter": true,
                                "justifyContent": {
                                    "base": "flex-end"
                                },
                                "text": {
                                    "size": {
                                        "base": "16"
                                    },
                                    "color": "#212121",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "filterText": "Filter by tags",
                                "filterView": "dropdown",
                                "width": "40%",
                                "filterContainerWidth": "60%"
                            },
                            "imageStyle": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "width": "100%",
                                "height": "200",
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "gray.300",
                                    "style": "none"
                                },
                                "rounded": {
                                    "topLeft": 8,
                                    "topRight": 8,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "justifyContent": "flex-start"
                            },
                            "title": {
                                "markup": "h3",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 5
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "text": {
                                    "size": {
                                        "base": "15"
                                    },
                                    "color": "gray.900",
                                    "weight": "700",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "limit": 10
                            },
                            "excerpt": {
                                "markup": "p",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "text": {
                                    "size": {
                                        "base": "14"
                                    },
                                    "color": "gray.700",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "limit": 20
                            },
                            "button": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 4
                                    },
                                    "right": {
                                        "base": 10
                                    },
                                    "bottom": {
                                        "base": 4
                                    },
                                    "left": {
                                        "base": 10
                                    }
                                },
                                "height": "auto",
                                "width": "auto",
                                "border": {
                                    "top": 1,
                                    "right": 1,
                                    "bottom": 1,
                                    "left": 1,
                                    "color": "gray.700",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 0,
                                    "topRight": 0,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "bgColor": "transparent",
                                "text": {
                                    "size": {
                                        "base": "12"
                                    },
                                    "color": "gray.900",
                                    "weight": "300",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "justifyContent": "flex-start",
                                "visibility": true,
                                "data": "Read More"
                            },
                            "data": {
                                "dataSource": null,
                                "dataSourceType": "all",
                                "limit": 10,
                                "filter": {
                                    "type": "author",
                                    "value": null
                                },
                                "sort": [
                                    {
                                        "label": "Date",
                                        "value": "createdAt",
                                        "type": "desc"
                                    }
                                ]
                            },
                            "categoryStyle": {
                                "showCategory": true
                            }
                        }
                    },
                    "selected": null,
                    "selectedType": ""
                },
                "genericSettings": {
                    "responsive": {
                        "isMobile": true,
                        "isTab": false,
                        "isWeb": false
                    },
                    "pageInfo": {
                        "meta": {
                            "title": "",
                            "isIncludeInSitemap": false,
                            "isAllowPageIndex": false,
                            "isAllowImageIndex": false,
                            "isAllowNoFollow": false,
                            "maxSnippet": 0,
                            "canonicalUrl": "https://b107.pc-staging.com/blog101",
                            "pageSeoScore": 14.761904761904761,
                            "keywordDensity": 0,
                            "pageContentMatchKeywordCount": 0,
                            "altTextMatchKeywordCount": 0,
                            "internalLinkCount": 0,
                            "externalLinkCount": 0,
                            "description": null,
                            "keywords": null,
                            "image": null,
                            "focusKeywords": [],
                            "ogUrl": "https://b107.pc-staging.com/blog101",
                            "metaInfo": {
                                "totalMeta": 6,
                                "missing": 5,
                                "exist": 1
                            }
                        },
                        "renderType": "builder",
                        "isHomePage": false,
                        "isBlogPage": true,
                        "pageName": "blog101",
                        "pageTitle": "blog101",
                        "slug": "blog101"
                    },
                    "headerInfo": {
                        "headerPosition": "sticky",
                        "headerBackground": "#ffffff"
                    },
                    "_id": "6528d3f3b7f1a6cdde649fa0",
                    "__v": 0,
                    "adminType": "CA",
                    "builderType": "FE_BLOG",
                    "createdAt": "2023-10-13T05:21:55.401Z",
                    "franchiseeId": "636a25eb5d1fa80f73143ea4",
                    "updatedAt": "2024-09-18T04:43:16.583Z",
                    "id": "6528d3f3b7f1a6cdde649fa0"
                }
            }
        }
    }

    const [searchText, setSearchText] = useState(true);
    const [expandedIndex, setExpandedIndex] = useState(null);
    const [shouldExpandSection1, setShouldExpandSection1] = useState(false);


    const fetchSubCategories = async (id) => {
        // setLoading(true);
        const apiBaseURL = `https://clientapi.rentmy.co/api`;
        const apiURL = `/get/child-categories/${id}`;
        const tokenObject = await getRentMyToken(id);
        if (tokenObject.success) {
            axios
                .request({
                    url: apiURL,
                    method: 'GET',
                    baseURL: apiBaseURL,
                    headers: {
                        Authorization: `Bearer ${tokenObject.token}`,
                        location: data?.locationDataSource?.value || tokenObject.locationId,
                    },
                })
                .then((response) => {
                    const { result } = response.data;
                    // setProducts(result.data);

                    if (result.data) {
                        setCategories(result?.data?.sibling)
                    }
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    // setLoading(false)
                    // setPageLoad(false)
                });
        } else {
            // setLoading(false);
            // setPageLoad(false)
        }
    };



    const getSubCategories = (data) => {
        // console.log("data ==>", data);
        return data && data?.length > 0 && data.map((item, index) => {
            return <>
                <Flex
                    key={item?.uuid + index}
                    justifyContent={'space-between'}
                    alignItems={'center'}
                    borderColor={'gray.100'}
                    borderTopWidth={1}
                    borderBottomWidth={1}
                    my={2}
                    p={1}
                    m={'auto'}

                    as={'button'}
                    w={'90%'}
                    bg={item?.uuid == selectedCate && settings.accentColor}
                    borderRadius={item?.uuid == selectedCate && '6px'}
                    color={item?.uuid == selectedCate ? 'white' : 'black'}

                    _hover={{
                        background: item?.uuid == selectedCate ? settings.accentColor : 'gray.100',
                        borderRadius: '6px',
                    }}
                    onClick={() => {
                        setSelectedCate(item?.uuid)
                        fetchSubCategories(item?.uuid)

                        setApiCallStat('selectedSubCate' + item?.uuid)
                    }}
                >
                    <Box pl={1}>{item?.name}</Box>
                    {!((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && <FiChevronRight />}
                    {((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && <FiChevronDown />}
                </Flex>

                <Box px={5} my={1}>
                    {((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && getSubCategories(item?.children?.length > 0 ? item?.children : item?.child)}
                </Box>
            </>
        })
    }


    return (
        <Accordion
            allowToggle
            allowMultiple
            borderColor={'gray.200'}
            defaultIndex={0}
            mb={5}
        >
            <AccordionItem
                borderRadius={6}
                boxShadow='lg'
                rounded='md'
            >
                <h2>
                    <AccordionButton background='gray.200' borderRadius={6}>
                        <Box as="span" flex='1' textAlign='left' fontSize="1rem" fontWeight="semibold" py={1} textTransform={'uppercase'}>
                            Categories
                        </Box>
                        <AccordionIcon />
                    </AccordionButton>
                </h2>

                <AccordionPanel pb={4} px={2}>

                    <Box
                        px={1}
                        h={'300px'}
                        overflowY={'auto'}
                        mb={2}
                    >
                        {categories && categories?.length > 0 && categories.map((item, index) => {
                            return (
                                <>
                                    <Flex
                                        key={item?.uuid + index}
                                        justifyContent={'space-between'}
                                        alignItems={'center'}
                                        borderColor={'gray.200'}
                                        borderTopWidth={1}
                                        borderBottomWidth={1}
                                        my={2}
                                        p={1}

                                        as={'button'}
                                        w={'100%'}
                                        bg={item?.uuid == selectedCate && settings.accentColor}
                                        borderRadius={item?.uuid == selectedCate && '6px'}
                                        color={item?.uuid == selectedCate ? 'white' : 'black'}

                                        _hover={{
                                            background: item?.uuid == selectedCate ? settings.accentColor : 'gray.100',
                                            borderRadius: '6px',
                                        }}
                                        onClick={() => {
                                            if (item?.uuid != selectedCate) {
                                                fetchSubCategories(item?.uuid)
                                                setSelectedCate(item?.uuid)
                                                setApiCallStat('selectedCate' + item?.uuid)
                                                setPage(1)
                                            }

                                        }}
                                    >
                                        <Box pl={1}>{item?.name}</Box>
                                        {!((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && <FiChevronRight />}
                                        {((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && <FiChevronDown />}
                                    </Flex>

                                    {((item?.children && item?.children?.length > 0) || (item?.child && item?.child?.length > 0)) && getSubCategories(item?.children?.length > 0 ? item?.children : item?.child)}
                                </>
                            )
                        })
                        }

                    </Box>


                    {/* <Button
                        mt={3}
                        fontSize={'15px'}
                        fontWeight={700}
                        color={'white'}
                        size="md"
                        textDecoration={'none'}
                        isDisabled={selectedCate ? false : true}
                        bg={settings?.accentColor}
                        _hover={{
                            bg: settings.secondaryColor,
                        }}
                        onClick={() => {
                            // fetchCategories()
                            // onResetCategories()

                            setApiCallStat('reset_category')
                        }}
                    >
                        Reset
                    </Button> */}
                </AccordionPanel>
            </AccordionItem>
        </Accordion >
    );
};

export default RentMyCategories;