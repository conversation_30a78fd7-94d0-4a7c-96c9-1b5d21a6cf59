import {
  Box,
  Button,
  Checkbox,
  Container,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Image,
  Input,
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  NumberDecrementStepper,
  NumberIncrementStepper,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Table,
  Tbody,
  Td,
  Text,
  Tr,
  useRadioGroup,
  useToast,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
// import offlinePayImage from './offline-pay-white.png';
import { useRouter } from 'next/router';
import countryList from './country.json';
import RadioCard from './RadioCard';
import {
  getCartByDelivery,
  getCartItems,
  getDeliveryCost,
  getDeliverySettings,
  getGetwaySettings,
  getLocationList,
  getPayment,
  getShippingMethods,
  placeOrder<PERSON><PERSON>ler,
} from './service';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js'

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyCheckout({ subscriberId }) {
  const { register, handleSubmit, getValues, errors, control } = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    shouldFocusError: true,
    shouldUnregister: true,
  });

  // Stripe objects------------------
  const stripe = useStripe();
  const elements = useElements();
  //---------------------------------
  const [locationList, setLocationList] = useState([]); // pickup location list
  const [cart, setCart] = useState(null);
  const [paymentGetwayOption, setPaymentGetwayOption] = useState([]);
  const [paymentType, setPaymentType] = useState({}); // selected payment type
  const [payment, setPayment] = useState(''); // booking = 50%, pickup = 100%, type=percent [object]

  const [paymentRadio, setPaymentRadio] = useState('fullPayment');
  const [paymentAmount, setPaymentAmount] = useState(null);

  const [deliverySetting, setDeliverySetting] = useState(null); // delivery setting for checkout
  const [deliveryChoice, setDeliveryChoice] = useState(''); // pickup or delivery or shipping
  const [deliveryOption, setDeliveryOption] = useState([]); // [pickup, delivery, shipping]
  const [deliveryCharge, setDeliveryCharge] = useState(''); // selected delivery charge to use

  const [shippingMethod, setShippingMethod] = useState(null); // shipping method for update cart

  const [sameAsAbove, setSameAsAbove] = useState({
    delivery: true,
    shipping: true,
  });
  const [deliveryLoading, setDeliveryLoading] = useState(false);
  const [shippingLoading, setShippingLoading] = useState(false);
  const [orderPlaceLoading, setOrderPlaceLoading] = useState(false);
  const toast = useToast();
  const router = useRouter();

  const { getRootProps, getRadioProps } = useRadioGroup({
    name: 'deliveryType',
    value: deliveryChoice,
    onChange: (value) => {
      setDeliveryChoice(value);
    },
  });

  const {
    getRootProps: paymentChoiceRootProps,
    getRadioProps: paymentChoiceRadioProps,
  } = useRadioGroup({
    name: 'paymentChoiceId',
    value: paymentType?.id,
    onChange: (value) => {
      let intValue = parseInt(value);
      if (paymentGetwayOption.length) {
        const payment = paymentGetwayOption.find(
          (item) => item.id === intValue
        );
        if (payment) setPaymentType(payment);
      }
    },
  });

  const group = getRootProps();
  const paymentChoiceGroup = paymentChoiceRootProps();
  // const watchFields = watch(["country", "city", "state", "zipcode"]);

  const pickUpLocationChangeHandler = (value) => {
    const locationId = parseInt(value);
    const pickupLocation = locationList.find(
      (location) => location.id === locationId
    );
    setDeliveryCharge(pickupLocation);
  };

  const backToCartPage = () => {
    const rentMyCartToken = localStorage.getItem('rentMyCartToken');
    if (rentMyCartToken) {
      router.push(`/rentmy/cart/${rentMyCartToken}`);
    }
  };

  // Get cart based on pick up location
  const updateCartByPickUpLocation = () => {
    if (cart) {
      const rentMyCartToken = localStorage.getItem('rentMyCartToken');
      if (rentMyCartToken) {
        const postData = {
          shipping_cost: 0,
          shipping_method: 1,
          tax: 0,
          token: rentMyCartToken,
        };
        getCartByDelivery(postData, subscriberId)
          .then((response) => {
            if (response.data.result.hasOwnProperty('data')) {
              setCart(response.data.result.data);
              const rentMyUserCartString = JSON.stringify(
                response.data.result.data
              );
              localStorage.setItem('rentMyUserCart', rentMyUserCartString);
            }
          })
          .catch((error) => console.log(error.message));
      }
    }
  };

  // get delivery cost
  const getDeliveryCostOption = () => {
    let emptyCheck = true;
    const address = getValues();
    if (sameAsAbove.delivery) {
      const { city, state, address_line1, country } = address;
      const addressObject = { city, state, address_line1, country };
      emptyCheck = Object.keys(addressObject).some(
        (key) => addressObject[key] === ''
      );
    } else {
      const {
        shipping_city,
        shipping_state,
        shipping_address_line1,
        shipping_country,
      } = address;
      const addressObject = {
        shipping_city,
        shipping_state,
        shipping_address_line1,
        shipping_country,
      };
      emptyCheck = Object.keys(addressObject).some(
        (key) => addressObject[key] === ''
      );
    }

    if (!emptyCheck) {
      setDeliveryLoading(true);
      getDeliveryCost(address, sameAsAbove.delivery, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('error')) {
            toast({
              title: 'Error',
              description: result.error,
              status: 'error',
              duration: 4000,
              isClosable: true,
            });
          } else {
            toast({
              title: 'Success',
              description: 'Successfully got delivery cost',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
          }

          if (result.hasOwnProperty('location')) {
            if (result.location[0].max_distance) {
              toast({
                title: 'Error',
                description:
                  'Your address is outside of our delivery area. Please contact us to make other arrangements.',
                status: 'warning',
                duration: 5000,
                isClosable: true,
              });
            } else {
              setDeliveryCharge(result.location[0]);
            }
          }
          setDeliveryLoading(false);
        })
        .catch((error) => {
          setDeliveryLoading(false);
          console.log(error.message);
          toast({
            title: 'Error',
            description: 'Something went wrong',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        });
    } else {
      // console.log("empty")
      toast({
        title: 'Error',
        description: 'Please fill up the required fields',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // get shipphing method options
  const getShippingMethodOptions = () => {
    let emptyCheck = true;
    const address = getValues();
    if (sameAsAbove.shipping) {
      const { city, state, address_line1, country } = address;
      const addressObject = { city, state, address_line1, country };
      emptyCheck = Object.keys(addressObject).some(
        (key) => addressObject[key] === ''
      );
    } else {
      const {
        shipping_city,
        shipping_state,
        shipping_address_line1,
        shipping_country,
      } = address;
      const addressObject = {
        shipping_city,
        shipping_state,
        shipping_address_line1,
        shipping_country,
      };
      emptyCheck = Object.keys(addressObject).some(
        (key) => addressObject[key] === ''
      );
    }

    if (!emptyCheck) {
      setShippingLoading(true);
      getShippingMethods(address, sameAsAbove.shipping, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('error')) {
            toast({
              title: 'Error',
              description: result.error,
              status: 'error',
              duration: 4000,
              isClosable: true,
            });
          } else {
            delete result.weight;
            const flatShippingOption = {
              standard: null,
              carrier: [],
            };
            Object.keys(result).forEach((key) => {
              if (key === 'standard') {
                flatShippingOption.standard = result[key];
              } else {
                flatShippingOption.carrier = [
                  ...flatShippingOption.carrier,
                  ...result[key],
                ];
              }
            });
            setShippingMethod(flatShippingOption);
          }
          setShippingLoading(false);
        })
        .catch((error) => {
          setShippingLoading(false);
          console.log(error.message);
        });
    }
  };

  // Shipping Method Change
  const shippingMethodChange = (value) => {
    if (value === 'standard') {
      setDeliveryCharge(shippingMethod.standard);
    } else {
      const carrier = shippingMethod.carrier.find(
        (item) => item.rate_id === value
      );
      setDeliveryCharge(carrier);
    }
  };

  // Accurate round price number
  const get2DecPoint = useCallback((number = '0.00') => {
    const floatNumber = parseFloat(number);
    return floatNumber.toLocaleString('en-US', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }, []);

  // Load necessary data
  useEffect(() => {
    // get delivery settings
    getDeliverySettings(subscriberId)
      .then((response) => {
        if (response.data.result.hasOwnProperty('delivery_settings')) {
          setDeliverySetting(response.data.result.delivery_settings);
        }
      })
      .catch((error) => console.log(error.message));

    getGetwaySettings(subscriberId)
      .then((response) => {
        if (response.data.result.hasOwnProperty('data')) {
          setPaymentGetwayOption(response.data.result.data);
        }
      })
      .catch((error) => console.log(error.message));

    // get pickup location list
    getLocationList(subscriberId)
      .then((response) => {
        if (response.data.result.hasOwnProperty('data')) {
          setLocationList(response.data.result.data);
        }
      })
      .catch((error) => console.log(error.message));

    const rentMyCartToken = localStorage.getItem('rentMyCartToken');
    const rentMyUserCart = localStorage.getItem('rentMyUserCart');

    if (rentMyCartToken) {
      if (rentMyUserCart) {
        setCart(JSON.parse(rentMyUserCart));
      } else {
        getCartItems(rentMyCartToken, subscriberId)
          .then((response) => {
            if (response.data.result.hasOwnProperty('data')) {
              setCart(response.data.result.data);
              const rentMyUserCartString = JSON.stringify(
                response.data.result.data
              );
              localStorage.setItem('rentMyUserCart', rentMyUserCartString);
            }
          })
          .catch((error) => console.log(error.message));
      }
    }
  }, []);

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
        '/products/' +
        rentMyStoreId +
        '/' +
        product.id +
        '/' +
        product.images[0]?.image_small
        : '/static/media/product-image-placeholder.jpg';
      return src;
    },
    [cart]
  );

  // Set delivery options
  useEffect(() => {
    const options = [];
    let deliveryChoiceName = '';
    if (deliverySetting?.instore_pickup) {
      options.push('pickup');
      deliveryChoiceName = 'pickup';
    }
    if (deliverySetting?.delivery) {
      options.push('delivery');
      deliveryChoiceName = deliveryChoiceName ? deliveryChoiceName : 'delivery';
    }
    if (deliverySetting?.shipping) {
      options.push('shipping');
      deliveryChoiceName = deliveryChoiceName ? deliveryChoiceName : 'shipping';
    }
    setDeliveryOption(options);
    if (deliveryChoiceName) setDeliveryChoice(deliveryChoiceName);
  }, [deliverySetting]);

  // Set delivery charge based on delivery choice
  useEffect(() => {
    if (deliveryChoice) {
      if (deliveryChoice === 'pickup' && locationList.length) {
        setDeliveryCharge(locationList[0]);
      } else {
        setDeliveryCharge('');
        setShippingMethod(null);
      }
    }
  }, [deliveryChoice]);

  // Update delivery charge when delivery choice type is "pickup"
  useEffect(() => {
    if (deliveryChoice === 'pickup') {
      if (locationList.length) {
        setDeliveryCharge(locationList[0]);
      }
    }
  }, [locationList]);

  // Cart update based on delivery charge
  useEffect(() => {
    if (deliveryChoice) {
      if (deliveryChoice === 'pickup') {
        updateCartByPickUpLocation();
      }
      if (deliveryChoice === 'delivery') {
        let postData = {
          address: {},
        };
        const cartToken = localStorage.getItem('rentMyCartToken');
        const addressValues = getValues();
        const {
          city: billing_city,
          state: billing_state,
          zipcode: billing_zipcode,
          country: billing_country,
        } = addressValues;
        if (sameAsAbove.delivery) {
          postData = {
            address: {
              billing_city,
              billing_state,
              billing_zipcode,
              billing_country,
              shipping_city: billing_city,
              shipping_state: billing_state,
              shipping_zipcode: billing_zipcode,
              shipping_country: billing_country,
            },
          };
        } else {
          const {
            shipping_city,
            shipping_state,
            shipping_zipcode,
            shipping_country,
          } = addressValues;
          postData = {
            address: {
              billing_city,
              billing_state,
              billing_zipcode,
              billing_country,
              shipping_city,
              shipping_state,
              shipping_zipcode,
              shipping_country,
            },
          };
        }

        postData = {
          ...postData,
          shipping_method: 3,
          shipping_cost: deliveryCharge.charge,
          token: cartToken,
        };

        getCartByDelivery(postData, subscriberId)
          .then((response) => {
            if (response.data.result.hasOwnProperty('data')) {
              setCart(response.data.result.data);
              const rentMyUserCartString = JSON.stringify(
                response.data.result.data
              );
              localStorage.setItem('rentMyUserCart', rentMyUserCartString);
            }
          })
          .catch((error) => console.log(error.message));
      }
      if (deliveryChoice === 'shipping') {
        let postData = {
          address: {},
        };
        const cartToken = localStorage.getItem('rentMyCartToken');
        const addressValues = getValues();
        const {
          city: billing_city,
          state: billing_state,
          zipcode: billing_zipcode,
          country: billing_country,
        } = addressValues;
        if (sameAsAbove.shipping) {
          postData = {
            address: {
              billing_city,
              billing_state,
              billing_zipcode,
              billing_country,
              shipping_city: billing_city,
              shipping_state: billing_state,
              shipping_zipcode: billing_zipcode,
              shipping_country: billing_country,
            },
          };
        } else {
          const {
            shipping_city,
            shipping_state,
            shipping_zipcode,
            shipping_country,
          } = addressValues;
          postData = {
            address: {
              billing_city,
              billing_state,
              billing_zipcode,
              billing_country,
              shipping_city,
              shipping_state,
              shipping_zipcode,
              shipping_country,
            },
          };
        }

        const shipping_method =
          deliveryCharge.carrier_code === 'Standard shipping' ? 6 : 4;
        const shipping_cost = deliveryCharge.charge;

        postData = {
          ...postData,
          shipping_method,
          shipping_cost,
          token: cartToken,
        };

        getCartByDelivery(postData, subscriberId)
          .then((response) => {
            if (response.data.result.hasOwnProperty('data')) {
              setCart(response.data.result.data);
              const rentMyUserCartString = JSON.stringify(
                response.data.result.data
              );
              localStorage.setItem('rentMyUserCart', rentMyUserCartString);
            }
          })
          .catch((error) => console.log(error.message));
      }
    }
  }, [deliveryCharge]);

  // Set payment type based on payment choice
  useEffect(() => {
    if (paymentType) {
      // console.log('paymentType:::', paymentType)
    }
  }, [paymentType]);

  // Get payments booking, pickup etc
  useEffect(() => {
    getPayment(subscriberId)
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          setPayment(result.data);
        }
      })
      .catch((error) => console.log(error.message));
  }, []);

  // Calculate amount to pay min and max for input
  const calculatePayment = (booking) => {
    const paymentValue = parseFloat(cart.total);
    const maxValue = paymentValue;
    const minValue = (paymentValue / 100) * booking;
    return {
      paymentValue,
      maxValue,
      minValue,
    };
  };

  useEffect(() => {
    if (payment !== '' && cart) {
      const booking = payment.booking ? parseFloat(payment.booking) : 0;
      if (payment.type === 'percent' && booking < 100 && booking > 0) {
        const { paymentValue, maxValue, minValue } = calculatePayment(booking);
        setPaymentAmount({
          value: paymentValue,
          max: maxValue,
          min: minValue,
        });
        setPaymentRadio('fullPayment');
      }
    }
  }, [payment, cart]);

  useEffect(() => {
    if (payment !== '' && cart) {
      if (paymentRadio === 'downPayment') {
        const booking = payment.booking ? parseFloat(payment.booking) : 0;
        if (payment.type === 'percent' && booking < 100 && booking > 0) {
          const { maxValue, minValue } = calculatePayment(booking);
          setPaymentAmount({
            value: minValue,
            max: maxValue,
            min: minValue,
          });
        }
      }

      if (paymentRadio === 'fullPayment') {
        const booking = payment.booking ? parseFloat(payment.booking) : 0;
        if (payment.type === 'percent' && booking < 100 && booking > 0) {
          const { paymentValue, maxValue, minValue } =
            calculatePayment(booking);
          setPaymentAmount({
            value: paymentValue,
            max: maxValue,
            min: minValue,
          });
        }
      }
    }
  }, [paymentRadio]);

  // Process order submit
  const onSubmit = async (data) => {
    if (!(deliveryCharge !== '' && paymentType.hasOwnProperty('name'))) return;

    const rentMyCartToken = localStorage.getItem('rentMyCartToken');
    let shippingMethod = '';
    if (deliveryChoice === 'pickup') shippingMethod = 1;
    if (deliveryChoice === 'shipping') {
      shippingMethod =
        deliveryCharge.carrier_code === 'Standard shipping' ? 6 : 4;
    }

    if (paymentType.type === 'offline') {
      setOrderPlaceLoading(true);
      placeOrderHandler(
        data,
        deliveryCharge,
        paymentType,
        shippingMethod,
        rentMyCartToken,
        sameAsAbove,
        subscriberId
      )
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            const { order } = result.data;
            const orderUID = order.data.uid;
            localStorage.removeItem('rentMyCartToken');
            localStorage.removeItem('rentMyUserCart');
            // setOrderPlaceLoading(false);
            router.push(`/rentmy/checkout/complete/${orderUID}`);
          } else {
            setOrderPlaceLoading(false);
            toast({
              title: 'Error',
              description: result.error,
              status: 'warning',
              duration: 5000,
              isClosable: true,
            });
          }
        })
        .catch((error) => {
          setOrderPlaceLoading(false);
          console.log(error.message);
          toast({
            title: 'Error',
            description: error.message,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        });
    }

    if (paymentType.type === 'online' && paymentType.name === 'Stripe') {
      if (!stripe || !elements) return;
      let payment_amount = cart.total;
      if (paymentAmount) {
        payment_amount = paymentAmount.value;
      }

      const { token, error } = await stripe.createToken(
        elements.getElement(CardElement)
      );

      if (!error) {
        setOrderPlaceLoading(true);
        const name_on_card = getValues('card_holder_name');
        const account = token.id;
        placeOrderHandler(
          data,
          deliveryCharge,
          paymentType,
          shippingMethod,
          rentMyCartToken,
          sameAsAbove,
          {
            payment_amount,
            account,
            name_on_card,
          },
          subscriberId
        )
          .then((response) => {
            const { result } = response?.data;
            if (result.hasOwnProperty('data')) {
              const { order } = result?.data;
              const orderUID = order?.data?.uid;

              console.log('RM checkout result:::', result);

              localStorage.removeItem('rentMyCartToken');
              localStorage.removeItem('rentMyUserCart');
              setOrderPlaceLoading(false);
              router.push(`/rentmy/checkout/complete/${orderUID}`);
            } else {
              setOrderPlaceLoading(false);
              toast({
                title: 'Error',
                description: result.error,
                status: 'warning',
                duration: 5000,
                isClosable: true,
              });
            }
          })
          .catch((error) => {
            setOrderPlaceLoading(false);
            console.log(error.message);
          });
      } else {
        setOrderPlaceLoading(false);
        toast({
          title: 'Credit Card',
          description: error.type,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  if (cart && cart?.cart_items.length) {
    return (
      <Box bg="gray.100">
        <Container maxW={1440} py={4}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Box
              display="flex"
              p={5}
              flexWrap="wrap"
              bg="white"
              rounded={8}
              justifyContent="space-between"
            >
              <Box
                flex="0 0"
                flexBasis="auto"
                width={['100%', '100%', '65%', '65%']}
                mb={4}
              >
                <Text fontSize="1.4rem" fontWeight={600} mb={8}>
                  Address
                </Text>

                <Flex flexWrap="wrap" justifyContent="space-between">
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.first_name}
                  >
                    <FormLabel htmlFor="first_name">First Name</FormLabel>
                    <Input
                      id="first_name"
                      name="first_name"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.first_name?.message}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.last_name}
                  >
                    <FormLabel htmlFor="last_name">Last Name</FormLabel>
                    <Input
                      id="last_name"
                      name="last_name"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.last_name?.message}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.mobile}
                  >
                    <FormLabel htmlFor="mobile">Mobile Number</FormLabel>
                    <Input
                      id="mobile"
                      name="mobile"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.mobile?.message}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.email}
                  >
                    <FormLabel htmlFor="email">Email address</FormLabel>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.email?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl flex="0 0" flexBasis="auto" width="100%" mb={4}>
                    <FormLabel htmlFor="company">Company Name</FormLabel>
                    <Input
                      id="company"
                      name="company"
                      ref={register}
                      type="text"
                    />
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width="100%"
                    mb={4}
                    isInvalid={errors?.country}
                  >
                    <FormLabel htmlFor="country">Country</FormLabel>
                    <Select
                      id="country"
                      name="country"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    >
                      {countryList.map((item) => (
                        <option
                          value={item.abbreviation}
                          key={item.country + '_' + item?.abbreviation}
                        >
                          {item.country}
                        </option>
                      ))}
                    </Select>
                    <FormErrorMessage>
                      {errors?.country?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.address_line1}
                  >
                    <FormLabel htmlFor="address_line1">
                      Address Line 1{' '}
                    </FormLabel>
                    <Input
                      id="address_line1"
                      name="address_line1"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.address_line1?.message}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                  >
                    <FormLabel htmlFor="address2">Address Line 2</FormLabel>
                    <Input
                      id="address2"
                      name="address2"
                      ref={register}
                      type="text"
                    />
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.city}
                  >
                    <FormLabel htmlFor="city">City</FormLabel>
                    <Input
                      id="city"
                      name="city"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>{errors?.city?.message}</FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.zipcode}
                  >
                    <FormLabel htmlFor="zipcode">Zipcode</FormLabel>
                    <Input
                      id="zipcode"
                      name="zipcode"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.zipcode?.message}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '48%', '48%']}
                    mb={4}
                    isInvalid={errors?.state}
                  >
                    <FormLabel htmlFor="state">State</FormLabel>
                    <Input
                      id="state"
                      name="state"
                      type="text"
                      ref={register({
                        required: 'Field is required',
                      })}
                    />
                    <FormErrorMessage>
                      {errors?.state?.message}
                    </FormErrorMessage>
                  </FormControl>
                </Flex>

                {/* Fullfillment */}
                <Box mt={4}>
                  <Text fontSize="1.4rem" fontWeight={600} mb={4}>
                    Fullfillment
                  </Text>
                  <Stack {...group} mb={4} direction={['column', 'row']}>
                    {deliveryOption.map((value) => {
                      const radio = getRadioProps({
                        value,
                      });
                      return (
                        <RadioCard key={value} {...radio}>
                          {value.toLocaleUpperCase()}
                        </RadioCard>
                      );
                    })}
                  </Stack>

                  {deliveryChoice === 'pickup' ? (
                    <RadioGroup
                      value={deliveryCharge?.id}
                      onChange={pickUpLocationChangeHandler}
                    >
                      <Stack>
                        {locationList.map((locationItem) => (
                          <Radio value={locationItem.id} key={locationItem.id}>
                            <Text fontWeight={300}>
                              {locationItem.name}({locationItem.location})
                            </Text>
                          </Radio>
                        ))}
                      </Stack>
                    </RadioGroup>
                  ) : null}

                  {deliveryChoice === 'delivery' ? (
                    <Box>
                      <Box>
                        <Checkbox
                          isChecked={sameAsAbove.delivery}
                          mb={4}
                          onChange={(e) =>
                            setSameAsAbove({
                              ...sameAsAbove,
                              delivery: e.target.checked,
                            })
                          }
                        >
                          <Text fontSize=".9rem">Same as above</Text>
                        </Checkbox>
                      </Box>

                      {!sameAsAbove.delivery ? (
                        <Flex flexWrap="wrap" justifyContent="space-between">
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_first_name}
                          >
                            <FormLabel htmlFor="shipping_first_name">
                              First Name
                            </FormLabel>
                            <Input
                              id="shipping_first_name"
                              name="shipping_first_name"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_first_name?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_last_name}
                          >
                            <FormLabel htmlFor="shipping_last_name">
                              Last Name
                            </FormLabel>
                            <Input
                              id="shipping_last_name"
                              name="shipping_last_name"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_last_name?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_mobile}
                          >
                            <FormLabel htmlFor="shipping_mobile">
                              Mobile Number
                            </FormLabel>
                            <Input
                              id="shipping_mobile"
                              name="shipping_mobile"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_mobile?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_email}
                          >
                            <FormLabel htmlFor="shipping_email">
                              Email address
                            </FormLabel>
                            <Input
                              id="shipping_email"
                              name="shipping_email"
                              type="email"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_email?.message}
                            </FormErrorMessage>
                          </FormControl>

                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width="100%"
                            mb={4}
                          >
                            <FormLabel htmlFor="shipping_company">
                              Company Name
                            </FormLabel>
                            <Input
                              id="shipping_company"
                              name="shipping_company"
                              ref={register}
                              type="text"
                            />
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width="100%"
                            mb={4}
                            isInvalid={errors?.shipping_country}
                          >
                            <FormLabel htmlFor="shipping_country">
                              Country
                            </FormLabel>
                            <Select
                              id="shipping_country"
                              name="shipping_country"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            >
                              {countryList.map((item) => (
                                <option value={item.abbreviation} key={item.country + '_' + item?.abbreviation}>
                                  {item.country}
                                </option>
                              ))}
                            </Select>
                            <FormErrorMessage>
                              {errors?.shipping_country?.message}
                            </FormErrorMessage>
                          </FormControl>

                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_address_line1}
                          >
                            <FormLabel htmlFor="shipping_address_line1">
                              Address Line 1{' '}
                            </FormLabel>
                            <Input
                              id="shipping_address_line1"
                              name="shipping_address_line1"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_address_line1?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                          >
                            <FormLabel htmlFor="shipping_address2">
                              Address Line 2
                            </FormLabel>
                            <Input
                              id="shipping_address2"
                              name="shipping_address2"
                              ref={register}
                              type="text"
                            />
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_city}
                          >
                            <FormLabel htmlFor="shipping_city">City</FormLabel>
                            <Input
                              id="shipping_city"
                              name="shipping_city"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_city?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_zipcode}
                          >
                            <FormLabel htmlFor="shipping_zipcode">
                              Zipcode
                            </FormLabel>
                            <Input
                              id="shipping_zipcode"
                              name="shipping_zipcode"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_zipcode?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_state}
                          >
                            <FormLabel htmlFor="shipping_state">
                              State
                            </FormLabel>
                            <Input
                              id="shipping_state"
                              name="shipping_state"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_state?.message}
                            </FormErrorMessage>
                          </FormControl>
                        </Flex>
                      ) : null}

                      {deliveryCharge !== '' ? (
                        <Text mb={4}>
                          Delivery Cost: ${deliveryCharge?.charge}
                        </Text>
                      ) : null}

                      <Button
                        size="sm"
                        colorScheme="yellow"
                        onClick={getDeliveryCostOption}
                        isLoading={deliveryLoading}
                        loadingText="please wait"
                      >
                        Get Delivery Cost
                      </Button>
                    </Box>
                  ) : null}

                  {deliveryChoice === 'shipping' ? (
                    <Box>
                      <Box>
                        <Checkbox
                          isChecked={sameAsAbove.shipping}
                          mb={4}
                          onChange={(e) =>
                            setSameAsAbove({
                              ...sameAsAbove,
                              shipping: e.target.checked,
                            })
                          }
                        >
                          <Text fontSize=".9rem">Same as above</Text>
                        </Checkbox>
                      </Box>

                      {!sameAsAbove.shipping ? (
                        <Flex flexWrap="wrap" justifyContent="space-between">
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_first_name}
                          >
                            <FormLabel htmlFor="shipping_first_name">
                              First Name
                            </FormLabel>
                            <Input
                              id="shipping_first_name"
                              name="shipping_first_name"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_first_name?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_last_name}
                          >
                            <FormLabel htmlFor="shipping_last_name">
                              Last Name
                            </FormLabel>
                            <Input
                              id="shipping_last_name"
                              name="shipping_last_name"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_last_name?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_mobile}
                          >
                            <FormLabel htmlFor="shipping_mobile">
                              Mobile Number
                            </FormLabel>
                            <Input
                              id="shipping_mobile"
                              name="shipping_mobile"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_mobile?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_email}
                          >
                            <FormLabel htmlFor="shipping_email">
                              Email address
                            </FormLabel>
                            <Input
                              id="shipping_email"
                              name="shipping_email"
                              type="email"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_email?.message}
                            </FormErrorMessage>
                          </FormControl>

                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width="100%"
                            mb={4}
                          >
                            <FormLabel htmlFor="shipping_company">
                              Company Name
                            </FormLabel>
                            <Input
                              id="shipping_company"
                              ref={register}
                              name="shipping_company"
                              type="text"
                            />
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width="100%"
                            mb={4}
                            isInvalid={errors?.shipping_country}
                          >
                            <FormLabel htmlFor="shipping_country">
                              Country
                            </FormLabel>
                            <Select
                              id="shipping_country"
                              name="shipping_country"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            >
                              {countryList.map((item) => (
                                <option value={item.abbreviation} key={item.country + '_' + item?.abbreviation}>
                                  {item.country}
                                </option>
                              ))}
                            </Select>
                            <FormErrorMessage>
                              {errors?.shipping_country?.message}
                            </FormErrorMessage>
                          </FormControl>

                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_address_line1}
                          >
                            <FormLabel htmlFor="shipping_address_line1">
                              Address Line 1{' '}
                            </FormLabel>
                            <Input
                              id="shipping_address_line1"
                              name="shipping_address_line1"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_address_line1?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                          >
                            <FormLabel htmlFor="shipping_address2">
                              Address Line 2
                            </FormLabel>
                            <Input
                              id="shipping_address2"
                              ref={register}
                              name="shipping_address2"
                              type="text"
                            />
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_city}
                          >
                            <FormLabel htmlFor="shipping_city">City</FormLabel>
                            <Input
                              id="shipping_city"
                              name="shipping_city"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_city?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_zipcode}
                          >
                            <FormLabel htmlFor="shipping_zipcode">
                              Zipcode
                            </FormLabel>
                            <Input
                              id="shipping_zipcode"
                              name="shipping_zipcode"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_zipcode?.message}
                            </FormErrorMessage>
                          </FormControl>
                          <FormControl
                            flex="0 0"
                            flexBasis="auto"
                            width={['100%', '100%', '48%', '48%']}
                            mb={4}
                            isInvalid={errors?.shipping_state}
                          >
                            <FormLabel htmlFor="shipping_state">
                              State
                            </FormLabel>
                            <Input
                              id="shipping_state"
                              name="shipping_state"
                              type="text"
                              ref={register({
                                required: 'Field is required',
                              })}
                            />
                            <FormErrorMessage>
                              {errors?.shipping_state?.message}
                            </FormErrorMessage>
                          </FormControl>
                        </Flex>
                      ) : null}

                      {shippingMethod ? (
                        <Scrollbars
                          style={{
                            height: '300px',
                            marginBottom: '20px',
                          }}
                          autoHeightMin={0}
                          autoHeightMax={200}
                          autoHide
                          autoHideTimeout={500}
                          autoHideDuration={200}
                        >
                          <RadioGroup onChange={shippingMethodChange}>
                            <Table variant="simple" size="sm">
                              <Tbody>
                                <Tr>
                                  <Td fontWeight={600}>
                                    <Radio value="standard" key="standard">
                                      {shippingMethod.standard.carrier_code}
                                    </Radio>
                                  </Td>
                                  <Td isNumeric fontWeight={600}>
                                    $
                                    {get2DecPoint(
                                      shippingMethod.standard.charge
                                    )}
                                  </Td>
                                </Tr>
                                {shippingMethod.carrier.map((shipping) => (
                                  <Tr key={shipping?.rate_id}>
                                    <Td fontWeight={600}>
                                      <Radio
                                        value={shipping.rate_id}
                                        key={shipping.rate_id}
                                      >
                                        {shipping.service_name}
                                      </Radio>
                                    </Td>
                                    <Td fontWeight={600} isNumeric>
                                      ${get2DecPoint(shipping.charge)}
                                    </Td>
                                  </Tr>
                                ))}
                              </Tbody>
                            </Table>
                          </RadioGroup>
                        </Scrollbars>
                      ) : null}

                      <Button
                        size="sm"
                        colorScheme="yellow"
                        onClick={getShippingMethodOptions}
                        isLoading={shippingLoading}
                        loadingText="please wait"
                      >
                        Get Shipping Method
                      </Button>
                    </Box>
                  ) : null}
                </Box>
              </Box>

              <Box
                flex="0 0"
                flexBasis="auto"
                width={['100%', '100%', '32%', '32%']}
                borderWidth={1}
                rounded={4}
                p={4}
              >
                <Text fontSize="1.4rem" fontWeight={600} mb={8}>
                  Your Order Summary
                </Text>
                <Box mb={8}>
                  <Table size="sm">
                    <Tbody>
                      {cart?.cart_items.map((item) => (
                        <Tr key={item.id}>
                          <Td>
                            <Image
                              src={getImage(item.product)}
                              priority
                              alt='product image'
                              width="60px"
                              height="auto"
                            />
                          </Td>
                          <Td>
                            <Text>{item.product.name}</Text>
                            <Text
                              as="span"
                              fontSize=".8rem"
                              color="gray.500"
                              mr={4}
                            >
                              quantity: {item.quantity}
                            </Text>
                            <Text as="span" fontSize=".8rem" color="gray.500">
                              price: ${item.price}
                            </Text>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>

                <Box>
                  <Table size="sm" variant="unstyled">
                    <Tbody>
                      {cart ? (
                        <>
                          {cart.coupon_id ? (
                            <Tr>
                              <Td fontWeight={700}>Coupon Applied</Td>
                              <Td>${get2DecPoint(cart.coupon_amount)}</Td>
                            </Tr>
                          ) : null}
                          <Tr>
                            <Td fontWeight={700}>Subtotal</Td>
                            <Td>${get2DecPoint(cart.sub_total)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Deposit Amount</Td>
                            <Td>${get2DecPoint(cart.deposit_amount)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Tax</Td>
                            <Td>{cart.tax.length ? '$0.00' : '$0.00'}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Shipping Charge</Td>
                            <Td>${get2DecPoint(cart.delivery_charge)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Delivery Tax</Td>
                            <Td>${get2DecPoint(cart.delivery_tax)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontSize="1.1rem" fontWeight={700}>
                              Total
                            </Td>
                            <Td fontSize="1.1rem">
                              ${get2DecPoint(cart.total)}
                            </Td>
                          </Tr>
                        </>
                      ) : null}
                    </Tbody>
                  </Table>
                </Box>

                {/* Payment method */}
                <Box py="15px">
                  <Stack
                    {...paymentChoiceGroup}
                    mb={4}
                    direction={['column', 'row']}
                  >
                    {paymentGetwayOption.map((item) => {
                      const paymentChoiceRadio = paymentChoiceRadioProps({
                        value: item.id,
                      });
                      return (
                        <RadioCard
                          fontSize="12px"
                          key={item.id}
                          {...paymentChoiceRadio}
                        >
                          {item.name.toLocaleUpperCase()}
                        </RadioCard>
                      );
                    })}
                  </Stack>
                </Box>

                {paymentType && paymentType?.config?.add_note ? (
                  <FormControl isInvalid={errors?.note} mb={4}>
                    <FormLabel htmlFor="note">Note</FormLabel>
                    <Input
                      id="note"
                      name="note"
                      type="text"
                      ref={register({ required: true })}
                    />
                    <FormErrorMessage>
                      {errors.note?.type === 'required' && 'Note is required'}
                    </FormErrorMessage>
                  </FormControl>
                ) : null}

                {paymentType && paymentType?.name === 'Stripe' ? (
                  <>
                    {payment.type === 'percent' &&
                      payment?.booking !== '0' &&
                      payment?.booking !== '100' ? (
                      <Box mb={4}>
                        <Text fontSize="14px" mb={2}>
                          A {payment?.booking}% down payment is required to
                          secure your reservation. Please choose an option and
                          pay to proceed.
                        </Text>
                        {paymentAmount ? (
                          <>
                            <RadioGroup
                              mb={4}
                              size="sm"
                              value={paymentRadio}
                              onChange={(value) => setPaymentRadio(value)}
                            >
                              <Stack>
                                <Radio value="fullPayment">
                                  <Text fontSize="13px" fontWeight="600">
                                    Pay full amount due
                                  </Text>
                                </Radio>
                                <Radio value="downPayment">
                                  <Text fontSize="13px" fontWeight="600">
                                    ${paymentAmount?.min} Now
                                  </Text>
                                </Radio>
                              </Stack>
                            </RadioGroup>

                            <FormControl>
                              <FormLabel
                                fontSize="13px"
                                htmlFor="amount_to_pay"
                              >
                                Amount to pay
                              </FormLabel>
                              <InputGroup size="sm">
                                <InputLeftAddon children="$" />
                                <NumberInput
                                  value={paymentAmount.value}
                                  onChange={(value) =>
                                    setPaymentAmount({
                                      ...paymentAmount,
                                      value: value,
                                    })
                                  }
                                  min={paymentAmount.min}
                                  max={paymentAmount.max}
                                  size="sm"
                                >
                                  <NumberInputField />
                                  <NumberInputStepper>
                                    <NumberIncrementStepper />
                                    <NumberDecrementStepper />
                                  </NumberInputStepper>
                                </NumberInput>
                                <InputRightAddon children="USD" />
                              </InputGroup>
                            </FormControl>
                          </>
                        ) : null}
                      </Box>
                    ) : null}

                    <FormControl isInvalid={errors?.card_holder_name} mb={4}>
                      <FormLabel fontSize="13px" htmlFor="card_holder_name">
                        Card Holder Name
                      </FormLabel>
                      <Input
                        size="sm"
                        id="card_holder_name"
                        name="card_holder_name"
                        type="text"
                        ref={register({
                          required: true,
                        })}
                      />
                      <FormErrorMessage>
                        {errors.card_holder_name?.type === 'required' &&
                          'Card holder name is required'}
                      </FormErrorMessage>
                    </FormControl>

                    <Box bg="white" p={5} rounded={5} mb={5} borderWidth={1}>
                      <CardElement options={{ hidePostalCode: true }} />
                    </Box>
                  </>
                ) : null}

                <Controller
                  control={control}
                  name="terms"
                  defaultValue={false}
                  rules={{
                    required: 'Accept Terms & Condition to continue.',
                  }}
                  render={(
                    { onChange, onBlur, value, name, ref },
                    { invalid, isTouched, isDirty }
                  ) => (
                    <Checkbox
                      onBlur={onBlur}
                      onChange={(e) => onChange(e.target.checked)}
                      checked={value}
                    >
                      <Text fontSize=".8rem">
                        I have read and agree with the terms & conditions
                      </Text>
                    </Checkbox>
                  )}
                />
                {errors?.terms ? (
                  <Text fontSize=".75rem" color="red.400">
                    {' '}
                    {errors?.terms?.message}{' '}
                  </Text>
                ) : null}

                <Button
                  mt={4}
                  mr={4}
                  colorScheme="purple"
                  onClick={backToCartPage}
                >
                  Back to cart
                </Button>
                <Button
                  mt={4}
                  colorScheme="green"
                  type="submit"
                  isLoading={orderPlaceLoading}
                  loadingText="placing order..."
                  disabled={
                    !(
                      deliveryCharge !== '' &&
                      paymentType.hasOwnProperty('name')
                    ) || orderPlaceLoading
                  }
                >
                  Place Order
                </Button>
              </Box>
            </Box>
          </form>
        </Container>
      </Box>
    );
  } else {
    return (
      <Box bg="gray.100">
        <Container maxW={1440} py={4}>
          Your Cart is empty
        </Container>
      </Box>
    );
  }
}

export default RentMyCheckout;
