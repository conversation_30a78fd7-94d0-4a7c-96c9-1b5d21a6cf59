import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Box,
  Text,
  Image,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  CloseButton,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
} from '@chakra-ui/react';
import { useParams } from 'react-router-dom';
import { completeOrder } from './service';
import imagePlaceholder from '../../builder/components/rentmy/product-image-placeholder.jpg';
import rentmyLogo from 'https://b1547765.smushcdn.com/1547765/wp-content/uploads/2019/11/logo.png?lossy=1&strip=1&webp=1';
// import rentmyLogo from '/static/media/rentmy.png';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyCheckoutSuccess() {
  const [order, setOrder] = useState(null);
  const { id } = useParams();

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
          '/products/' +
          rentMyStoreId +
          '/' +
          product.id +
          '/' +
          product.images[0]?.image_small
        : imagePlaceholder;
      return src;
    },
    [order]
  );

  function getTwoDecimal(x) {
    return Number.parseFloat(x).toFixed(2);
  }

  useEffect(() => {
    completeOrder(id)
      .then((response) => {
        if (response.data.result.hasOwnProperty('data')) {
          setOrder(response.data.result.data);
        }
      })
      .catch((error) => console.log(error.message));
  }, []);

  return (
    <Box bg="gray.100">
      <Container maxW={1240} py={4}>
        <Box bg="white" rounded={8} p={8}>
          <Box display="flex" bg="gray.100" mb={4}>
            <Image src={rentmyLogo} width="100px" mr={4} alt='rentmy logo'/>
            <Text
              fontSize="1.4rem"
              textTransform="uppercase"
              fontWeight={600}
              my={2}
            >
              Checkout
            </Text>
          </Box>
          <Alert status="success" mb={4}>
            <AlertIcon />
            Thank you for your order
            <CloseButton position="absolute" right="8px" top="8px" />
          </Alert>
          <Box display="flex" justifyContent="space-between">
            <Box
              flex="0 0"
              flexBasis="auto"
              width={['100%', '100%', '60%', '60%']}
            >
              <Table size="sm">
                <Thead>
                  <Tr>
                    <Th></Th>
                    <Th>Product</Th>
                    <Th>Unit Price</Th>
                    <Th>Quantity</Th>
                    <Th>Sub Total</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {order?.order_items.map((item) => (
                    <Tr key={item.id}>
                      <Td>
                        <Image
                          src={getImage(item.product)}
                          priority
                          alt='product image'
                          width="60px"
                          height="auto"
                        />
                      </Td>
                      <Td>
                        <Text>{item.product.name}</Text>
                      </Td>
                      <Td>
                        <Text as="span" fontSize=".8rem" color="gray.500">
                          ${item.price}
                        </Text>
                      </Td>
                      <Td>
                        <Text
                          as="span"
                          fontSize=".8rem"
                          color="gray.500"
                          mr={4}
                        >
                          {item.quantity}
                        </Text>
                      </Td>
                      <Td>
                        <Text
                          as="span"
                          fontSize=".8rem"
                          color="gray.500"
                          mr={4}
                        >
                          ${item.sub_total}
                        </Text>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
            <Box
              flex="0 0"
              flexBasis="auto"
              width={['100%', '100%', '36%', '36%']}
            >
              <Table size="sm" variant="striped">
                <Tbody>
                  {order ? (
                    <>
                      <Tr>
                        <Td fontWeight={700}>Subtotal</Td>
                        <Td>${order.sub_total}</Td>
                      </Tr>
                      <Tr>
                        <Td fontWeight={700}>Deposit Amount</Td>
                        <Td>${order.deposit_amount}</Td>
                      </Tr>
                      <Tr>
                        <Td fontWeight={700}>Tax</Td>
                        <Td>{order.tax.length ? '$0' : '$0'}</Td>
                      </Tr>
                      <Tr>
                        <Td fontWeight={700}>Shipping Charge</Td>
                        <Td>${getTwoDecimal(order.delivery_charge)}</Td>
                      </Tr>
                      <Tr>
                        <Td fontWeight={700}>Delivery Tax</Td>
                        <Td>${getTwoDecimal(order.delivery_tax)}</Td>
                      </Tr>
                      <Tr>
                        <Td fontSize="1.1rem" fontWeight={700}>
                          Total
                        </Td>
                        <Td fontSize="1.1rem">${getTwoDecimal(order.total)}</Td>
                      </Tr>
                    </>
                  ) : null}
                </Tbody>
              </Table>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
}

export default RentMyCheckoutSuccess;
