import React, { useEffect, useState } from 'react'
import { getGetwaySettings } from './service';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js'
import RentMyCheckout from './RentMyCheckout'


function RentMyCheckoutWrapper() {

    const [stripePromise, setStripePromise] = useState(null);

    useEffect(() => {
        getGetwaySettings()
        .then(response => {
            if (response.data.result.hasOwnProperty('data')) {
                const { data:getways } = response.data.result;
                const cardGetway = getways.find(item => item.name === "Stripe");
                if (cardGetway) {
                    const stripePromiseData = loadStripe(cardGetway.config.publishable_key);
                    setStripePromise(stripePromiseData);
                }

            }
        }).catch(error => console.log(error.message))
    }, [])


    if(stripePromise) {
        return (
            <Elements stripe={stripePromise} key={stripePromise}>
                <RentMyCheckout />
            </Elements>
        )
    }

    return <RentMyCheckout />
}

export default RentMyCheckoutWrapper