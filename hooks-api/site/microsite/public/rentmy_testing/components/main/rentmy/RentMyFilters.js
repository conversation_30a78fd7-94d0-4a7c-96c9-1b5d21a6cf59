import {
    Box,
    Flex,
    Accordion,
    AccordionIcon,
    AccordionItem,
    AccordionButton,
    AccordionPanel,
    Checkbox,
    Text,
    Radio,
    Stack,
    CheckboxGroup,
    RadioGroup,
    NumberInput,
    NumberInputField,
    NumberInputStepper,
    NumberIncrementStepper,
    NumberD<PERSON>rementStepper,
    But<PERSON>
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { globalSettingData } from 'store/slices/globalSettingSlice';
import { useSelector } from 'react-redux';


const RentMyFilters = ({
    filters,
    tags,
    selectedTags,
    setSelectedTags,
    selectedType,
    setSelectedType,
    setMinPrice,
    minPrice,
    setMaxPrice,
    maxPrice,
    fetchData,
    onResetFilters,
    apiCallStat,
    setApiCallStat,
    setPage,
}) => {
    
    const settings = {
        "settingsId": null,
        "showSignIn": true,
        "primaryColor": "#fb0303",
        "secondaryColor": "rgba(126,211,33,1)",
        "accentColor": "rgba(65,117,5,1)",
        "logo": {
            "_id": "651a4f6597fee0e6e785085b",
            "altText": "",
            "filePath": "i8s2uzjm/image/tea-49982469-of02u7w7-removebg-preview-24684430.webp"
        },
        "favIcon": {
            "_id": "651a4f5797fee0e6e785083e",
            "altText": "",
            "filePath": "i8s2uzjm/image/finger.webp"
        },
        "brand": "Jahid brand",
        "siteFont": "ABeeZee",
        "advanceSeoSettings": {
            "requestAudit": {
                "isRequested": false,
                "auditLogs": []
            },
            "progress": 0,
            "overallProgress": "0.00",
            "homepageProgress": "0.00",
            "incompleteMeta": "0.00",
            "isRobotTxtEnable": true,
            "focusKeywords": [],
            "isAllowIndexing": true,
            "robotsTxt": "Robot.Txt 12345"
        },
        "siteName": "",
        "seo": {
            "adsense": {
                "_id": "636e2a7fb1804d5a06f5e2ac",
                "active": true,
                "content": "ca-pub-9775757577653408"
            },
            "searchConsole": {
                "_id": "6371c3afb1804d5a06f781b7",
                "active": false,
                "content": "aA2M-HM29FrEbNU913HLd1QCC4TbV9bZFCcCUkGOYxg"
            },
            "pinterest": {
                "_id": "63b56ba2b1804d5a0614ec37",
                "active": true,
                "content": "2f758c3c494a6a15b347f5bfb36363d6"
            },
            "facebookPixel": {
                "_id": "651fdbbcc6dc7baeae7906d5",
                "active": true,
                "content": "1486704542175999"
            },
            "activeBlogPage": {
                "active": false,
                "_id": "6528d3f3b7f1a6cdde649fa2",
                "content": {
                    "name": "blog101",
                    "generation": "1st",
                    "section": {
                        "ids": [
                            "b29aa84f-a797-4148-8dc6-88bd8f18abdf"
                        ],
                        "entries": {
                            "b29aa84f-a797-4148-8dc6-88bd8f18abdf": {
                                "id": "b29aa84f-a797-4148-8dc6-88bd8f18abdf",
                                "type": "uncategorized",
                                "style": {
                                    "padding": {
                                        "top": {
                                            "base": 50,
                                            "md": 50,
                                            "lg": 100
                                        },
                                        "right": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "bottom": {
                                            "base": 50,
                                            "md": 50,
                                            "lg": 50
                                        },
                                        "left": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        }
                                    },
                                    "margin": {
                                        "top": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "right": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "bottom": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        },
                                        "left": {
                                            "base": 0,
                                            "md": 0,
                                            "lg": 0
                                        }
                                    },
                                    "isGradient": false,
                                    "isGradientAnimated": false,
                                    "bgColor": "transparent",
                                    "gradientDirection": "to right",
                                    "gradientColorOne": "#37d5d6",
                                    "gradientColorTwo": "#36096d",
                                    "gradientColorThree": "",
                                    "bgBlendMode": "normal",
                                    "isTintActive": false,
                                    "bgImage": {
                                        "path": "",
                                        "altText": "",
                                        "id": ""
                                    },
                                    "border": {
                                        "top": 0,
                                        "right": 0,
                                        "bottom": 0,
                                        "left": 0,
                                        "color": "#3F3047",
                                        "style": "solid"
                                    },
                                    "rounded": {
                                        "topLeft": 0,
                                        "topRight": 4,
                                        "bottomLeft": 4,
                                        "bottomRight": 4
                                    }
                                },
                                "fullWidth": false,
                                "columnGap": 10,
                                "children": [
                                    "583e9ea9-b20f-43a3-9ba8-4af7a0b66057"
                                ]
                            }
                        }
                    },
                    "column": {
                        "583e9ea9-b20f-43a3-9ba8-4af7a0b66057": {
                            "id": "583e9ea9-b20f-43a3-9ba8-4af7a0b66057",
                            "style": {
                                "width": {
                                    "base": 100
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "rounded": {
                                    "topLeft": 0,
                                    "topRight": 0,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "boxShadow": "none",
                                "bgColor": "#ffffff",
                                "hover": {
                                    "backgroundColor": "transparent",
                                    "color": "#212121",
                                    "transformValue": "none",
                                    "transform": {
                                        "scaleX": 1,
                                        "scaleY": 1,
                                        "skewX": 0,
                                        "skewY": 0,
                                        "rotateX": 0,
                                        "rotateY": 0,
                                        "rotateZ": 0,
                                        "translateX": 0,
                                        "translateY": 0
                                    }
                                },
                                "animation": {
                                    "animation": false,
                                    "value": "fadeIn",
                                    "onScroll": false,
                                    "isRepeat": false,
                                    "transition": {
                                        "type": "spring",
                                        "duration": 0.5,
                                        "delay": 0,
                                        "bounce": 0.4,
                                        "repeat": 1,
                                        "repeatType": "mirror",
                                        "repeatDelay": 0
                                    }
                                },
                                "bgImage": {
                                    "path": "",
                                    "altText": "",
                                    "id": ""
                                },
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "#3F3047",
                                    "style": "solid"
                                }
                            },
                            "children": [
                                "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67"
                            ]
                        }
                    },
                    "component": {
                        "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67": {
                            "id": "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67",
                            "type": "blog-grid",
                            "cardBgColor": "red",
                            "style": {
                                "margin": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0
                                },
                                "numberOfColumn": {
                                    "base": 1,
                                    "md": 2,
                                    "lg": 4
                                },
                                "columnGap": 10,
                                "rowGap": 20,
                                "animation": {
                                    "animation": false,
                                    "value": "fadeIn",
                                    "onScroll": false,
                                    "isRepeat": false,
                                    "transition": {
                                        "type": "spring",
                                        "duration": 0.5,
                                        "delay": 0,
                                        "bounce": 0.4,
                                        "repeat": 1,
                                        "repeatType": "mirror",
                                        "repeatDelay": 0
                                    }
                                },
                                "layout": "list"
                            },
                            "cardStyle": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 10
                                    },
                                    "right": {
                                        "base": 10
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 10
                                    }
                                },
                                "border": {
                                    "top": 1,
                                    "right": 1,
                                    "bottom": 1,
                                    "left": 1,
                                    "color": "gray.300",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 8,
                                    "topRight": 8,
                                    "bottomLeft": 8,
                                    "bottomRight": 8
                                },
                                "bgColor": "#FFFFFF",
                                "boxShadow": "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
                                "position": "relative",
                                "textAlign": "left",
                                "showTags": true,
                                "showTitle": true,
                                "showExcerpt": true,
                                "showComments": true,
                                "showAuthor": true
                            },
                            "searchStyle": {
                                "showSearchBar": true,
                                "justifyContent": {
                                    "base": "flex-start"
                                },
                                "height": "auto",
                                "width": "60%",
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "#666666",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 4,
                                    "topRight": 4,
                                    "bottomLeft": 4,
                                    "bottomRight": 4
                                },
                                "bgColor": "#CBD5E0",
                                "text": {
                                    "size": {
                                        "base": "16"
                                    },
                                    "color": "#212121",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "searchText": "Search",
                                "searchContainerWidth": "40%"
                            },
                            "paginationStyle": {
                                "showPagination": true,
                                "paginateView": "button",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "selectedPageColor": "blue.400",
                                "buttonVariant": "outline",
                                "color": "blue.400"
                            },
                            "filterStyle": {
                                "showFilter": true,
                                "justifyContent": {
                                    "base": "flex-end"
                                },
                                "text": {
                                    "size": {
                                        "base": "16"
                                    },
                                    "color": "#212121",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "filterText": "Filter by tags",
                                "filterView": "dropdown",
                                "width": "40%",
                                "filterContainerWidth": "60%"
                            },
                            "imageStyle": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "width": "100%",
                                "height": "200",
                                "border": {
                                    "top": 0,
                                    "right": 0,
                                    "bottom": 0,
                                    "left": 0,
                                    "color": "gray.300",
                                    "style": "none"
                                },
                                "rounded": {
                                    "topLeft": 8,
                                    "topRight": 8,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "justifyContent": "flex-start"
                            },
                            "title": {
                                "markup": "h3",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 5
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "text": {
                                    "size": {
                                        "base": "15"
                                    },
                                    "color": "gray.900",
                                    "weight": "700",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "limit": 10
                            },
                            "excerpt": {
                                "markup": "p",
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 10
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "text": {
                                    "size": {
                                        "base": "14"
                                    },
                                    "color": "gray.700",
                                    "weight": "400",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "limit": 20
                            },
                            "button": {
                                "margin": {
                                    "top": {
                                        "base": 0
                                    },
                                    "right": {
                                        "base": 0
                                    },
                                    "bottom": {
                                        "base": 0
                                    },
                                    "left": {
                                        "base": 0
                                    }
                                },
                                "padding": {
                                    "top": {
                                        "base": 4
                                    },
                                    "right": {
                                        "base": 10
                                    },
                                    "bottom": {
                                        "base": 4
                                    },
                                    "left": {
                                        "base": 10
                                    }
                                },
                                "height": "auto",
                                "width": "auto",
                                "border": {
                                    "top": 1,
                                    "right": 1,
                                    "bottom": 1,
                                    "left": 1,
                                    "color": "gray.700",
                                    "style": "solid"
                                },
                                "rounded": {
                                    "topLeft": 0,
                                    "topRight": 0,
                                    "bottomLeft": 0,
                                    "bottomRight": 0
                                },
                                "bgColor": "transparent",
                                "text": {
                                    "size": {
                                        "base": "12"
                                    },
                                    "color": "gray.900",
                                    "weight": "300",
                                    "align": {
                                        "base": "left"
                                    },
                                    "transform": "none",
                                    "lineHeight": 1.5,
                                    "letterSpacing": 0,
                                    "fontFamily": "ABeeZee",
                                    "fontStyle": "normal",
                                    "textDecoration": "none"
                                },
                                "justifyContent": "flex-start",
                                "visibility": true,
                                "data": "Read More"
                            },
                            "data": {
                                "dataSource": null,
                                "dataSourceType": "all",
                                "limit": 10,
                                "filter": {
                                    "type": "author",
                                    "value": null
                                },
                                "sort": [
                                    {
                                        "label": "Date",
                                        "value": "createdAt",
                                        "type": "desc"
                                    }
                                ]
                            },
                            "categoryStyle": {
                                "showCategory": true
                            }
                        }
                    },
                    "selected": null,
                    "selectedType": ""
                },
                "genericSettings": {
                    "responsive": {
                        "isMobile": true,
                        "isTab": false,
                        "isWeb": false
                    },
                    "pageInfo": {
                        "meta": {
                            "title": "",
                            "isIncludeInSitemap": false,
                            "isAllowPageIndex": false,
                            "isAllowImageIndex": false,
                            "isAllowNoFollow": false,
                            "maxSnippet": 0,
                            "canonicalUrl": "https://b107.pc-staging.com/blog101",
                            "pageSeoScore": 14.761904761904761,
                            "keywordDensity": 0,
                            "pageContentMatchKeywordCount": 0,
                            "altTextMatchKeywordCount": 0,
                            "internalLinkCount": 0,
                            "externalLinkCount": 0,
                            "description": null,
                            "keywords": null,
                            "image": null,
                            "focusKeywords": [],
                            "ogUrl": "https://b107.pc-staging.com/blog101",
                            "metaInfo": {
                                "totalMeta": 6,
                                "missing": 5,
                                "exist": 1
                            }
                        },
                        "renderType": "builder",
                        "isHomePage": false,
                        "isBlogPage": true,
                        "pageName": "blog101",
                        "pageTitle": "blog101",
                        "slug": "blog101"
                    },
                    "headerInfo": {
                        "headerPosition": "sticky",
                        "headerBackground": "#ffffff"
                    },
                    "_id": "6528d3f3b7f1a6cdde649fa0",
                    "__v": 0,
                    "adminType": "CA",
                    "builderType": "FE_BLOG",
                    "createdAt": "2023-10-13T05:21:55.401Z",
                    "franchiseeId": "636a25eb5d1fa80f73143ea4",
                    "updatedAt": "2024-09-18T04:43:16.583Z",
                    "id": "6528d3f3b7f1a6cdde649fa0"
                }
            }
        }
    }

    const [searchText, setSearchText] = useState(true);


    return (

        <Accordion
            allowToggle
            mt={2}
            borderColor={'gray.200'}
            defaultIndex={0}
        >
            <AccordionItem
                // background='gray.50'
                borderRadius={6}
                boxShadow='lg'
                rounded='md'
            >
                <h2>
                    <AccordionButton background='gray.200' borderRadius={6}>
                        <Box as="span" flex='1' textAlign='left' fontSize="1rem" fontWeight="semibold" py={1} textTransform={'uppercase'}>
                            Filters
                        </Box>
                        <AccordionIcon />
                    </AccordionButton>
                </h2>

                <AccordionPanel pb={4} px={2}>

                    {/* Tags filter */}
                    {filters?.tags?.show &&
                        <Box
                            h={'200px'}
                            overflowY={'auto'}
                            mb={2}
                            boxShadow='md'
                            rounded='md'
                            border={'1px solid #80808040'}
                            p={2}
                            mt={2}
                        >
                            <Text fontSize=".9rem" fontWeight="600" ml={2} mb={2} textTransform={'uppercase'} mt={3}>Tags</Text>

                            <CheckboxGroup
                                value={selectedTags}
                                onChange={(value) => {
                                    // console.log('value', value)
                                    setSelectedTags(value)
                                    setApiCallStat('filter_tag ' + value)
                                    setPage(1)
                                }}
                            >
                                <Stack
                                    spacing={2}
                                    direction={'column'}
                                    pl="10px"
                                >
                                    {tags && tags?.length > 0 && tags.map((item, index) => {
                                        return (
                                            <Checkbox
                                                key={item?.id + index}
                                                value={item?.id + ''}
                                            >
                                                {item?.name}
                                            </Checkbox>
                                        )
                                    })
                                    }
                                </Stack>
                            </CheckboxGroup>
                        </Box>
                    }

                    {/* Price range filter */}
                    {filters?.priceRange?.show &&
                        <Box
                            mt={4}
                            mb={3}
                            boxShadow='md'
                            rounded='md'
                            border={'1px solid #80808040'}
                            p={2}
                            pb={2}
                        >
                            <Text fontSize=".9rem" fontWeight="600" ml={2} my={3} textTransform={'uppercase'}>Price</Text>

                            <Flex justifyContent={'space-between'} gap={2} mt={2}>
                                <Box>
                                    <Text fontSize=".8rem" fontWeight="400" ml={2} mb={1}>Min</Text>
                                    <NumberInput
                                        // clampValueOnBlur={false}
                                        size='sm'
                                        borderRadius={6}
                                        min={0}
                                        value={minPrice ? +minPrice : ''}
                                        onChange={(value) => { setMinPrice(value) }}
                                    >
                                        <NumberInputField />
                                        <NumberInputStepper>
                                            <NumberIncrementStepper />
                                            <NumberDecrementStepper />
                                        </NumberInputStepper>
                                    </NumberInput>
                                </Box>

                                <Box>
                                    <Text fontSize=".8rem" fontWeight="400" ml={2} mb={1}>Max</Text>
                                    <NumberInput
                                        size='sm'
                                        clampValueOnBlur={false}
                                        borderRadius={6}
                                        min={0}
                                        value={maxPrice ? +maxPrice : ''}
                                        onChange={(value) => { setMaxPrice(value) }}
                                    >
                                        <NumberInputField />
                                        <NumberInputStepper>
                                            <NumberIncrementStepper />
                                            <NumberDecrementStepper />
                                        </NumberInputStepper>
                                    </NumberInput>
                                </Box>

                            </Flex>

                            <Flex justifyContent={'space-between'} gap={2} my={3}>
                                <Button
                                    fontSize={'15px'}
                                    fontWeight={700}
                                    color={'black'}
                                    size="sm"
                                    textDecoration={'none'}
                                    onClick={() => {
                                        setMinPrice(null)
                                        setMaxPrice(null)

                                        setPage(1)
                                        setApiCallStat('price_clear')

                                        // fetchData()
                                    }}
                                >
                                    Clear
                                </Button>

                                <Button
                                    fontSize={'15px'}
                                    fontWeight={700}
                                    color={'white'}
                                    size="sm"
                                    textDecoration={'none'}
                                    bg={settings.accentColor}
                                    _hover={{
                                        bg: settings.secondaryColor,
                                    }}
                                    onClick={() => {
                                        let customPayload = {
                                            price_min: +minPrice,
                                            price_max: +maxPrice,
                                        }

                                        console.log("minPrice", minPrice);
                                        console.log("maxPrice", maxPrice);

                                        setPage(1)
                                        setApiCallStat('price_submit' + minPrice + maxPrice)

                                        // fetchData(null, customPayload);
                                    }
                                    }
                                >
                                    Submit
                                </Button>
                            </Flex>
                        </Box>
                    }

                    {/* Types filters */}
                    {filters?.types?.show &&
                        <Box
                            mt={4}
                            mb={2}
                            pb={2}
                            boxShadow='md'
                            rounded='md'
                            border={'1px solid #80808040'}
                            p={2}
                        >
                            <Text fontSize=".9rem" fontWeight="600" ml={2} my={3} textTransform={'uppercase'}>Types</Text>

                            <RadioGroup
                                flex="1 1"
                                ml={2}
                                value={selectedType}
                                onChange={(value) => {
                                    // console.log('value', value)
                                    setSelectedType(value)
                                    setApiCallStat('filter_type ' + value)
                                    setPage(1)
                                }}
                            >
                                <Stack direction="column">
                                    <Radio
                                        value="rent"
                                    >
                                        Rent
                                    </Radio>

                                    <Radio
                                        value="buy"
                                    >
                                        Buy
                                    </Radio>

                                    <Radio
                                        value="all"
                                    >
                                        All
                                    </Radio>

                                </Stack>
                            </RadioGroup>

                        </Box>
                    }


                    {/* <Button
                        mt={3}
                        fontSize={'15px'}
                        fontWeight={700}
                        color={'white'}
                        size="md"
                        textDecoration={'none'}
                        bg={settings?.accentColor}
                        _hover={{
                            bg: settings.secondaryColor,
                        }}
                        onClick={() => {
                            setApiCallStat('reset_filter')
                            // onResetFilters()
                        }}
                    >
                        Reset
                    </Button> */}
                </AccordionPanel>
            </AccordionItem>
        </Accordion>
    );
};

export default RentMyFilters;