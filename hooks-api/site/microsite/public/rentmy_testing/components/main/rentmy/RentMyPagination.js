
import { Box } from "@chakra-ui/react";
import NumberPagination from "./PaginationNumber/NumberPagination";
import { PaginationText } from "./PaginationText/PaginationText";
import { useEffect } from "react";


const RentMyPagination = ({
    totalPages,
    isFetching,
    setApiCallStat,
    page, limit, setPage, component
}) => {

    const paginateView = component?.filters?.pagination?.type

    // console.log("currentPage", page);

    return (
        <Box
            display={component?.filters?.pagination?.show ? 'flex' : 'none'}
            justifyContent={'center'}
            w={'100%'}
            mt={3}
        // mt={marginTop(component, 'paginationStyle')}
        // mb={marginBottom(component, 'paginationStyle')}
        // mr={marginRight(component, 'paginationStyle')}
        // ml={marginLeft(component, 'paginationStyle')}
        // pl={2}
        >
            {
                paginateView === 'text' ?
                    <PaginationText
                        totalPages={+totalPages}
                        isFetching={isFetching}
                        limit={+limit}
                        page={+page}
                        // paginationStyle={component?.paginationStyle}

                        // setPage={setPage}

                        setPage={(page) => {
                            setPage(page)
                            setApiCallStat('page_text' + page)
                        }}
                    />
                    :
                    <NumberPagination
                        currentPage={+page}
                        totalCount={+totalPages}
                        pageSize={+limit}
                        onPageChange={page => {
                            setPage(+page)
                            setApiCallStat('page_number' + page)
                        }}
                    />
            }
        </Box>
    )

};

export default RentMyPagination;
