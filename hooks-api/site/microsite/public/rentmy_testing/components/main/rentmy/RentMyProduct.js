import { Box, Skeleton, SkeletonText, Text } from '@chakra-ui/react';
import axios from 'axios';
import truncate from 'lodash/truncate';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyProduct({ component }) {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  // const { style, cardStyle, imageStyle, title, excerpt, data } = component;

  const component2 = {
    id: '70b24573-5197-4453-85a1-300986d0ef6b',
    type: 'rentMyWithFilters',
    style: {
      margin: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      numberOfColumn: 4,
      columnGap: 10,
      rowGap: 20,
      alignProduct: 'row',
      alignSearch: 'flex-start',
    },
    cardStyle: {
      margin: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      padding: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10,
      },
      border: {
        top: 1,
        right: 1,
        bottom: 1,
        left: 1,
        color: 'gray.300',
        style: 'solid',
      },
      rounded: {
        topLeft: 8,
        topRight: 8,
        bottomLeft: 8,
        bottomRight: 8,
      },
      bgColor: '#FFFFFF',
      boxShadow: 'rgba(99, 99, 99, 0.2) 0px 2px 8px 0px',
      position: 'relative',
      textAlign: 'center',
    },
    imageStyle: {
      margin: {
        top: 0,
        right: 0,
        bottom: 10,
        left: 0,
      },
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      width: '100%',
      height: '160',
      border: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        color: 'gray.300',
        style: 'none',
      },
      rounded: {
        topLeft: 8,
        topRight: 8,
        bottomLeft: 0,
        bottomRight: 0,
      },
      justifyContent: 'flex-start',
    },
    title: {
      markup: 'h3',
      margin: {
        top: 0,
        right: 0,
        bottom: 5,
        left: 0,
      },
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      text: {
        size: '15',
        color: 'gray.900',
        weight: '700',
        align: 'left',
        transform: 'none',
        lineHeight: 1.5,
        letterSpacing: 0,
        fontFamily: 'ABeeZee',
        fontStyle: 'normal',
        textDecoration: 'none',
      },
      limit: 10,
    },
    excerpt: {
      markup: 'p',
      margin: {
        top: 0,
        right: 0,
        bottom: 10,
        left: 0,
      },
      padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
      },
      text: {
        size: '14',
        color: 'gray.700',
        weight: '400',
        align: 'left',
        transform: 'none',
        lineHeight: 1.5,
        letterSpacing: 0,
        fontFamily: 'ABeeZee',
        fontStyle: 'normal',
        textDecoration: 'none',
      },
      limit: 20,
    },
    data: {
      dataSource: null,
      locationDataSource: null,
      dataSourceType: 'all',
      limit: '100',
      sort: [
        {
          label: 'Date',
          value: 'createdAt',
          type: 'desc',
        },
      ],
    },
    filters: {
      search: {
        show: true,
        style: {},
      },
      categories: {
        show: true,
        style: {},
      },
      filter: {
        show: true,
        style: {},
      },
      priceRange: {
        show: true,
        style: {},
      },
      tags: {
        show: true,
        style: {},
      },
      types: {
        show: true,
        style: {},
      },
      sorting: {
        show: true,
        style: {},
      },
      pagination: {
        show: true,
        type: 'text',
        style: {},
      },
    },
  };
  const { style, cardStyle, imageStyle, title, excerpt, data } = component2;

  const apiUrl = useRef(null);
  const payload = useRef({});
  // const { id } = useSelector(subscriberData);

  //   console.log(data);
  switch (data.dataSourceType) {
    case 'category':
      payload.current = { limit: data.limit };
      if (data.dataSource) {
        apiUrl.current = `/category/products/${data.dataSource.value}`;
      } else {
        apiUrl.current = null;
      }
      break;
    case 'all':
      apiUrl.current = '/products/online';
      payload.current = { limit: data.limit };
      break;
    default:
      break;
  }

  const fetchData = async () => {
    setLoading(true);
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    // const tokenObject = await getRentMyToken(id);
    const tokenObject = {
      success: true,
      token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0wNyAwNToyMTowOCIsInN0b3JlX2lkIjozNTEsImxvY2F0aW9uIjozOTEsImFwcF9pZCI6Mzc4LCJleHBpcmUiOiIyMDI0LTA4LTA4IDA1OjIxOjAwIiwiaXNfb25saW5lIjowLCJzb3VyY2UiOiJhcGkiLCJkaXNhYmxlX2RlZmF1bHRfdGltZSI6ZmFsc2V9.Nv9_qboMtedRRtEgP7eFl8oMxOVSDPlv9V4rcocZceg',
      locationId: '391',
      storeId: '351',
    };
    if (tokenObject.success) {
      axios
        .request({
          url: apiUrl.current,
          method: 'POST',
          baseURL: apiBaseURL,
          data: payload.current,
          headers: {
            Authorization: `Bearer ${tokenObject.token}`,
            location: data?.locationDataSource?.value || tokenObject.locationId,
          },
        })
        .then((response) => {
          const { result } = response.data;
          setProducts(result.data);
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  };

  const getImage = (product) => {
    const rentMyStoreId = localStorage.getItem('rentMystrId');
    const src = product.images.length
      ? mediaUrl +
        '/products/' +
        rentMyStoreId +
        '/' +
        product.id +
        '/' +
        product.images[0]?.image_small
      : '/static/media/product-image-placeholder.jpg';
    return src;
  };

  // Accurate round price number
  const get2DecPoint = (number = 0.0) => {
    return number.toLocaleString('en-US', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  };

  const descriptionText = (pricing, key, tailText) => {
    const priceDescription =
      pricing[key][0].duration > 1
        ? `Starting at $${get2DecPoint(pricing[key][0].price)} USD for ${
            pricing[key][0].duration
          } ${tailText}s`
        : `Starting at $${get2DecPoint(
            pricing[key][0].price
          )} USD per ${tailText}`;
    return priceDescription;
  };

  const getDescription = (prices) => {
    let pricing = prices[0];
    if (pricing.hasOwnProperty('hourly')) {
      const priceDescription = descriptionText(pricing, 'hourly', 'hour');
      return priceDescription;
    } else if (pricing.hasOwnProperty('daily')) {
      const priceDescription = descriptionText(pricing, 'daily', 'day');
      return priceDescription;
    } else if (pricing.hasOwnProperty('weekly')) {
      const priceDescription = descriptionText(pricing, 'weekly', 'week');
      return priceDescription;
    } else if (pricing.hasOwnProperty('monthly')) {
      const priceDescription = descriptionText(pricing, 'monthly', 'month');
      return priceDescription;
    } else if (pricing.hasOwnProperty('yearly')) {
      const priceDescription = descriptionText(pricing, 'yearly', 'year');
      return priceDescription;
    } else {
      return `Buy now for $${pricing.base.price} USD`;
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const getSkeleton = (item) => {
    const skeleton = Array(item)
      .fill()
      .map((_, i) => (
        <Box
          key={i}
          flex="0 0"
          flexBasis="auto"
          width={['100%', '50%', '50%', `${100 / style.numberOfColumn}%`]}
          mb={`${style.rowGap}px`}
          px={[
            0,
            `${style.columnGap}px`,
            `${style.columnGap}px`,
            `${style.columnGap}px`,
          ]}
        >
          <Box
            rounded={`
                        ${cardStyle.rounded.topLeft}px 
                        ${cardStyle.rounded.topRight}px 
                        ${cardStyle.rounded.bottomRight}px 
                        ${cardStyle.rounded.bottomLeft}px
                    `}
            pt={`${cardStyle.padding.top}px`}
            pb={`${cardStyle.padding.bottom}px`}
            pl={`${cardStyle.padding.left}px`}
            pr={`${cardStyle.padding.right}px`}
            borderTopWidth={`${cardStyle.border.top}px`}
            borderBottomWidth={`${cardStyle.border.bottom}px`}
            borderLeftWidth={`${cardStyle.border.left}px`}
            borderRightWidth={`${cardStyle.border.right}px`}
            borderColor={cardStyle.border.color}
          >
            <Skeleton>
              <Box minHeight="180px"></Box>
            </Skeleton>
            <Skeleton height="20px" mt={4} />
            <SkeletonText mt="4" noOfLines={4} spacing="5" />
          </Box>
        </Box>
      ));

    return skeleton;
  };

  return (
    <Box
      display="flex"
      flexWrap="wrap"
      mx={`-${Math.round(style.columnGap / 2)}px`}
      mt={`${style.margin.top}px`}
      mb={`${style.margin.bottom}px`}
    >
      {loading ? (
        <>{getSkeleton(style.numberOfColumn * 1)}</>
      ) : (
        products.map((product) => (
          <Box
            flex="0 0"
            flexBasis="auto"
            width={['100%', '50%', '50%', `${100 / style.numberOfColumn}%`]}
            mb={`${style.rowGap}px`}
            px={[
              0,
              `${style.columnGap}px`,
              `${style.columnGap}px`,
              `${style.columnGap}px`,
            ]}
            key={product.uuid}
          >
            <Box
              height="100%"
              bg={cardStyle.bgColor}
              position="relative"
              shadow={cardStyle.boxShadow}
              rounded={`
                                ${cardStyle.rounded.topLeft}px 
                                ${cardStyle.rounded.topRight}px 
                                ${cardStyle.rounded.bottomRight}px 
                                ${cardStyle.rounded.bottomLeft}px
                            `}
              pt={`${cardStyle.padding.top}px`}
              pb={`${cardStyle.padding.bottom}px`}
              pl={`${cardStyle.padding.left}px`}
              pr={`${cardStyle.padding.right}px`}
              borderTopWidth={`${cardStyle.border.top}px`}
              borderBottomWidth={`${cardStyle.border.bottom}px`}
              borderLeftWidth={`${cardStyle.border.left}px`}
              borderRightWidth={`${cardStyle.border.right}px`}
              borderColor={cardStyle.border.color}
            >
              <Box
                mt={`${imageStyle.margin.top}px`}
                mb={`${imageStyle.margin.bottom}px`}
                ml={`${imageStyle.margin.left}px`}
                mr={`${imageStyle.margin.right}px`}
                pt={`${imageStyle.padding.top}px`}
                pb={`${imageStyle.padding.bottom}px`}
                pl={`${imageStyle.padding.left}px`}
                pr={`${imageStyle.padding.right}px`}
                borderTopWidth={`${imageStyle.border.top}px`}
                borderBottomWidth={`${imageStyle.border.bottom}px`}
                borderLeftWidth={`${imageStyle.border.left}px`}
                borderRightWidth={`${imageStyle.border.right}px`}
                rounded={`
                                    ${imageStyle.rounded.topLeft}px 
                                    ${imageStyle.rounded.topRight}px 
                                    ${imageStyle.rounded.bottomRight}px 
                                    ${imageStyle.rounded.bottomLeft}px
                                `}
              >
                <Link
                  href={
                    product.type === 1
                      ? `/rentmy/product/${product.uuid}`
                      : `/rentmy/package/${product.uuid}`
                  }
                >
                  {/* <Image
                    src={getImage(product)}
                    alt={product.name}
                    width="100%"
                    cursor="pointer"
                    alt='product image'
                    objectFit="cover"
                    rounded={`
                                            ${imageStyle.rounded.topLeft}px 
                                            ${imageStyle.rounded.topRight}px 
                                            ${imageStyle.rounded.bottomRight}px 
                                            ${imageStyle.rounded.bottomLeft}px
                                        `}
                    maxHeight="400px"
                    height={`${imageStyle.height}px`}
                  /> */}

                  <div className="w-full relative">
                    <Image
                      src={getImage(product)}
                      alt={product.name}
                      priority
                      objectFit="cover"
                      layout="responsive"
                      width="100%"
                      height="100%"
                    />
                  </div>
                </Link>
              </Box>

              <Box position="static" textAlign={cardStyle.textAlign}>
                <Text
                  mt={`${title.margin.top}px`}
                  mb={`${title.margin.bottom}px`}
                  ml={`${title.margin.left}px`}
                  mr={`${title.margin.right}px`}
                  pt={`${title.padding.top}px`}
                  pb={`${title.padding.bottom}px`}
                  pl={`${title.padding.left}px`}
                  pr={`${title.padding.right}px`}
                  color={title.text.color}
                  fontFamily={title.text?.fontFamily}
                  fontSize={`${title.text.size}px`}
                  fontWeight={title.text.weight}
                  textTransform={title.text.transform}
                  lineHeight={
                    title.text?.lineHeight ? title.text?.lineHeight : 'normal'
                  }
                  letterSpacing={
                    title.text?.letterSpacing
                      ? `${title.text?.letterSpacing}px`
                      : 'normal'
                  }
                  fontStyle={
                    title.text?.fontStyle
                      ? `${title.text?.fontStyle}`
                      : 'normal'
                  }
                  textDecoration={
                    title.text?.textDecoration
                      ? `${title.text?.textDecoration}`
                      : 'none'
                  }
                >
                  <Link
                    href={
                      product.type === 1
                        ? `/rentmy/product/${product.uuid}`
                        : `/rentmy/package/${product.uuid}`
                    }
                  >
                    {truncate(product.name, {
                      length: 50,
                      separator: /,? +/,
                    })}
                  </Link>
                </Text>
                <Text
                  mt={`${excerpt.margin.top}px`}
                  mb={`${excerpt.margin.bottom}px`}
                  ml={`${excerpt.margin.left}px`}
                  mr={`${excerpt.margin.right}px`}
                  pt={`${excerpt.padding.top}px`}
                  pb={`${excerpt.padding.bottom}px`}
                  pl={`${excerpt.padding.left}px`}
                  pr={`${excerpt.padding.right}px`}
                  color={excerpt.text.color}
                  fontFamily={excerpt.text?.fontFamily}
                  fontSize={`${excerpt.text.size}px`}
                  fontWeight={excerpt.text.weight}
                  textTransform={excerpt.text.transform}
                  lineHeight={
                    excerpt.text.lineHeight ? excerpt.text.lineHeight : 'normal'
                  }
                  letterSpacing={
                    excerpt.text.letterSpacing
                      ? `${excerpt.text.letterSpacing}px`
                      : 'normal'
                  }
                  fontStyle={
                    excerpt.text?.fontStyle
                      ? `${excerpt.text?.fontStyle}`
                      : 'normal'
                  }
                  textDecoration={
                    excerpt.text?.textDecoration
                      ? `${excerpt.text?.textDecoration}`
                      : 'none'
                  }
                >
                  {getDescription(product.prices)}
                </Text>
              </Box>
            </Box>
          </Box>
        ))
      )}
    </Box>
  );
}

export default RentMyProduct;
