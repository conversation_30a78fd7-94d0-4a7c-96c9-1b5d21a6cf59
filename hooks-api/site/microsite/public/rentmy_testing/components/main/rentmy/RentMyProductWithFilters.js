import { Box, Button, Flex, Image, Select, Skeleton, SkeletonText, Spinner, Text, useMediaQuery } from '@chakra-ui/react';
import axios from 'axios';
import truncate from 'lodash/truncate';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';

const RentMyCategories = dynamic(() => import('./RentMyCategories'), {
  ssr: false,
});
const RentMyFilters = dynamic(() => import('./RentMyFilters'), {
  ssr: false,
});
const RentMySearch = dynamic(() => import('./RentMySearch'), {
  ssr: false,
});
const RentMyPagination = dynamic(() => import('./RentMyPagination'), {
  ssr: false,
});

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyProductWithFilters({ component }) {
  // const settings = useSelector(globalSettingData);
  const settings = {
    "settingsId": null,
    "showSignIn": true,
    "primaryColor": "#fb0303",
    "secondaryColor": "rgba(126,211,33,1)",
    "accentColor": "rgba(65,117,5,1)",
    "logo": {
      "_id": "651a4f6597fee0e6e785085b",
      "altText": "",
      "filePath": "i8s2uzjm/image/tea-49982469-of02u7w7-removebg-preview-24684430.webp"
    },
    "favIcon": {
      "_id": "651a4f5797fee0e6e785083e",
      "altText": "",
      "filePath": "i8s2uzjm/image/finger.webp"
    },
    "brand": "Jahid brand",
    "siteFont": "ABeeZee",
    "advanceSeoSettings": {
      "requestAudit": {
        "isRequested": false,
        "auditLogs": []
      },
      "progress": 0,
      "overallProgress": "0.00",
      "homepageProgress": "0.00",
      "incompleteMeta": "0.00",
      "isRobotTxtEnable": true,
      "focusKeywords": [],
      "isAllowIndexing": true,
      "robotsTxt": "Robot.Txt 12345"
    },
    "siteName": "",
    "seo": {
      "adsense": {
        "_id": "636e2a7fb1804d5a06f5e2ac",
        "active": true,
        "content": "ca-pub-9775757577653408"
      },
      "searchConsole": {
        "_id": "6371c3afb1804d5a06f781b7",
        "active": false,
        "content": "aA2M-HM29FrEbNU913HLd1QCC4TbV9bZFCcCUkGOYxg"
      },
      "pinterest": {
        "_id": "63b56ba2b1804d5a0614ec37",
        "active": true,
        "content": "2f758c3c494a6a15b347f5bfb36363d6"
      },
      "facebookPixel": {
        "_id": "651fdbbcc6dc7baeae7906d5",
        "active": true,
        "content": "1486704542175999"
      },
      "activeBlogPage": {
        "active": false,
        "_id": "6528d3f3b7f1a6cdde649fa2",
        "content": {
          "name": "blog101",
          "generation": "1st",
          "section": {
            "ids": [
              "b29aa84f-a797-4148-8dc6-88bd8f18abdf"
            ],
            "entries": {
              "b29aa84f-a797-4148-8dc6-88bd8f18abdf": {
                "id": "b29aa84f-a797-4148-8dc6-88bd8f18abdf",
                "type": "uncategorized",
                "style": {
                  "padding": {
                    "top": {
                      "base": 50,
                      "md": 50,
                      "lg": 100
                    },
                    "right": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    },
                    "bottom": {
                      "base": 50,
                      "md": 50,
                      "lg": 50
                    },
                    "left": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    }
                  },
                  "margin": {
                    "top": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    },
                    "right": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    },
                    "bottom": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    },
                    "left": {
                      "base": 0,
                      "md": 0,
                      "lg": 0
                    }
                  },
                  "isGradient": false,
                  "isGradientAnimated": false,
                  "bgColor": "transparent",
                  "gradientDirection": "to right",
                  "gradientColorOne": "#37d5d6",
                  "gradientColorTwo": "#36096d",
                  "gradientColorThree": "",
                  "bgBlendMode": "normal",
                  "isTintActive": false,
                  "bgImage": {
                    "path": "",
                    "altText": "",
                    "id": ""
                  },
                  "border": {
                    "top": 0,
                    "right": 0,
                    "bottom": 0,
                    "left": 0,
                    "color": "#3F3047",
                    "style": "solid"
                  },
                  "rounded": {
                    "topLeft": 0,
                    "topRight": 4,
                    "bottomLeft": 4,
                    "bottomRight": 4
                  }
                },
                "fullWidth": false,
                "columnGap": 10,
                "children": [
                  "583e9ea9-b20f-43a3-9ba8-4af7a0b66057"
                ]
              }
            }
          },
          "column": {
            "583e9ea9-b20f-43a3-9ba8-4af7a0b66057": {
              "id": "583e9ea9-b20f-43a3-9ba8-4af7a0b66057",
              "style": {
                "width": {
                  "base": 100
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "rounded": {
                  "topLeft": 0,
                  "topRight": 0,
                  "bottomLeft": 0,
                  "bottomRight": 0
                },
                "boxShadow": "none",
                "bgColor": "#ffffff",
                "hover": {
                  "backgroundColor": "transparent",
                  "color": "#212121",
                  "transformValue": "none",
                  "transform": {
                    "scaleX": 1,
                    "scaleY": 1,
                    "skewX": 0,
                    "skewY": 0,
                    "rotateX": 0,
                    "rotateY": 0,
                    "rotateZ": 0,
                    "translateX": 0,
                    "translateY": 0
                  }
                },
                "animation": {
                  "animation": false,
                  "value": "fadeIn",
                  "onScroll": false,
                  "isRepeat": false,
                  "transition": {
                    "type": "spring",
                    "duration": 0.5,
                    "delay": 0,
                    "bounce": 0.4,
                    "repeat": 1,
                    "repeatType": "mirror",
                    "repeatDelay": 0
                  }
                },
                "bgImage": {
                  "path": "",
                  "altText": "",
                  "id": ""
                },
                "border": {
                  "top": 0,
                  "right": 0,
                  "bottom": 0,
                  "left": 0,
                  "color": "#3F3047",
                  "style": "solid"
                }
              },
              "children": [
                "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67"
              ]
            }
          },
          "component": {
            "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67": {
              "id": "dc92f1d0-af22-4d33-8dde-4b3a3c87fc67",
              "type": "blog-grid",
              "cardBgColor": "red",
              "style": {
                "margin": {
                  "top": 0,
                  "right": 0,
                  "bottom": 0,
                  "left": 0
                },
                "numberOfColumn": {
                  "base": 1,
                  "md": 2,
                  "lg": 4
                },
                "columnGap": 10,
                "rowGap": 20,
                "animation": {
                  "animation": false,
                  "value": "fadeIn",
                  "onScroll": false,
                  "isRepeat": false,
                  "transition": {
                    "type": "spring",
                    "duration": 0.5,
                    "delay": 0,
                    "bounce": 0.4,
                    "repeat": 1,
                    "repeatType": "mirror",
                    "repeatDelay": 0
                  }
                },
                "layout": "list"
              },
              "cardStyle": {
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 10
                  },
                  "right": {
                    "base": 10
                  },
                  "bottom": {
                    "base": 10
                  },
                  "left": {
                    "base": 10
                  }
                },
                "border": {
                  "top": 1,
                  "right": 1,
                  "bottom": 1,
                  "left": 1,
                  "color": "gray.300",
                  "style": "solid"
                },
                "rounded": {
                  "topLeft": 8,
                  "topRight": 8,
                  "bottomLeft": 8,
                  "bottomRight": 8
                },
                "bgColor": "#FFFFFF",
                "boxShadow": "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
                "position": "relative",
                "textAlign": "left",
                "showTags": true,
                "showTitle": true,
                "showExcerpt": true,
                "showComments": true,
                "showAuthor": true
              },
              "searchStyle": {
                "showSearchBar": true,
                "justifyContent": {
                  "base": "flex-start"
                },
                "height": "auto",
                "width": "60%",
                "border": {
                  "top": 0,
                  "right": 0,
                  "bottom": 0,
                  "left": 0,
                  "color": "#666666",
                  "style": "solid"
                },
                "rounded": {
                  "topLeft": 4,
                  "topRight": 4,
                  "bottomLeft": 4,
                  "bottomRight": 4
                },
                "bgColor": "#CBD5E0",
                "text": {
                  "size": {
                    "base": "16"
                  },
                  "color": "#212121",
                  "weight": "400",
                  "align": {
                    "base": "left"
                  },
                  "transform": "none",
                  "lineHeight": 1.5,
                  "letterSpacing": 0,
                  "fontFamily": "ABeeZee",
                  "fontStyle": "normal",
                  "textDecoration": "none"
                },
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "searchText": "Search",
                "searchContainerWidth": "40%"
              },
              "paginationStyle": {
                "showPagination": true,
                "paginateView": "button",
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "selectedPageColor": "blue.400",
                "buttonVariant": "outline",
                "color": "blue.400"
              },
              "filterStyle": {
                "showFilter": true,
                "justifyContent": {
                  "base": "flex-end"
                },
                "text": {
                  "size": {
                    "base": "16"
                  },
                  "color": "#212121",
                  "weight": "400",
                  "align": {
                    "base": "left"
                  },
                  "transform": "none",
                  "lineHeight": 1.5,
                  "letterSpacing": 0,
                  "fontFamily": "ABeeZee",
                  "fontStyle": "normal",
                  "textDecoration": "none"
                },
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "filterText": "Filter by tags",
                "filterView": "dropdown",
                "width": "40%",
                "filterContainerWidth": "60%"
              },
              "imageStyle": {
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 10
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "width": "100%",
                "height": "200",
                "border": {
                  "top": 0,
                  "right": 0,
                  "bottom": 0,
                  "left": 0,
                  "color": "gray.300",
                  "style": "none"
                },
                "rounded": {
                  "topLeft": 8,
                  "topRight": 8,
                  "bottomLeft": 0,
                  "bottomRight": 0
                },
                "justifyContent": "flex-start"
              },
              "title": {
                "markup": "h3",
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 5
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "text": {
                  "size": {
                    "base": "15"
                  },
                  "color": "gray.900",
                  "weight": "700",
                  "align": {
                    "base": "left"
                  },
                  "transform": "none",
                  "lineHeight": 1.5,
                  "letterSpacing": 0,
                  "fontFamily": "ABeeZee",
                  "fontStyle": "normal",
                  "textDecoration": "none"
                },
                "limit": 10
              },
              "excerpt": {
                "markup": "p",
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 10
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "text": {
                  "size": {
                    "base": "14"
                  },
                  "color": "gray.700",
                  "weight": "400",
                  "align": {
                    "base": "left"
                  },
                  "transform": "none",
                  "lineHeight": 1.5,
                  "letterSpacing": 0,
                  "fontFamily": "ABeeZee",
                  "fontStyle": "normal",
                  "textDecoration": "none"
                },
                "limit": 20
              },
              "button": {
                "margin": {
                  "top": {
                    "base": 0
                  },
                  "right": {
                    "base": 0
                  },
                  "bottom": {
                    "base": 0
                  },
                  "left": {
                    "base": 0
                  }
                },
                "padding": {
                  "top": {
                    "base": 4
                  },
                  "right": {
                    "base": 10
                  },
                  "bottom": {
                    "base": 4
                  },
                  "left": {
                    "base": 10
                  }
                },
                "height": "auto",
                "width": "auto",
                "border": {
                  "top": 1,
                  "right": 1,
                  "bottom": 1,
                  "left": 1,
                  "color": "gray.700",
                  "style": "solid"
                },
                "rounded": {
                  "topLeft": 0,
                  "topRight": 0,
                  "bottomLeft": 0,
                  "bottomRight": 0
                },
                "bgColor": "transparent",
                "text": {
                  "size": {
                    "base": "12"
                  },
                  "color": "gray.900",
                  "weight": "300",
                  "align": {
                    "base": "left"
                  },
                  "transform": "none",
                  "lineHeight": 1.5,
                  "letterSpacing": 0,
                  "fontFamily": "ABeeZee",
                  "fontStyle": "normal",
                  "textDecoration": "none"
                },
                "justifyContent": "flex-start",
                "visibility": true,
                "data": "Read More"
              },
              "data": {
                "dataSource": null,
                "dataSourceType": "all",
                "limit": 10,
                "filter": {
                  "type": "author",
                  "value": null
                },
                "sort": [
                  {
                    "label": "Date",
                    "value": "createdAt",
                    "type": "desc"
                  }
                ]
              },
              "categoryStyle": {
                "showCategory": true
              }
            }
          },
          "selected": null,
          "selectedType": ""
        },
        "genericSettings": {
          "responsive": {
            "isMobile": true,
            "isTab": false,
            "isWeb": false
          },
          "pageInfo": {
            "meta": {
              "title": "",
              "isIncludeInSitemap": false,
              "isAllowPageIndex": false,
              "isAllowImageIndex": false,
              "isAllowNoFollow": false,
              "maxSnippet": 0,
              "canonicalUrl": "https://b107.pc-staging.com/blog101",
              "pageSeoScore": 14.761904761904761,
              "keywordDensity": 0,
              "pageContentMatchKeywordCount": 0,
              "altTextMatchKeywordCount": 0,
              "internalLinkCount": 0,
              "externalLinkCount": 0,
              "description": null,
              "keywords": null,
              "image": null,
              "focusKeywords": [],
              "ogUrl": "https://b107.pc-staging.com/blog101",
              "metaInfo": {
                "totalMeta": 6,
                "missing": 5,
                "exist": 1
              }
            },
            "renderType": "builder",
            "isHomePage": false,
            "isBlogPage": true,
            "pageName": "blog101",
            "pageTitle": "blog101",
            "slug": "blog101"
          },
          "headerInfo": {
            "headerPosition": "sticky",
            "headerBackground": "#ffffff"
          },
          "_id": "6528d3f3b7f1a6cdde649fa0",
          "__v": 0,
          "adminType": "CA",
          "builderType": "FE_BLOG",
          "createdAt": "2023-10-13T05:21:55.401Z",
          "franchiseeId": "636a25eb5d1fa80f73143ea4",
          "updatedAt": "2024-09-18T04:43:16.583Z",
          "id": "6528d3f3b7f1a6cdde649fa0"
        }
      }
    }
  };

  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCate, setSelectedCate] = useState(null);
  const [tags, setTags] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedType, setSelectedType] = useState('all');
  const [minPrice, setMinPrice] = useState(null);
  const [maxPrice, setMaxPrice] = useState(null);

  const [sortValue, setSortValue] = useState('0');

  const [apiCallStat, setApiCallStat] = useState(null);

  const [pageLoad, setPageLoad] = useState(true);
  const [loading, setLoading] = useState(false);
  const [isResetBtnDisable, setIsResetBtnDisable] = useState(false);
  const [searchText, setSearchText] = useState(null);
  const [sortDir, setSortDir] = useState(null);
  const [sortBy, setSortBy] = useState(null);

  // const { style, cardStyle, imageStyle, title, excerpt, data, filters } = component;


  const component2 =
  {
    "id": "70b24573-5197-4453-85a1-300986d0ef6b",
    "type": "rentMyWithFilters",
    "style": {
      "margin": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0
      },
      "numberOfColumn": 4,
      "columnGap": 10,
      "rowGap": 20,
      "alignProduct": "row",
      "alignSearch": "flex-start"
    },
    "cardStyle": {
      "margin": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0
      },
      "padding": {
        "top": 10,
        "right": 10,
        "bottom": 10,
        "left": 10
      },
      "border": {
        "top": 1,
        "right": 1,
        "bottom": 1,
        "left": 1,
        "color": "gray.300",
        "style": "solid"
      },
      "rounded": {
        "topLeft": 8,
        "topRight": 8,
        "bottomLeft": 8,
        "bottomRight": 8
      },
      "bgColor": "#FFFFFF",
      "boxShadow": "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px",
      "position": "relative",
      "textAlign": "center"
    },
    "imageStyle": {
      "margin": {
        "top": 0,
        "right": 0,
        "bottom": 10,
        "left": 0
      },
      "padding": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0
      },
      "width": "100%",
      "height": "160",
      "border": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0,
        "color": "gray.300",
        "style": "none"
      },
      "rounded": {
        "topLeft": 8,
        "topRight": 8,
        "bottomLeft": 0,
        "bottomRight": 0
      },
      "justifyContent": "flex-start"
    },
    "title": {
      "markup": "h3",
      "margin": {
        "top": 0,
        "right": 0,
        "bottom": 5,
        "left": 0
      },
      "padding": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0
      },
      "text": {
        "size": "15",
        "color": "gray.900",
        "weight": "700",
        "align": "left",
        "transform": "none",
        "lineHeight": 1.5,
        "letterSpacing": 0,
        "fontFamily": "ABeeZee",
        "fontStyle": "normal",
        "textDecoration": "none"
      },
      "limit": 10
    },
    "excerpt": {
      "markup": "p",
      "margin": {
        "top": 0,
        "right": 0,
        "bottom": 10,
        "left": 0
      },
      "padding": {
        "top": 0,
        "right": 0,
        "bottom": 0,
        "left": 0
      },
      "text": {
        "size": "14",
        "color": "gray.700",
        "weight": "400",
        "align": "left",
        "transform": "none",
        "lineHeight": 1.5,
        "letterSpacing": 0,
        "fontFamily": "ABeeZee",
        "fontStyle": "normal",
        "textDecoration": "none"
      },
      "limit": 20
    },
    "data": {
      "dataSource": null,
      "locationDataSource": null,
      "dataSourceType": "all",
      "limit": "100",
      "sort": [
        {
          "label": "Date",
          "value": "createdAt",
          "type": "desc"
        }
      ]
    },
    "filters": {
      "search": {
        "show": true,
        "style": {}
      },
      "categories": {
        "show": true,
        "style": {}
      },
      "filter": {
        "show": true,
        "style": {}
      },
      "priceRange": {
        "show": true,
        "style": {}
      },
      "tags": {
        "show": true,
        "style": {}
      },
      "types": {
        "show": true,
        "style": {}
      },
      "sorting": {
        "show": true,
        "style": {}
      },
      "pagination": {
        "show": true,
        "type": "text",
        "style": {}
      }
    }
  }
  const { style, cardStyle, imageStyle, title, excerpt, data, filters } = component2;
  const apiUrl = useRef(null);
  const payload = useRef({});
  // const { id } = useSelector(subscriberData);

  const [page, setPage] = useState(1);
  const [pageCount, setPageCount] = useState(1);

  const [isMobile] = useMediaQuery('(max-width: 767px)');
  const [isTab] = useMediaQuery('(min-width: 768px)');
  const [isWeb] = useMediaQuery('(min-width: 992px)');

  //   console.log(data);
  switch (data.dataSourceType) {
    case 'category':
      payload.current = {
        limit: +data.limit,
        page_no: +page,
      };
      if (data.dataSource) {
        apiUrl.current = `/category/products/${data.dataSource.value}`;
      } else {
        apiUrl.current = null;
      }
      break;
    case 'all':
      apiUrl.current = '/products/online'
      payload.current = {
        limit: +data.limit,
        page_no: +page,
      };
    default:
      break;
  }

  const fetchData = async (apiURL = null, customPayload = null) => {
    setLoading(true);
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    // const tokenObject = await getRentMyToken(id);
    const tokenObject = {
      "success": true,
      "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0wNyAwNToyMTowOCIsInN0b3JlX2lkIjozNTEsImxvY2F0aW9uIjozOTEsImFwcF9pZCI6Mzc4LCJleHBpcmUiOiIyMDI0LTA4LTA4IDA1OjIxOjAwIiwiaXNfb25saW5lIjowLCJzb3VyY2UiOiJhcGkiLCJkaXNhYmxlX2RlZmF1bHRfdGltZSI6ZmFsc2V9.Nv9_qboMtedRRtEgP7eFl8oMxOVSDPlv9V4rcocZceg",
      "locationId": "391",
      "storeId": "351"
    }
    if (tokenObject.success) {
      axios
        .request({
          url: apiURL || apiUrl.current,
          method: 'POST',
          baseURL: apiBaseURL,
          data: customPayload || payload.current,
          headers: {
            Authorization: `Bearer ${tokenObject.token}`,
            location: data?.locationDataSource?.value || tokenObject.locationId,
          },
        })
        .then((response) => {
          const { result } = response.data;
          // setProducts(result.data);

          if (result.data) {
            setProducts(result?.data);
            setLoading(false);
            setPageLoad(false)
            setPageCount(Math.ceil(+result?.total / +data.limit))
          }
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          setLoading(false)
          setPageLoad(false)
        });
    } else {
      setLoading(false);
      setPageLoad(false)
    }
  };


  const fetchCategories = async () => {
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    // const tokenObject = await getRentMyToken(id);
    const tokenObject = {
      "success": true,
      "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0wNyAwNToyMTowOCIsInN0b3JlX2lkIjozNTEsImxvY2F0aW9uIjozOTEsImFwcF9pZCI6Mzc4LCJleHBpcmUiOiIyMDI0LTA4LTA4IDA1OjIxOjAwIiwiaXNfb25saW5lIjowLCJzb3VyY2UiOiJhcGkiLCJkaXNhYmxlX2RlZmF1bHRfdGltZSI6ZmFsc2V9.Nv9_qboMtedRRtEgP7eFl8oMxOVSDPlv9V4rcocZceg",
      "locationId": "391",
      "storeId": "351"
    }
    if (tokenObject.success) {
      axios.request({
        url: 'categories',
        method: 'GET',
        baseURL: apiBaseURL,
        headers: {
          'Authorization': `Bearer ${tokenObject.token}`,
        },
      }).then(response => {
        const { result } = response.data;
        // console.log("result", result);
        if (result.hasOwnProperty('data')) {
          setCategories(result.data);
        }

      }).catch(error => console.log(error))

    }
  }


  const fetchTags = async () => {
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    // const tokenObject = await getRentMyToken(id);
    const tokenObject = {
      "success": true,
      "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0wNyAwNToyMTowOCIsInN0b3JlX2lkIjozNTEsImxvY2F0aW9uIjozOTEsImFwcF9pZCI6Mzc4LCJleHBpcmUiOiIyMDI0LTA4LTA4IDA1OjIxOjAwIiwiaXNfb25saW5lIjowLCJzb3VyY2UiOiJhcGkiLCJkaXNhYmxlX2RlZmF1bHRfdGltZSI6ZmFsc2V9.Nv9_qboMtedRRtEgP7eFl8oMxOVSDPlv9V4rcocZceg",
      "locationId": "391",
      "storeId": "351"
    }
    if (tokenObject.success) {
      axios.request({
        url: 'tags',
        method: 'GET',
        baseURL: apiBaseURL,
        headers: {
          'Authorization': `Bearer ${tokenObject.token}`,
        },
      }).then(response => {
        const { result } = response.data;
        // console.log("result", result);
        if (result.hasOwnProperty('data')) {
          setTags(result.data);
        }

      }).catch(error => console.log(error))

    }
  }


  const getImage = (product) => {
    const rentMyStoreId = localStorage.getItem('rentMystrId');
    const src = product.images.length
      ? mediaUrl +
      '/products/' +
      rentMyStoreId +
      '/' +
      product.id +
      '/' +
      product.images[0]?.image_small
      : '/static/media/product-image-placeholder.jpg';
    return src;
  };

  // Accurate round price number
  const get2DecPoint = (number = 0.0) => {
    return number.toLocaleString('en-US', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  };

  const descriptionText = (pricing, key, tailText) => {
    const priceDescription =
      pricing[key][0].duration > 1
        ? `Starting at $${get2DecPoint(pricing[key][0].price)} USD for ${pricing[key][0].duration
        } ${tailText}s`
        : `Starting at $${get2DecPoint(
          pricing[key][0].price
        )} USD per ${tailText}`;
    return priceDescription;
  };

  const getDescription = (prices) => {
    let pricing = prices[0];
    if (pricing.hasOwnProperty('hourly')) {
      const priceDescription = descriptionText(pricing, 'hourly', 'hour');
      return priceDescription;
    } else if (pricing.hasOwnProperty('daily')) {
      const priceDescription = descriptionText(pricing, 'daily', 'day');
      return priceDescription;
    } else if (pricing.hasOwnProperty('weekly')) {
      const priceDescription = descriptionText(pricing, 'weekly', 'week');
      return priceDescription;
    } else if (pricing.hasOwnProperty('monthly')) {
      const priceDescription = descriptionText(pricing, 'monthly', 'month');
      return priceDescription;
    } else if (pricing.hasOwnProperty('yearly')) {
      const priceDescription = descriptionText(pricing, 'yearly', 'year');
      return priceDescription;
    } else {
      return `Buy now for $${pricing.base.price} USD`;
    }
  };

  useEffect(() => {
    fetchCategories();
    fetchTags();

    fetchData();
  }, []);

  // useEffect(() => {
  //   fetchData();
  // }, []);
  // }, [data]);


  // with search
  useEffect(() => {
    if (searchText) {
      setPage(1)

      const timeoutId = setTimeout(() => {
        fetchBySearch()

        if (page != 1) {
          setPage(1);
        }
      }, 500);
      return () => clearTimeout(timeoutId);

    }
  }, [searchText, 500]);


  // without search
  useEffect(() => {
    if (apiCallStat) {
      setSearchText('')

      let customPayload = {
        ...payload.current,
        all: true,
        sort_by: sortBy || '',
        sort_dir: sortDir || '',
        price_min: +minPrice,
        price_max: +maxPrice,
        tag_id: selectedTags?.toString(),
      }

      if (selectedType != 'all') {
        customPayload.purchase_type = selectedType
      }

      if (selectedCate) { // If category selected
        if (apiCallStat == 'reset_category') { // category reset
          fetchCategories()
          onResetCategories()
          setApiCallStat('reset_filter')
        } else {
          if (apiCallStat == 'reset_filter') { // category with filter reset
            onResetFilters()
            fetchData(`/category/products/${selectedCate}`);
          } else {
            fetchData(`/category/products/${selectedCate}`, customPayload);
          }
        }

      } else { // If only filters selected
        if (apiCallStat == 'reset_filter') { // filter reset
          onResetFilters()
          fetchData()
        } else {
          fetchData(null, customPayload);
        }

      }

    }
  }, [apiCallStat]);



  const getSkeleton = (item) => {
    const skeleton = Array(item)
      .fill()
      .map((_, i) => (
        <Box
          key={i}
          flex="0 0"
          flexBasis="auto"
          width={['100%', '50%', '50%', `${100 / style.numberOfColumn}%`]}
          mb={`${style.rowGap}px`}
          px={[
            0,
            `${style.columnGap}px`,
            `${style.columnGap}px`,
            `${style.columnGap}px`,
          ]}
        >
          <Box
            rounded={`
                        ${cardStyle.rounded.topLeft}px 
                        ${cardStyle.rounded.topRight}px 
                        ${cardStyle.rounded.bottomRight}px 
                        ${cardStyle.rounded.bottomLeft}px
                    `}
            pt={`${cardStyle.padding.top}px`}
            pb={`${cardStyle.padding.bottom}px`}
            pl={`${cardStyle.padding.left}px`}
            pr={`${cardStyle.padding.right}px`}
            borderTopWidth={`${cardStyle.border.top}px`}
            borderBottomWidth={`${cardStyle.border.bottom}px`}
            borderLeftWidth={`${cardStyle.border.left}px`}
            borderRightWidth={`${cardStyle.border.right}px`}
            borderColor={cardStyle.border.color}
          >
            <Skeleton>
              <Box minHeight="180px"></Box>
            </Skeleton>
            <Skeleton height="20px" mt={4} />
            <SkeletonText mt="4" noOfLines={4} spacing="5" />
          </Box>
        </Box>
      ));

    return skeleton;
  };



  const fetchBySearch = () => {
    if (searchText?.trim() != '' && searchText?.trim().length > 0) {
      onResetFilters()
      onResetCategories()

      let customPayload = {
        ...payload.current,
        search: searchText.trim(),
        // category_id: selectedCate || '',
      }

      fetchData(`/search/products`, customPayload);
    } else {
      onResetCategories()
      onResetFilters()

      fetchData()
    }
  }


  const onResetAll = () => {
    setSearchText('')
    onResetCategories(null)
    onResetFilters(null)

    fetchCategories()
    fetchData()
  }

  const onResetCategories = () => {
    setSelectedCate(null)
    // fetchData()
  }

  const onResetFilters = () => {
    setSortValue('0')
    setSortBy(null)
    setSortDir(null)
    setMaxPrice(null)
    setMinPrice(null)
    setSelectedTags([])
    setSelectedType('all')

    // fetchData()
  }


  return (
    <Box
      display="flex"
      flexWrap="wrap"
      mx={`-${Math.round(style.columnGap / 2)}px`}
      mt={`${style.margin.top}px`}
      mb={`${style.margin.bottom}px`}
    >
      {/* {loading ? ( */}
      {pageLoad ? (
        <>{getSkeleton(style.numberOfColumn * 1)}</>
      ) : (
        <>
          <Flex
            w='100%'
            mb={4}
            flexDirection={isMobile ? 'column' : 'row'}
            justifyContent={isMobile ? 'center' : 'space-between'}
            gap={2}
          // px={1}
          >
            <Flex
              w={isMobile ? '100%' : filters?.sorting?.show ? '75%' : '100%'}
              justifyContent={isMobile ? 'center' : style?.alignSearch}
            >
              {filters?.search?.show &&
                <RentMySearch
                  searchText={searchText}
                  setSearchText={setSearchText}
                  page={page}
                  setPage={setPage}
                  filters={filters}
                  setApiCallStat={setApiCallStat}
                />
              }
            </Flex>

            {filters?.sorting?.show &&
              <Flex
                w={isMobile ? '100%' : '25%'}
              // minHeight='70px'
              // background='gray.200'
              // alignItems='center'
              // fontSize={25}
              // fontWeight='500'
              // borderRadius={8}
              // px={3}
              >
                {/* Sorting */}

                <Select
                  variant="outline"
                  cursor={'pointer'}
                  size="md"
                  borderRadius={6}
                  value={sortValue}

                  onChange={e => {
                    setSortValue(e.target.value)

                    if (e.target.value != '0') {

                      if (e.target.value == '1' || e.target.value == '3' || e.target.value == '5') {
                        setSortDir("ASC")
                      } else {
                        setSortDir("DSC")
                      }

                      if (e.target.value == '1' || e.target.value == '2') {
                        setSortBy("product_name")
                      }
                      if (e.target.value == '3' || e.target.value == '4') {
                        setSortBy("rent_price")
                      }
                      if (e.target.value == '5' || e.target.value == '6') {
                        setSortBy("buy_price")
                      }

                      setApiCallStat('sorting ' + e.target.value)
                    } else {
                      // console.log("object", e.target.value);
                      setSortDir(null)
                      setSortBy(null)

                      setApiCallStat('sorting ' + e.target.value)
                    }

                  }}
                >
                  <option value="0">Select to sort</option>
                  <option value="1">Product name A-Z</option>
                  <option value="2">Product name Z-A</option>
                  <option value="3">Rental price low to high</option>
                  <option value="4">Rental price high to low</option>
                  <option value="5">Sale price low to high</option>
                  <option value="6">Sale price high to low</option>
                </Select>
              </Flex>
            }

          </Flex>

          {/* <Text
            pl={2}
            mb={3}
            cursor={'pointer'}
            color={settings?.accentColor}
            onClick={() => onResetAll()}
          >
            Reset all
          </Text> */}

          <Flex
            w='100%'
            flexDirection={isMobile ? 'column' : style?.alignProduct ? style?.alignProduct : 'row'}
            gap={2}
            rowGap={6}
          >
            <Box w={isMobile ? '100%' : '25%'}>
              {filters?.categories?.show && data?.dataSourceType == 'all' &&
                <RentMyCategories
                  categories={categories}
                  setCategories={setCategories}
                  data={data}
                  setSelectedCate={setSelectedCate}
                  selectedCate={selectedCate}
                  onResetCategories={onResetCategories}
                  fetchCategories={fetchCategories}
                  apiCallStat={apiCallStat}
                  setApiCallStat={setApiCallStat}
                  setPage={setPage}
                />
              }

              {filters?.filter?.show && (filters?.priceRange?.show || filters?.tags?.show || filters?.types?.show) &&
                <RentMyFilters
                  apiCallStat={apiCallStat}
                  setApiCallStat={setApiCallStat}
                  filters={filters}
                  tags={tags}
                  setSelectedTags={setSelectedTags}
                  selectedTags={selectedTags}
                  setSelectedType={setSelectedType}
                  selectedType={selectedType}
                  minPrice={minPrice}
                  setMinPrice={setMinPrice}
                  maxPrice={maxPrice}
                  setMaxPrice={setMaxPrice}
                  fetchData={fetchData}
                  onResetFilters={onResetFilters}
                  setPage={setPage}
                />
              }

              {/* Reset Btn */}
              <Button
                mt={5}
                fontSize={'15px'}
                fontWeight={700}
                color={'white'}
                size="sm"
                textDecoration={'none'}
                // isDisabled={isResetBtnDisable}
                bg={settings?.accentColor || 'green.500'}
                _hover={{
                  bg: settings?.secondaryColor || 'green.500',
                }}
                onClick={() => {
                  // setApiCallStat('reset_all')
                  setPage(1)
                  onResetAll()
                }}
              >
                Reset
              </Button>

            </Box>


            <Flex
              w={isMobile ? '100%' : !(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') ? '100%' : '75%'}
              flexWrap={'wrap'}
              justifyContent={isMobile ? 'center' : 'flex-start'}
              h={'fit-content'}
            >
              {loading ?
                <Flex
                  w={'100%'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  h={'50vh'}
                >
                  <Spinner
                    thickness='4px'
                    speed='0.65s'
                    emptyColor='gray.200'
                    color='blue.500'
                    size='xl'
                  />
                </Flex>
                :
                <>
                  {products?.length < 1 &&
                    <Flex
                      w={'100%'}
                      justifyContent={'center'}
                      alignItems={'center'}
                      h={'50vh'}
                    >
                      <Text fontSize={'1.5rem'}>No data found</Text>
                    </Flex>
                  }

                  {
                    products?.length > 0 && products.map((product) => (
                      <Box
                        flex="0 0"
                        flexBasis="auto"
                        minW={isMobile ? '100%' : '220px'}
                        h={'290px'}
                        width={['100%', '50%', '50%', `${100 / style.numberOfColumn}%`]}
                        mb={`${style.rowGap}px`}
                        px={[
                          0,
                          `${style.columnGap}px`,
                          `${style.columnGap}px`,
                          `${style.columnGap}px`,
                        ]}
                        key={product.uuid}
                      >
                        <Box
                          height="100%"
                          bg={cardStyle.bgColor}
                          position="relative"
                          shadow={cardStyle.boxShadow}
                          rounded={`
                                ${cardStyle.rounded.topLeft}px 
                                ${cardStyle.rounded.topRight}px 
                                ${cardStyle.rounded.bottomRight}px 
                                ${cardStyle.rounded.bottomLeft}px
                            `}
                          pt={`${cardStyle.padding.top}px`}
                          pb={`${cardStyle.padding.bottom}px`}
                          pl={`${cardStyle.padding.left}px`}
                          pr={`${cardStyle.padding.right}px`}
                          borderTopWidth={`${cardStyle.border.top}px`}
                          borderBottomWidth={`${cardStyle.border.bottom}px`}
                          borderLeftWidth={`${cardStyle.border.left}px`}
                          borderRightWidth={`${cardStyle.border.right}px`}
                          borderColor={cardStyle.border.color}
                        >
                          <Box
                            mt={`${imageStyle.margin.top}px`}
                            mb={`${imageStyle.margin.bottom}px`}
                            ml={`${imageStyle.margin.left}px`}
                            mr={`${imageStyle.margin.right}px`}
                            pt={`${imageStyle.padding.top}px`}
                            pb={`${imageStyle.padding.bottom}px`}
                            pl={`${imageStyle.padding.left}px`}
                            pr={`${imageStyle.padding.right}px`}
                            borderTopWidth={`${imageStyle.border.top}px`}
                            borderBottomWidth={`${imageStyle.border.bottom}px`}
                            borderLeftWidth={`${imageStyle.border.left}px`}
                            borderRightWidth={`${imageStyle.border.right}px`}
                            rounded={`
                                    ${imageStyle.rounded.topLeft}px 
                                    ${imageStyle.rounded.topRight}px 
                                    ${imageStyle.rounded.bottomRight}px 
                                    ${imageStyle.rounded.bottomLeft}px
                                `}
                          >
                            <Link
                              href={
                                product.type === 1
                                  ? `/rentmy/product/${product.uuid}`
                                  : `/rentmy/package/${product.uuid}`
                              }
                            >
                              <Image
                                src={getImage(product)}
                                priority
                                alt='product image'
                                width="100%"
                                cursor="pointer"
                                objectFit="cover"
                                rounded={`
                                            ${imageStyle.rounded.topLeft}px 
                                            ${imageStyle.rounded.topRight}px 
                                            ${imageStyle.rounded.bottomRight}px 
                                            ${imageStyle.rounded.bottomLeft}px
                                        `}
                                maxHeight="400px"
                                height={`${imageStyle.height}px`}
                              />
                            </Link>
                          </Box>

                          <Box position="static" textAlign={cardStyle.textAlign}>
                            <Text
                              mt={`${title.margin.top}px`}
                              mb={`${title.margin.bottom}px`}
                              ml={`${title.margin.left}px`}
                              mr={`${title.margin.right}px`}
                              pt={`${title.padding.top}px`}
                              pb={`${title.padding.bottom}px`}
                              pl={`${title.padding.left}px`}
                              pr={`${title.padding.right}px`}
                              color={title.text.color}
                              fontFamily={title.text?.fontFamily}
                              fontSize={`${title.text.size}px`}
                              fontWeight={title.text.weight}
                              textTransform={title.text.transform}
                              lineHeight={
                                title.text?.lineHeight ? title.text?.lineHeight : 'normal'
                              }
                              letterSpacing={
                                title.text?.letterSpacing
                                  ? `${title.text?.letterSpacing}px`
                                  : 'normal'
                              }
                              fontStyle={title.text?.fontStyle ? `${title.text?.fontStyle}` : 'normal'}
                              textDecoration={title.text?.textDecoration ? `${title.text?.textDecoration}` : 'none'}
                            >
                              <Link
                                href={
                                  product.type === 1
                                    ? `/rentmy/product/${product.uuid}`
                                    : `/rentmy/package/${product.uuid}`
                                }
                              >
                                {truncate(product.name, {
                                  length: 50,
                                  separator: /,? +/,
                                })}
                              </Link>
                            </Text>
                            <Text
                              mt={`${excerpt.margin.top}px`}
                              mb={`${excerpt.margin.bottom}px`}
                              ml={`${excerpt.margin.left}px`}
                              mr={`${excerpt.margin.right}px`}
                              pt={`${excerpt.padding.top}px`}
                              pb={`${excerpt.padding.bottom}px`}
                              pl={`${excerpt.padding.left}px`}
                              pr={`${excerpt.padding.right}px`}
                              color={excerpt.text.color}
                              fontFamily={excerpt.text?.fontFamily}
                              fontSize={`${excerpt.text.size}px`}
                              fontWeight={excerpt.text.weight}
                              textTransform={excerpt.text.transform}
                              lineHeight={
                                excerpt.text.lineHeight ? excerpt.text.lineHeight : 'normal'
                              }
                              letterSpacing={
                                excerpt.text.letterSpacing
                                  ? `${excerpt.text.letterSpacing}px`
                                  : 'normal'
                              }
                              fontStyle={excerpt.text?.fontStyle ? `${excerpt.text?.fontStyle}` : 'normal'}
                              textDecoration={excerpt.text?.textDecoration ? `${excerpt.text?.textDecoration}` : 'none'}
                            >
                              {/* {getDescription(product.prices)} */}
                              {getDescription(product.prices)?.length > 40 ? getDescription(product.prices).substring(0, 40) + '...' : getDescription(product.prices)}
                            </Text>
                          </Box>
                        </Box>
                      </Box>
                    ))
                  }

                  {!(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') && !loading && (searchText == null || searchText?.trim() == '') &&
                    <RentMyPagination
                      component={component}
                      page={page}
                      setPage={setPage}
                      limit={data.limit}
                      totalPages={pageCount}
                      isPreviousData={false}
                      isFetching={loading}
                      apiCallStat={apiCallStat}
                      setApiCallStat={setApiCallStat}
                    />
                  }
                </>
              }
            </Flex>

          </Flex>

          {(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') && !loading && (searchText == null || searchText?.trim() == '') &&
            <RentMyPagination
              component={component}
              page={page}
              setPage={setPage}
              limit={data.limit}
              totalPages={pageCount}
              isPreviousData={false}
              isFetching={loading}
              apiCallStat={apiCallStat}
              setApiCallStat={setApiCallStat}
            />
          }

        </>
      )}
    </Box>
  );
}

export default RentMyProductWithFilters;
