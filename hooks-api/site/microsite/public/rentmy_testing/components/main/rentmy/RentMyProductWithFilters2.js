import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
    Box, Image, Text, Link,
    Skeleton, SkeletonText, Flex
} from '@chakra-ui/react'
import DomainUtils from '../../../service/utils/DomainUtils'
import { config } from '../../../config'
import axios from 'axios';
import { truncate } from 'lodash';
import { getRentMyToken } from '../../../service/rentmyAuth'
import imagePlaceholder from './product-image-placeholder.jpg';
import { useSelector } from 'react-redux';
import { RentMyPagination } from './RentMyPagination';

const mediaUrl = "https://s3.us-east-2.amazonaws.com/images.rentmy.co";

function RentMyProductWithFilters({ component }) {
    const viewport = useSelector((state) => state.builder.breakPointMode);

    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(false);
    const { style, cardStyle, imageStyle, title, excerpt, data, filters } = component;
    const apiUrl = useRef(null);
    const payload = useRef({});
    const baseURL = `${config.baseUrl}/v1`;
    const [error, setError] = useState(false);

    const [page, setPage] = useState(1);
    const [pageCount, setPageCount] = useState(1);


    switch (data.dataSourceType) {
        case 'category':
            payload.current = {
                limit: +data.limit,
                page_no: +page,
            };
            if (data.dataSource) {
                apiUrl.current = `/category/products/${data.dataSource.value}`;
            } else {
                apiUrl.current = null;
            }
            break;
        case 'all':
            apiUrl.current = '/products/online'
            payload.current = {
                limit: +data.limit,
                page_no: +page,
            };
        default:
            break;
    }

    const fetchData = useCallback(async () => {
        setLoading(true);
        const apiBaseURL = `https://clientapi.rentmy.co/api`;
        const [franchisee] = await DomainUtils();
        const tokenObject = await getRentMyToken(franchisee);
        console.log("data in RM product", data);
        if (tokenObject.success) {
            setError(false);
            axios.request({
                url: apiUrl.current,
                method: 'POST',
                baseURL: apiBaseURL,
                data: payload.current,
                headers: {
                    'Authorization': `Bearer ${tokenObject.token}`,
                    'location': data?.locationDataSource?.value || tokenObject.locationId
                },
            }).then(response => {
                const { result } = response.data;

                console.log("data ==>", data);
                console.log("response.data", response.data);

                // setProducts(result.data);
                // setLoading(false);

                if (result.data) {
                    setProducts(result?.data);
                    setLoading(false);
                    // setPage(result?.page)
                    setPageCount(Math.ceil(+result?.total / +data.limit))

                    console.log("numberrrrrrrrr of page", Math.ceil(+result?.total / +result.limit));

                    // if (filters?.pagination?.type === 'number') {
                    //     setPageCount(result)
                    // }
                    // if (filters?.pagination?.type === 'text') {
                    //     setPageCount(Math.ceil(result?.total / +data.limit))

                    // }
                }
            }).catch(error => {
                console.log(error);
                setLoading(false);
            })
                .finally(() => setLoading(false));

        } else {
            setLoading(false);
            setError(true);
        }
    }, [data])

    useEffect(() => {
        fetchData()
    }, [page])

    const getImage = useCallback((product) => {
        const rentMyStoreId = localStorage.getItem('rentMystrId');
        const src = product.images.length
            ? mediaUrl + '/products/' + rentMyStoreId + '/' + product.id + '/' + product.images[0]?.image_small
            : imagePlaceholder;
        return src;
    }, [])

    const descriptionText = useCallback((pricing, key, tailText) => {
        const priceDescription = pricing[key][0].duration > 1
            ? `Starting at $${pricing[key][0].price} USD for ${pricing[key][0].duration} ${tailText}s`
            : `Starting at $${pricing[key][0].price} USD per ${tailText}`
        return priceDescription;
    }, [])

    const getDescription = useCallback((prices) => {
        let pricing = prices[0];
        if (pricing.hasOwnProperty('hourly')) {
            const priceDescription = descriptionText(pricing, 'hourly', 'hour');
            return priceDescription;

        } else if (pricing.hasOwnProperty('daily')) {
            const priceDescription = descriptionText(pricing, 'daily', 'day');
            return priceDescription;

        } else if (pricing.hasOwnProperty('weekly')) {
            const priceDescription = descriptionText(pricing, 'weekly', 'week');
            return priceDescription;

        } else if (pricing.hasOwnProperty('monthly')) {
            const priceDescription = descriptionText(pricing, 'monthly', 'month');
            return priceDescription;

        } else if (pricing.hasOwnProperty('yearly')) {
            const priceDescription = descriptionText(pricing, 'yearly', 'year');
            return priceDescription;
        } else {
            return `Buy now for $${pricing.base.price} USD`;
        }
    }, [])

    useEffect(() => {
        if (apiUrl.current) {
            fetchData();
        }
    }, [data])

    const getSkeleton = (item) => {
        const skeleton = Array(item).fill().map((_, i) => (
            <Box
                flex="0 0" flexBasis="auto"
                width={["100%", "50%", "50%", `${100 / style.numberOfColumn}%`]}
                mb={`${style.rowGap}px`} px={[0, `${style.columnGap}px`, `${style.columnGap}px`, `${style.columnGap}px`]}
            >
                <Box
                    rounded={`
                        ${cardStyle.rounded.topLeft}px 
                        ${cardStyle.rounded.topRight}px 
                        ${cardStyle.rounded.bottomRight}px 
                        ${cardStyle.rounded.bottomLeft}px
                    `}
                    pt={`${cardStyle.padding.top}px`} pb={`${cardStyle.padding.bottom}px`}
                    pl={`${cardStyle.padding.left}px`} pr={`${cardStyle.padding.right}px`}
                    borderTopWidth={`${cardStyle.border.top}px`} borderBottomWidth={`${cardStyle.border.bottom}px`}
                    borderLeftWidth={`${cardStyle.border.left}px`} borderRightWidth={`${cardStyle.border.right}px`}
                    borderColor={cardStyle.border.color}
                >
                    <Skeleton>
                        <Box minHeight="180px"></Box>
                    </Skeleton>
                    <Skeleton height="20px" mt={4} />
                    <SkeletonText mt="4" noOfLines={4} spacing="5" />

                </Box>
            </Box>
        ))

        return skeleton;
    }

    return (
        <Box
            display="flex" flexWrap="wrap"
            mx={`-${Math.round(style.columnGap / 2)}px`}
            mt={`${style.margin.top}px`}
            mb={`${style.margin.bottom}px`}
            w='100%'
        // h={!error ? 'auto' : 20}
        >
            {
                error ?
                    <Text p={8} textAlign="center" w="100%" fontSize="sm">Rentmy integration may not be done properly of missing.</Text>
                    : null
            }
            {
                loading ?
                    <>
                        {getSkeleton(style.numberOfColumn * 1)}
                    </>
                    :
                    <>
                        <Flex
                            w='100%'
                            mb={4}
                            flexDirection={viewport?.breakPoint == 'base' ? 'column' : 'row'}
                            justifyContent={viewport?.breakPoint == 'base' ? 'center' : 'space-between'}
                            gap={2}
                        // px={1}
                        >
                            <Flex
                                w={viewport?.breakPoint == 'base' ? '100%' : filters?.sorting?.show ? '75%' : '100%'}
                                justifyContent={viewport?.breakPoint == 'base' ? 'center' : style?.alignSearch}
                            >
                                {filters?.search?.show &&
                                    <Flex
                                        minHeight='70px'
                                        background='gray.200'
                                        alignItems='center'
                                        w={viewport?.breakPoint == 'base' ? '100%' : filters?.sorting?.show ? '32.5%' : '24%'}
                                        // justifyContent='center'
                                        fontSize={25}
                                        fontWeight='500'
                                        borderRadius={8}
                                        px={3}
                                    >
                                        Search
                                    </Flex>
                                }
                            </Flex>

                            {/* {<Box
                                w={viewport?.breakPoint == 'base' ? '100%' : '25%'}
                            > */}
                            {filters?.sorting?.show &&
                                <Flex
                                    minHeight='70px'
                                    background='gray.200'
                                    alignItems='center'
                                    // w={'100%'}
                                    w={viewport?.breakPoint == 'base' ? '100%' : '25%'}
                                    // w={viewport?.breakPoint == 'base' ? '100%' : '24%'}
                                    // justifyContent={viewport?.breakPoint == 'base' ? 'flex-start' : 'flex-end'}
                                    fontSize={25}
                                    fontWeight='500'
                                    borderRadius={8}
                                    px={3}
                                >
                                    Sorting
                                </Flex>
                            }
                            {/* </Box>} */}

                        </Flex>

                        <Flex
                            w='100%'
                            flexDirection={viewport?.breakPoint == 'base' ? 'column' : style?.alignProduct ? style?.alignProduct : 'row'}
                            gap={2}
                        >
                            <Box w={viewport?.breakPoint == 'base' ? '100%' : '25%'} px={1}>
                                {filters?.categories?.show &&
                                    <Flex
                                        minHeight='70px'
                                        background='gray.200'
                                        alignItems='center'
                                        // justifyContent='center'
                                        fontSize={25}
                                        fontWeight='500'
                                        borderRadius={8}
                                        px={3}
                                    >
                                        Categories
                                    </Flex>
                                }

                                {filters?.filter?.show && (filters?.priceRange?.show || filters?.tags?.show || filters?.types?.show) &&
                                    <Box
                                        background='gray.200'
                                        borderRadius={8}
                                        mt={2}
                                        pt={2}
                                        pb={2}
                                    >

                                        <Flex
                                            mb={1}
                                            background='gray.200'
                                            alignItems='center'
                                            // justifyContent='center'
                                            fontSize={25}
                                            fontWeight='500'
                                            borderRadius={8}
                                            px={3}
                                        >
                                            Filters
                                        </Flex>

                                        {filters?.tags?.show &&
                                            <Flex
                                                background='gray.200'
                                                alignItems='center'
                                                // justifyContent='center'
                                                fontSize={20}
                                                fontWeight='500'
                                                borderRadius={8}
                                                pl={5}
                                            >
                                                - Tags
                                            </Flex>
                                        }

                                        {filters?.priceRange?.show &&
                                            <Flex
                                                mt={1}
                                                background='gray.200'
                                                alignItems='center'
                                                // justifyContent='center'
                                                fontSize={20}
                                                fontWeight='500'
                                                borderRadius={8}
                                                pl={5}
                                            >
                                                - Price
                                            </Flex>
                                        }

                                        {filters?.types?.show &&
                                            <Flex
                                                mt={1}
                                                background='gray.200'
                                                alignItems='center'
                                                // justifyContent='center'
                                                fontSize={20}
                                                fontWeight='500'
                                                borderRadius={8}
                                                pl={5}
                                            >
                                                - Type
                                            </Flex>
                                        }

                                    </Box>
                                }
                            </Box>

                            <Flex
                                w={viewport?.breakPoint == 'base' ? '100%' : !(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') ? '100%' : '75%'}
                                flexWrap={'wrap'}
                                justifyContent={viewport?.breakPoint == 'base' ? 'center' : 'flex-start'}
                            >
                                {
                                    products.map(product => (
                                        <Box
                                            flex="0 0" flexBasis="auto"
                                            minW={viewport?.breakPoint == 'base' ? '100%' : '220px'}
                                            h={'270px'}
                                            width={["100%", "50%", "50%", `${100 / style.numberOfColumn}%`]}
                                            mb={`${style.rowGap}px`} px={[0, `${style.columnGap}px`, `${style.columnGap}px`, `${style.columnGap}px`]}
                                            key={product._id}
                                        >
                                            <Box
                                                height="100%"
                                                bg={cardStyle.bgColor}
                                                position="relative"
                                                shadow={cardStyle.boxShadow}
                                                rounded={`
                                ${cardStyle.rounded.topLeft}px 
                                ${cardStyle.rounded.topRight}px 
                                ${cardStyle.rounded.bottomRight}px 
                                ${cardStyle.rounded.bottomLeft}px
                            `}
                                                pt={`${cardStyle.padding.top}px`} pb={`${cardStyle.padding.bottom}px`}
                                                pl={`${cardStyle.padding.left}px`} pr={`${cardStyle.padding.right}px`}
                                                borderTopWidth={`${cardStyle.border.top}px`} borderBottomWidth={`${cardStyle.border.bottom}px`}
                                                borderLeftWidth={`${cardStyle.border.left}px`} borderRightWidth={`${cardStyle.border.right}px`}
                                                borderColor={cardStyle.border.color}
                                            >
                                                <Box
                                                    mt={`${imageStyle.margin.top}px`} mb={`${imageStyle.margin.bottom}px`}
                                                    ml={`${imageStyle.margin.left}px`} mr={`${imageStyle.margin.right}px`}
                                                    pt={`${imageStyle.padding.top}px`} pb={`${imageStyle.padding.bottom}px`}
                                                    pl={`${imageStyle.padding.left}px`} pr={`${imageStyle.padding.right}px`}
                                                    borderTopWidth={`${imageStyle.border.top}px`} borderBottomWidth={`${imageStyle.border.bottom}px`}
                                                    borderLeftWidth={`${imageStyle.border.left}px`} borderRightWidth={`${imageStyle.border.right}px`}
                                                    rounded={`
                                    ${imageStyle.rounded.topLeft}px 
                                    ${imageStyle.rounded.topRight}px 
                                    ${imageStyle.rounded.bottomRight}px 
                                    ${imageStyle.rounded.bottomLeft}px
                                `}
                                                >
                                                    <Image
                                                        src={getImage(product)}
                                                        alt='product image'
                                                        priority
                                                        width="100%"
                                                        objectFit="cover"
                                                        rounded={`
                                        ${imageStyle.rounded.topLeft}px 
                                        ${imageStyle.rounded.topRight}px 
                                        ${imageStyle.rounded.bottomRight}px 
                                        ${imageStyle.rounded.bottomLeft}px
                                    `}
                                                        maxHeight="400px"
                                                        height={imageStyle.height}
                                                    />
                                                </Box>

                                                <Box
                                                    position="static"
                                                    textAlign={cardStyle.textAlign}
                                                >
                                                    <Text
                                                        mt={`${title.margin.top}px`} mb={`${title.margin.bottom}px`}
                                                        ml={`${title.margin.left}px`} mr={`${title.margin.right}px`}
                                                        pt={`${title.padding.top}px`} pb={`${title.padding.bottom}px`}
                                                        pl={`${title.padding.left}px`} pr={`${title.padding.right}px`}
                                                        color={title.text.color}
                                                        fontFamily={title.text?.fontFamily}
                                                        fontSize={`${title.text.size}px`} fontWeight={title.text.weight}
                                                        textTransform={title.text.transform}
                                                        lineHeight={title.text?.lineHeight ? title.text?.lineHeight : 'normal'}
                                                        letterSpacing={title.text?.letterSpacing ? `${title.text?.letterSpacing}px` : 'normal'}
                                                        fontStyle={title.text?.fontStyle ? `${title.text?.fontStyle}` : 'normal'}
                                                        textDecoration={title.text?.textDecoration ? `${title.text?.textDecoration}` : 'none'}
                                                    >
                                                        <Link href="">
                                                            {
                                                                truncate(product.name, {
                                                                    'length': 50,
                                                                    'separator': /,? +/
                                                                })
                                                            }
                                                        </Link>
                                                    </Text>
                                                    <Text
                                                        mt={`${excerpt.margin.top}px`} mb={`${excerpt.margin.bottom}px`}
                                                        ml={`${excerpt.margin.left}px`} mr={`${excerpt.margin.right}px`}
                                                        pt={`${excerpt.padding.top}px`} pb={`${excerpt.padding.bottom}px`}
                                                        pl={`${excerpt.padding.left}px`} pr={`${excerpt.padding.right}px`}
                                                        color={excerpt.text.color}
                                                        fontFamily={excerpt.text?.fontFamily}
                                                        fontSize={`${excerpt.text.size}px`} fontWeight={excerpt.text.weight}
                                                        textTransform={excerpt.text.transform}
                                                        lineHeight={excerpt.text.lineHeight ? excerpt.text.lineHeight : 'normal'}
                                                        letterSpacing={excerpt.text.letterSpacing ? `${excerpt.text.letterSpacing}px` : 'normal'}
                                                        fontStyle={excerpt.text?.fontStyle ? `${excerpt.text?.fontStyle}` : 'normal'}
                                                        textDecoration={excerpt.text?.textDecoration ? `${excerpt.text?.textDecoration}` : 'none'}
                                                    >
                                                        {/* Starting at $20.00 USD for 12 hours */}
                                                        {getDescription(product.prices)}
                                                    </Text>

                                                </Box>
                                            </Box>
                                        </Box>
                                    ))
                                }

                                {!(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') &&
                                    <RentMyPagination
                                        component={component}
                                        page={page}
                                        setPage={setPage}
                                        limit={data.limit}
                                        totalPages={pageCount}
                                        isPreviousData={false}
                                        isFetching={loading}
                                    />
                                }
                            </Flex>
                        </Flex>

                        {(style?.alignProduct == 'row' || style?.alignProduct == 'row-reverse') &&
                            <RentMyPagination
                                component={component}
                                page={page}
                                setPage={setPage}
                                limit={data.limit}
                                totalPages={pageCount}
                                isPreviousData={false}
                                isFetching={loading}
                            />
                        }

                    </>
            }
        </Box>
    )
}

export default React.memo(RentMyProductWithFilters);

