import { Box, Icon, Input, InputGroup, InputRightElement, useMediaQuery } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { MdClear } from 'react-icons/md';


const RentMySearch = ({
    searchText,
    setSearchText,
    page,
    setPage,
    filters,
    setApiCallStat
}) => {
    const [isMobile] = useMediaQuery('(max-width: 767px)');

    const [inputValue, setInputValue] = useState(searchText);


    const handleInputChange = (val) => {
        setInputValue(val);
    };

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            // setSearchText(inputValue);

            if (page !== 1) {
                setPage(1);
            }
        }, 500);
        return () => clearTimeout(timeoutId);
    }, [inputValue, 500]);


    return (
        <Box
            w={isMobile ? '100%' : filters?.sorting?.show ? '32.5%' : '24%'}
        >
            <InputGroup>
                <Input
                    type="text"
                    size='md'
                    w="100%"
                    variant="outline"
                    // variant="flushed"
                    placeholder="Search"
                    value={searchText}
                    // value={inputValue || ''}
                    onChange={(e) => {
                        setSearchText(e.target.value)
                        // handleInputChange(e.target.value);
                        if (e.target.value == '') {
                            setApiCallStat('search_empty')
                        }
                    }}
                />
                <InputRightElement width='4.5rem' display={searchText?.trim().length > 0 ? 'flex' : 'none'} cursor={'pointer'}
                    onClick={() => {
                        setSearchText('');
                        setInputValue('')
                    }}
                >
                    <Icon ml={10} size='sm' as={MdClear} onClick={() => {
                        setSearchText('');
                        setInputValue('')
                        setApiCallStat('search_clear' + searchText + new Date().getTime())
                    }}>

                    </Icon>
                </InputRightElement>
            </InputGroup>
        </Box>
    )
}

export default RentMySearch;
