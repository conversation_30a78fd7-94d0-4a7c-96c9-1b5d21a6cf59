import axios from 'axios';
import dayjs from 'dayjs';
import { getRentMyToken } from 'service/rentmyAuth';

const apiBaseURL = `https://clientapi.rentmy.co/api`;

export const getDatePriceDuration = async (startDate, priceId, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios.request({
    url: '/product/get_dates_price_duration',
    method: 'GET',
    baseURL: apiBaseURL,
    params: {
      start_date: startDate,
      start_time: false,
      price_id: priceId,
      location: tokenObject.locationId,
    },
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const getPriceValueForRent = async (
  response,
  product,
  rentOption,
  quantity,
  isApply = false,
  franchisee
) => {
  const tokenObject = await getRentMyToken(franchisee);
  const rentMyCartToken = localStorage.getItem('rentMyCartToken');
  const cartToken = rentMyCartToken ? rentMyCartToken : '';
  const data = {
    custom_fields: [],
    deposit_amount: product.deposit_amount,
    driving_license_required: product.driving_license,
    is_apply: isApply,
    location: tokenObject.locationId,
    price: rentOption.price,
    price_id: rentOption.id,
    product_id: product.id,
    quantity: quantity,
    rent_end: response.data.end_date,
    rent_start: response.data.start_date,
    rental_duration: rentOption.duration,
    rental_type: 'rent',
    sales_tax: product.sales_tax,
    term: 1,
    token: cartToken,
    variants_products_id: product.default_variant.variants_products_id,
  };

  return axios.request({
    url: '/get-price-value',
    method: 'POST',
    baseURL: apiBaseURL,
    data: data,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const getPackagePriceValueForRent = async (
  response,
  packageProduct,
  rentOption,
  quantity,
  isApply = false,
  franchisee
) => {
  const tokenObject = await getRentMyToken(franchisee);
  const postData = {
    custom_fields: [],
    is_apply: isApply,
    location: 1793,
    package_id: packageProduct.id,
    price: rentOption.price,
    price_id: rentOption.id,
    products: [],
    quantity: quantity,
    rent_start: response.data.start_date,
    rent_end: response.data.end_date,
    rental_duration: rentOption.duration,
    rental_type: 'rent',
    term: packageProduct.term,
    token: '',
    variants_products_id: packageProduct.variants_products_id,
  };

  packageProduct.products.forEach((product, index) => {
    postData.products.push({
      quantity: product.quantity,
      product_id: product.id,
      available: 1,
      product_index: index,
      variants_products_id: product.variants[0].id,
    });
  });

  return axios({
    url: '/get-package-price',
    method: 'POST',
    baseURL: apiBaseURL,
    data: postData,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const getPriceValueForBuy = async (product, quantity, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  const rentMyCartToken = localStorage.getItem('rentMyCartToken');
  const cartToken = rentMyCartToken ? rentMyCartToken : '';

  const data = {
    custom_fields: [],
    deposit_amount: product.deposit_amount,
    driving_license_required: product.driving_license,
    is_apply: false,
    location: tokenObject.locationId,
    price: product.prices[0].base.price,
    price_id: product.prices[0].base.id,
    product_id: product.id,
    quantity: quantity,
    rent_end: '',
    rent_start: '',
    rental_duration: 0,
    rental_type: 'buy',
    sales_tax: product.sales_tax,
    term: 1,
    token: cartToken,
    variants_products_id: product.default_variant.variants_products_id,
  };

  return axios.request({
    url: '/get-price-value',
    method: 'POST',
    baseURL: apiBaseURL,
    data: data,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const getVariantChain = async (params, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);

  return axios.request({
    url: '/variant-chain',
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
    params: {
      ...params,
      t: Date.now(),
    },
  });
};

export const getPathOfChain = async (params, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios.request({
    url: '/get-path-of-chain',
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
    params: params,
  });
};

export const addToCart = async (
  product,
  rentType,
  rent,
  quantity,
  isApply,
  startDate,
  endDate,
  pickUpDate,
  franchisee
) => {
  const tokenObject = await getRentMyToken(franchisee);
  const rentMyCartToken = localStorage.getItem('rentMyCartToken');
  const cartToken = rentMyCartToken ? rentMyCartToken : '';
  let postData = null;
  if (rentType.type === 'rent') {
    postData = {
      custom_fields: [],
      deposit_amount: product?.deposit_amount,
      driving_license_required: product?.driving_license,
      is_apply: isApply,
      location: tokenObject?.locationId,
      price: rent?.price,
      price_id: rent?.id,
      product_id: product?.id,
      quantity: quantity,
      rent_end: dayjs(endDate).format('YYYY-MM-DD H:mm'),
      rent_start: dayjs(startDate).format('YYYY-MM-DD H:mm'),
      rental_duration: rent?.duration,
      rental_type: 'rent',
      sales_tax: product?.sales_tax,
      term: 1,
      token: cartToken,
      variants_products_id: product?.default_variant?.variants_products_id,
      zone: 360,
    };
  } else {
    postData = {
      custom_fields: [],
      deposit_amount: product?.deposit_amount,
      deposite_tax: '',
      driving_license_required: product?.driving_license,
      is_apply: isApply,
      location: tokenObject?.locationId,
      price: rentType?.base?.price,
      price_id: rentType?.base?.id,
      product_id: product?.id,
      quantity: quantity,
      rent_end: dayjs(pickUpDate).format('YYYY-MM-DD H:mm'),
      rent_start: '',
      rental_duration: 0,
      rental_type: 'buy',
      sales_tax: product?.sales_tax,
      term: '',
      token: cartToken,
      variants_products_id: product?.default_variant?.variants_products_id,
      zone: 360,
    };
  }

  return axios({
    url: '/carts/add-to-cart',
    method: 'POST',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
    data: postData,
  });
};

// Add to cart for package
export const addToCartForPackage = async (
  packageProduct,
  variants,
  rentType,
  rent,
  quantity,
  isApply,
  startDate,
  endDate,
  pickUpDate,
  franchisee
) => {
  const tokenObject = await getRentMyToken(franchisee);
  const rentMyCartToken = localStorage.getItem('rentMyCartToken');
  const cartToken = rentMyCartToken ? rentMyCartToken : '';
  let url = '/package/add-to-cart';
  let postData = null;
  if (rentType.type === 'rent') {
    postData = {
      custom_fields: [],
      deposit_amount: packageProduct.deposit_amount,
      location: tokenObject.locationId,
      package_id: packageProduct.id,
      price: rent.price,
      price_id: rent.id,
      products: variants,
      quantity: quantity,
      rent_end: dayjs(endDate).format('YYYY-MM-DD H:mm'),
      rent_start: dayjs(startDate).format('YYYY-MM-DD H:mm'),
      rental_duration: rent.duration,
      rental_type: 'rent',
      term: 1,
      token: cartToken,
      variants_products_id: packageProduct.variants_products_id,
      zone: 360,
    };
  } else {
    postData = {
      custom_fields: [],
      deposit_amount: packageProduct.deposit_amount,
      location: tokenObject.locationId,
      package_id: packageProduct.id,
      price: rentType.base.price,
      price_id: rentType.base.id,
      products: variants,
      quantity: quantity,
      rent_end: dayjs(pickUpDate).format('YYYY-MM-DD H:mm'),
      rent_start: '',
      rental_duration: 0,
      rental_type: 'buy',
      term: '',
      token: cartToken,
      variants_products_id: packageProduct.variants_products_id,
      zone: 360,
    };
    url = '/package/add-to-cart/buy';
  }

  return axios({
    url: url,
    method: 'POST',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
    data: postData,
  });
};

export const getCartItems = async (token, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/carts/${token}`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const deleteCartItem = async (token, itemId, productId, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/carts/cart-remove-item`,
    method: 'POST',
    baseURL: apiBaseURL,
    data: {
      cart_item_id: itemId,
      product_id: productId,
      token: token,
    },
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const updateCartItem = async (postData, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/carts/update`,
    method: 'POST',
    baseURL: apiBaseURL,
    data: postData,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const applyCouponHandler = async (couponCode, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  const rentMyCartToken = localStorage.getItem('rentMyCartToken');
  return axios({
    url: `/carts/apply-coupon`,
    method: 'POST',
    baseURL: apiBaseURL,
    data: {
      coupon: couponCode,
      token: rentMyCartToken,
    },
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

// Checkout ----------------------------------------------------------------------------

// get delivery settings
export const getDeliverySettings = async (franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/stores/delivery-settings`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

// get getway settings
export const getGetwaySettings = async (franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/payments/gateway?is_online=1`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

// get locations list
export const getLocationList = async (franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/locations/list`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

// get delivery cost option
export const getDeliveryCost = async (postData, isSame, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  if (isSame) {
    const {
      address_line1: shipping_address1,
      address2: shipping_address2,
      city: shipping_city,
      country: shipping_country,
      email: shipping_email,
      first_name: shipping_first_name,
      last_name: shipping_last_name,
      mobile: shipping_mobile,
      state: shipping_state,
      zipcode: shipping_zipcode,
    } = postData;
    const address = {
      combinedDeliveryAddress: '',
      shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    };
    return axios({
      url: `/delivery-charge-list`,
      method: 'POST',
      baseURL: apiBaseURL,
      data: {
        address,
        pickup: tokenObject.locationId,
      },
      headers: {
        Authorization: `Bearer ${tokenObject.token}`,
        location: tokenObject.locationId,
      },
    });
  } else {
    const {
      shipping_address_line1: shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    } = postData;
    const address = {
      combinedDeliveryAddress: '',
      shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    };
    return axios({
      url: `/delivery-charge-list`,
      method: 'POST',
      baseURL: apiBaseURL,
      data: {
        address,
        pickup: tokenObject.locationId,
      },
      headers: {
        Authorization: `Bearer ${tokenObject.token}`,
        location: tokenObject.locationId,
      },
    });
  }
};

// get shipping method options
export const getShippingMethods = async (postData, isSame, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  const cartToken = localStorage.getItem('rentMyCartToken');
  if (isSame) {
    const {
      address_line1: shipping_address1,
      address2: shipping_address2,
      city: shipping_city,
      country: shipping_country,
      email: shipping_email,
      first_name: shipping_first_name,
      last_name: shipping_last_name,
      mobile: shipping_mobile,
      state: shipping_state,
      zipcode: shipping_zipcode,
    } = postData;
    const address = {
      combinedDeliveryAddress: '',
      shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    };
    return axios({
      url: `/shipping/rate`,
      method: 'POST',
      baseURL: apiBaseURL,
      data: {
        address,
        pickup: tokenObject.locationId,
        token: cartToken,
      },
      headers: {
        Authorization: `Bearer ${tokenObject.token}`,
        location: tokenObject.locationId,
      },
    });
  } else {
    const {
      shipping_address_line1: shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    } = postData;
    const address = {
      combinedDeliveryAddress: '',
      shipping_address1,
      shipping_address2,
      shipping_city,
      shipping_country,
      shipping_email,
      shipping_first_name,
      shipping_last_name,
      shipping_mobile,
      shipping_state,
      shipping_zipcode,
    };
    return axios({
      url: `/shipping/rate`,
      method: 'POST',
      baseURL: apiBaseURL,
      data: {
        address,
        pickup: tokenObject.locationId,
        token: cartToken,
      },
      headers: {
        Authorization: `Bearer ${tokenObject.token}`,
        location: tokenObject.locationId,
      },
    });
  }
};

export const getCartByDelivery = async (postData, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/carts/delivery`,
    method: 'POST',
    baseURL: apiBaseURL,
    data: postData,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const getPayment = async (franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios({
    url: `/store-config/payments`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};
// @bject extraData: payment amount, stripe token, card holder name
export const placeOrderHandler = async (
  userData,
  delivery,
  getway,
  shippingMethod,
  rentMyCartToken,
  isSame,
  extraData,
  franchisee
) => {
  const tokenObject = await getRentMyToken(franchisee);

  console.log('userData:::', userData);
  console.log('delivery:::', delivery);
  console.log('getway:::', getway);
  console.log('shippingMethod:::', shippingMethod);
  console.log('rentMyCartToken:::', rentMyCartToken);
  console.log('isSame:::', isSame);
  console.log('extraData:::', extraData);
  console.log('franchisee:::', franchisee);

  let payload = {
    additional_charges: [],
    address2: userData.address2,
    address_line1: userData.address_line1,
    city: userData.city,
    combinedAddress: '',
    combinedDeliveryAddress: '',
    company: userData.company,
    country: userData.country.toLowerCase(),
    currency: 'USD',
    custom_checkout: { fields: [] },
    custom_values: null,
    delivery: delivery,
    driving_license: '',
    email: userData.email,
    fieldSelection: null,
    fieldText: null,
    first_name: userData.first_name,
    gateway_id: getway.id,
    last_name: userData.last_name,
    location_id: tokenObject.locationId,
    mobile: userData.mobile,
    note: userData.note ? userData.note : '',
    order_source: 'Online',
    payment_gateway_name: getway.name,
    signature: null,
    special_instructions: '',
    special_requests: '',
    state: userData.state,
    token: rentMyCartToken,
    type: getway.type === 'offline' ? 2 : 1,
    zipcode: userData.zipcode,
    shipping_method: shippingMethod,
  };
  if (isSame.delivery || isSame.shipping) {
    payload = {
      ...payload,
      shipping_address1: userData.address_line1,
      shipping_address2: userData.address2,
      shipping_city: userData.city,
      shipping_country: userData.country.toLowerCase(),
      shipping_email: userData.email,
      shipping_first_name: userData.first_name,
      shipping_last_name: userData.last_name,
      shipping_mobile: userData.mobile,
      shipping_state: userData.state,
      shipping_zipcode: userData.zipcode,
    };
  } else {
    payload = {
      ...payload,
      shipping_address1: userData.shipping_address_line1,
      shipping_address2: userData.shipping_address2,
      shipping_city: userData.shipping_city,
      shipping_country: userData.shipping_country.toLowerCase(),
      shipping_email: userData.shipping_email,
      shipping_first_name: userData.shipping_first_name,
      shipping_last_name: userData.shipping_last_name,
      shipping_mobile: userData.shipping_mobile,
      shipping_state: userData.shipping_state,
      shipping_zipcode: userData.shipping_zipcode,
    };
  }
  if (getway.type === 'online') {
    payload = {
      ...payload,
      ...extraData,
    };
  }
  return axios({
    url: `/orders/online`,
    method: 'POST',
    baseURL: apiBaseURL,
    data: payload,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};

export const completeOrder = async (id, franchisee) => {
  const tokenObject = await getRentMyToken(franchisee);
  return axios.request({
    url: `/orders/${id}/complete`,
    method: 'GET',
    baseURL: apiBaseURL,
    headers: {
      Authorization: `Bearer ${tokenObject.token}`,
      location: tokenObject.locationId,
    },
  });
};
