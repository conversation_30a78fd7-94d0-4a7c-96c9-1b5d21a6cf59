const subscriber_api = '/franchisees/store_name';
const builder_api = '/pages/cityAdmin';
const builder_sa_api = '/pages/superAdmin';
const builder_by_slug_ca_api = `/pages?adminType=CA`;
const subscriber_menu_api = '/menus';
const global_setting_sa_api =
  '/globalSettings?adminType=SA&limit=1&fields=theme,favicon,showSignIn,brand';
const global_setting_ca_api = '/globalSettings?adminType=CA';
const menus_ca_api = '/menus?adminType=CA';
const menus_sa_api = '/menus?adminType=SA&primaryMenu=true';
const header_ca_api = '/headers/getActive/CA';
const header_sa_api = '/headers/getActive/SA';
const footer_ca_api = '/footers/getActive/CA';
const blog_post_api = '/posts';
const googleAnalyticsApi = '/googleAnalytics';
const googleSearchConsoleApi = '/googleSearchConsole';
const googleAdSenseApi = '/googleAdSense';
const chatConfigApi = '/chatConfig';
const pinterestConfigApi = '/pinterestVerification';
const page_api = '/builders/subscriberAdmin/page';

export {
  subscriber_api,
  builder_api,
  builder_sa_api,
  subscriber_menu_api,
  global_setting_sa_api,
  global_setting_ca_api,
  menus_ca_api,
  menus_sa_api,
  header_ca_api,
  header_sa_api,
  footer_ca_api,
  builder_by_slug_ca_api,
  blog_post_api,
  googleAnalyticsApi,
  // googleSearchConsoleApi,
  googleAdSenseApi,
  chatConfigApi,
  pinterestConfigApi,
  page_api,
};
