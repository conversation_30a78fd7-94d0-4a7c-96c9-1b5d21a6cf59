const local = process.env.NEXT_PUBLIC_ENVIRONMENT == 'local' ? true : false;
const development =
  process.env.NEXT_PUBLIC_ENVIRONMENT == 'development' ? true : false;
const production =
  process.env.NEXT_PUBLIC_ENVIRONMENT == 'production' ? true : false;

const protocol = local ? 'https://' : 'https://';

const host = local
  ? 'api.pc-staging.com'
  // ? '*************:3530'
  : production
    ? 'api.prolificcloud.com'
    : 'api.pc-staging.com';

const domain = 'prolificcloud.com';
const domainDev = 'pc-staging.com';
const domainLocal = 'localhost:3001';

const site = 'https://prolificcloud.com';
const siteDev = 'https://pc-staging.com';
const siteLocal = 'http://localhost:3001';

const media = 'https://content.prolificcloud.com';
const mediaDev = 'https://content.pc-staging.com';
const mediaLocal = 'https://content.pc-staging.com';

const version = 'v1';

export const basicToken = process.env.NEXT_PUBLIC_BASIC_TOKEN;
export const ipV4 = 'https://api64.ipify.org/?format=json';

export const siteUrl = local ? siteLocal : production ? site : siteDev;
export const siteDomain = local ? domainLocal : production ? domain : domainDev;
export const mediaUrl = local ? mediaLocal : production ? media : mediaDev;
export const baseUrl = `${protocol}${host}`;
export const apiBaseUrl = `${protocol}${host}/${version}`;
export const apiBaseUrlV2 = `${protocol}${host}/api/v2`;

// if (local) {
// }
// if (development) {
// }
// if (production) {
// }

// *************:3530
