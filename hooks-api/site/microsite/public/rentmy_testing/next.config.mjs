import withBundleAnalyzer from '@next/bundle-analyzer';

const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

// const nextConfig = {
//   reactStrictMode: false,
// };

const nextConfig = bundleAnalyzer({
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
    ],
  },
  productionBrowserSourceMaps: true, // Enables source maps in production
  compress: true,
});

export default nextConfig;
