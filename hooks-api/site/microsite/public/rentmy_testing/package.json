{"name": "testing-3.12.24", "version": "0.1.0", "private": true, "scripts": {"lint": "next lint", "dev": "next dev -p 3002", "build": "next build", "export": "next build & next export", "start": "next start -p 3002", "analyze": "next build", "server:prod": "set NODE_ENV=production && pm2 start 'npm start' --name 'pc_web'", "server:dev": "set NODE_ENV=production && pm2 start 'npm start' --name 'pc_web'"}, "dependencies": {"@chakra-ui/react": "1.8.8", "@emotion/react": "11", "@emotion/styled": "11", "@reduxjs/toolkit": "^1.8.2", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.1", "axios": "0.27.2", "dayjs": "^1.11.6", "framer-motion": "6", "lodash": "^4.17.21", "next": "12.3.0", "next-redux-wrapper": "^7.0.5", "nprogress": "^0.2.0", "react": "17.0.2", "react-datepicker": "^4.8.0", "react-dom": "17.0.2", "react-redux": "^8.0.2", "redux": "^4.2.0", "react-hook-form": "6.14.2", "react-icons": "^4.3.1", "redux-devtools-extension": "^2.13.9"}, "devDependencies": {"@next/bundle-analyzer": "^13.5.7"}}