import { ChakraProvider } from "@chakra-ui/react";
import "../styles/globals.css";
import { useEffect } from "react";
import { wrapper } from "@/store/store";
import Head from "next/head";

const App = ({ Component, pageProps }) => {

  useEffect(() => {
    localStorage?.setItem('rentMystrId', 351)
  })

  return (
    <ChakraProvider>
      <Head>
        <title>Dummy title</title>
        <meta name="description" content="Dummy description" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Component {...pageProps} />
    </ChakraProvider>
  )
}
export default wrapper.withRedux(App);
