import Layout from '@/components/layout/layout';
import { Box, Button, Text } from "@chakra-ui/react";
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

const RentMyProductWithFilters = dynamic(() => import('@/components/main/rentmy/RentMyProductWithFilters'), {
  ssr: false, 
});
const Filter = () => {
    
    const router = useRouter();

    return (
        <Layout>
            <Box textAlign={'center'} my={5}>
                <Text fontSize={35} mt={2}>Product lists with filters</Text>
                <Button
                    onClick={() => router.push('/')}
                >
                    Go to Products without filters
                </Button>
            </Box>

            <RentMyProductWithFilters />
        </Layout>
    );
};

export default Filter;