import Layout from "@/components/layout/layout";
import { Box, Button, Text } from "@chakra-ui/react";
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

const RentMyProduct = dynamic(() => import('@/components/main/rentmy/RentMyProduct'), {
  ssr: false, 
});

export default function Home() {

  const router = useRouter();

  return (
    <Layout>

      <Box textAlign={'center'} my={5}>
        <Text fontSize={35} mt={2}>Product lists</Text>
        <Button
          onClick={() => router.push('/filter')}
        >
          Go to Products with filters
        </Button>
      </Box>

      <RentMyProduct component={null} />
    </Layout>
  );
}
