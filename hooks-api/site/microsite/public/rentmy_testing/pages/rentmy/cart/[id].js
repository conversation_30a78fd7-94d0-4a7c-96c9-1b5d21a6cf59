import {
  Alert,
  AlertDescription,
  AlertIcon,
  AlertTitle,
  Box,
  Button,
  Center,
  Container,
  Flex,
  Icon,
  Image,
  Input,
  Table,
  Tbody,
  Td,
  Text,
  Tfoot,
  Th,
  Thead,
  Tr,
  useToast,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
// import { Scrollbars } from 'react-custom-scrollbars';
import Layout from 'components/layout/layout';
import Loader from 'components/loader/Loader';
import {
  applyCouponHandler,
  deleteCartItem,
  getCartItems,
  updateCartItem,
} from 'components/main/rentmy/service';
import { useRouter } from 'next/router';
import { FaRegTrashAlt } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { pageSetup, subscriberFromDomain } from 'service/initialSetting';
import { metaBuilder } from 'store/slices/metaSlice';
import { decrementCartItem } from 'store/slices/rentmySlice';
import { subscriberData } from 'store/slices/subscriberSlice';
import { wrapper } from 'store/store';
import dynamic from 'next/dynamic';
//import Link from 'next/link';
const Link = dynamic(() => import('next/link'));

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyCart({ headerData, footerData }) {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isProceedLoading, setIsProceedLoading] = useState(false);
  const [isICBtnDisable, setIsICBtnDisable] = useState(false);
  const [coupon, setCoupon] = useState('');
  const router = useRouter();
  const { id } = router.query;
  const toast = useToast();
  const dispatch = useDispatch();

  // const { id: subscriberId } = useSelector(subscriberData);
  const subscriberId = '636a25eb5d1fa80f73143ea4'

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
        '/products/' +
        rentMyStoreId +
        '/' +
        product.id +
        '/' +
        product.images[0]?.image_small
        : '/static/media/product-image-placeholder.jpg';
      return src;
    },
    [cart]
  );

  const deleteCartItemHandle = useCallback(
    (itemId, product) => {
      const cartToken = localStorage.getItem('rentMyCartToken');
      deleteCartItem(cartToken, itemId, product.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            dispatch(decrementCartItem(1));
            toast({
              title: 'Delete',
              description: 'Item deleted successfully',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            const rentMyCart = JSON.stringify(result.data);
            localStorage.setItem('rentMyUserCart', rentMyCart);
            setCart(result.data);
          }
        })
        .catch((error) => console.log(error.message));
    },
    [cart]
  );

  const cartItemQuantityUpdate = useCallback(
    (prevQuantity, type, cartItem) => {
      setIsICBtnDisable(true);
      if (type === 'dec' && prevQuantity === 1) {
        toast({
          title: 'Error',
          description: "Quantity can't be zero",
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else {
        const rentMyCartToken = localStorage.getItem('rentMyCartToken');
        if (rentMyCartToken) {
          const postData = {
            id: cartItem.id,
            increment: type === 'inc' ? 1 : 0,
            price: cartItem.price,
            sales_tax: cartItem.sales_tax,
            token: rentMyCartToken,
            view_token: '',
          };

          updateCartItem(postData, subscriberId)
            .then((response) => {
              const { result } = response.data;
              if (result.hasOwnProperty('data')) {
                toast({
                  title: 'Update',
                  description: 'Item quantity has been updated',
                  status: 'success',
                  duration: 5000,
                  isClosable: true,
                });
                const rentMyCart = JSON.stringify(result.data);
                localStorage.setItem('rentMyUserCart', rentMyCart);
                setCart(result.data);
              } else {
                toast({
                  title: 'Error',
                  description: result?.error,
                  status: 'error',
                  duration: 5000,
                  isClosable: true,
                });
              }
              setIsICBtnDisable(false);
            })
            .catch((error) => {
              console.log(error.message);
              setIsICBtnDisable(false);
            });
        }
      }
    },
    [cart]
  );

  const couponHandler = useCallback(
    (couponCode) => {
      if (!couponCode) return;

      applyCouponHandler(couponCode, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            toast({
              title: 'Success',
              description: 'Coupon accepted successfully',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
            const rentMyCart = JSON.stringify(result.data);
            localStorage.setItem('rentMyUserCart', rentMyCart);
            setCart(result.data);
            setCoupon('');
          } else {
            toast({
              title: 'Error',
              description: result?.error,
              status: 'error',
              duration: 5000,
              isClosable: true,
            });
          }
        })
        .catch((error) => console.log(error.message));
    },
    [cart]
  );

  useEffect(() => {
    setLoading(true);
    if (!router.isReady) {
      return;
    }

    getCartItems(id, subscriberId)
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          const rentMyCart = JSON.stringify(result.data);
          localStorage.setItem('rentMyUserCart', rentMyCart);
          setCart(result.data);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.log(error.message);
        setLoading(false);
      });
  }, [router.isReady]);

  if (loading) {
    return (
      <Layout header={headerData} footer={footerData}>
        <Loader />
      </Layout>
    );
  }

  return (
    <Layout header={headerData} footer={footerData}>
      <Box bg="gray.100">
        <Container maxW={1240} py={4}>
          <Box p={5} bg="white" rounded={8}>
            <Box display="flex" bg="gray.100" mb={4} borderRadius="8px">
              <Image
                src={
                  'https://b1547765.smushcdn.com/1547765/wp-content/uploads/2019/11/logo.png?lossy=1&strip=1&webp=1'
                }
                alt='rentmy logo'
                width="150px"
                mr={4}
                borderRadius=" 8px 0 0 8px"
              />
              <Center>
                <Text
                  fontSize="1.4rem"
                  textTransform="uppercase"
                  fontWeight={600}
                  my={2}
                >
                  Cart Details
                </Text>
              </Center>
            </Box>

            {cart && cart?.cart_items.length ? (
              <Box>
                {/* <Scrollbars
                autoHide
                autoHeight
                autoHeightMin={0}
                autoHeightMax={500}> */}
                <Table variant="simple" size="lg">
                  <Thead>
                    <Tr>
                      <Th></Th>
                      <Th>Product</Th>
                      <Th>Unit Price</Th>
                      <Th>Quantity</Th>
                      <Th isNumeric>Sub Total</Th>
                      <Th></Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {cart.cart_items.map((cartItem) => (
                      <Tr key={cartItem.id}>
                        <Td>
                          <Image
                            src={getImage(cartItem.product)}
                            priority
                            alt='product image'
                            width="60px"
                            height="auto"
                          />
                        </Td>
                        <Td>{cartItem.product.name}</Td>
                        <Td>{cartItem.price}</Td>
                        <Td>
                          {cartItem.product_type == 1 ? (
                            <Flex>
                              <Button
                                disabled={
                                  cartItem.quantity == 1 || isICBtnDisable
                                }
                                size="xs"
                                rounded={2}
                                onClick={() =>
                                  cartItemQuantityUpdate(
                                    cartItem.quantity,
                                    'dec',
                                    cartItem
                                  )
                                }
                              >
                                -
                              </Button>
                              <Text px="10px">{cartItem.quantity}</Text>
                              <Button
                                disabled={isICBtnDisable}
                                size="xs"
                                rounded={2}
                                onClick={() =>
                                  cartItemQuantityUpdate(
                                    cartItem.quantity,
                                    'inc',
                                    cartItem
                                  )
                                }
                              >
                                +
                              </Button>
                            </Flex>
                          ) : (
                            cartItem.quantity
                          )}
                        </Td>
                        <Td isNumeric>
                          {cartItem.sub_total < 0 ? 0 : cartItem.sub_total}
                        </Td>
                        <Td>
                          <Icon
                            as={FaRegTrashAlt}
                            w={3}
                            h={3}
                            color="gray.400"
                            ml="10px"
                            cursor="pointer"
                            _hover={{ color: 'gray.500' }}
                            onClick={() =>
                              deleteCartItemHandle(
                                cartItem.id,
                                cartItem.product
                              )
                            }
                          />
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
                {/* </Scrollbars> */}

                <Box
                  display="flex"
                  flexWrap="wrap"
                  mt={4}
                  justifyContent="space-between"
                >
                  <Box
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '50%', '50%']}
                  >
                    <Text fontSize="1.1rem" fontWeight={700} mb={2}>
                      Cart Totals
                    </Text>
                    <Table variant="simple" size="sm">
                      <Tbody>
                        {cart.coupon_id ? (
                          <Tr>
                            <Td>Coupon Applied</Td>
                            <Td textAlign="right" isNumeric>
                              {/* Problem with api responses */}
                              {cart.coupon_amount < 0 ? 0 : cart.coupon_amount}
                            </Td>
                          </Tr>
                        ) : null}
                        <Tr>
                          <Td>Subtotal</Td>
                          <Td textAlign="right" isNumeric>
                            {/* Problem with api responses */}
                            {cart.sub_total < 0 ? 0 : cart.sub_total}
                            <Text as="span">(inc. tax)</Text>
                          </Td>
                        </Tr>
                        <Tr>
                          <Td>Deposit Amount</Td>
                          <Td textAlign="right" isNumeric>
                            {/* Problem with api responses */}
                            {cart.deposit_amount < 0 ? 0 : cart.deposit_amount}
                          </Td>
                        </Tr>
                        <Tr>
                          <Td>Shipping Charge</Td>
                          <Td textAlign="right">Calculated in the next step</Td>
                        </Tr>
                        <Tr>
                          <Td>Tax</Td>
                          <Td textAlign="right" isNumeric>
                            $0
                          </Td>
                        </Tr>
                        <Tr>
                          <Td>Delivery Tax</Td>
                          <Td textAlign="right">Calculated in the next step</Td>
                        </Tr>
                      </Tbody>
                      <Tfoot>
                        <Tr>
                          <Th fontSize="1.2rem" py={4}>
                            Total
                          </Th>
                          <Th fontSize="1.2rem" py={4} isNumeric>
                            {/* Problem with api responses */}
                            <Text>{cart?.total < 0 ? 0 : cart?.total}</Text>
                            <Text fontSize=".7rem" fontWeight={300}>
                              (includes)
                            </Text>
                          </Th>
                        </Tr>
                      </Tfoot>
                    </Table>
                  </Box>
                  <Box
                    flex="0 0"
                    flexBasis="auto"
                    width={['100%', '100%', '45%', '45%']}
                    mb={4}
                  >
                    <Flex mb={4}>
                      <Input
                        size="sm"
                        placeholder="Enter coupon code"
                        value={coupon}
                        onChange={(e) => setCoupon(e.target.value)}
                      />
                      <Button
                        onClick={() => couponHandler(coupon)}
                        size="sm"
                        ml="10px"
                        px="30px"
                      >
                        Apply Coupon
                      </Button>
                    </Flex>
                    <Flex justifyContent="center" py="20px" flexWrap="wrap">
                      <Link href="/">
                        <Button flexGrow="1" px="30px" mb="10px">
                          Continue Shopping
                        </Button>
                      </Link>
                      <Link href="/rentmy/checkout/single-page-checkout">
                        <Button
                          flexGrow="1"
                          ml="10px"
                          px="30px"
                          onClick={() => setIsProceedLoading(true)}
                          isLoading={isProceedLoading}
                        >
                          Proceed to Checkout
                        </Button>
                      </Link>
                    </Flex>
                  </Box>
                </Box>
              </Box>
            ) : (
              <>
                <Box bg="#edf2f9" />
                <Alert
                  status="info"
                  variant="subtle"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                  textAlign="center"
                  height="50vh"
                  colorScheme="red.50"
                  mt="20px"
                >
                  <AlertIcon boxSize="40px" mr={0} />
                  <AlertTitle mt={4} mb={1} fontSize="lg">
                    Sorry, No item found!
                  </AlertTitle>
                  <AlertDescription maxWidth="sm">
                    No item has been added yet.
                  </AlertDescription>
                </Alert>
              </>
            )}
          </Box>
        </Container>
      </Box>
    </Layout>
  );
}

export default RentMyCart;

// export const getServerSideProps = wrapper.getServerSideProps(
//   (store) =>
//     async ({ req }) => {
//       try {
//         //Global common fetch
//         const subscriberId = await subscriberFromDomain(
//           req?.headers?.host,
//           store
//         );
//         const { settings, header, footer } = await pageSetup(
//           subscriberId,
//           store
//         );

//         // Page specific fetch
//         const pageInfo = {
//           pageName: 'Add to cart',
//           pageTitle: 'Add to cart',
//           _id: 'rent product-101',
//           meta: {
//             description: 'Add to cart details',
//             keywords: 'Add to cart details',
//             // image: '/images/rentmyog.png',
//             image: {
//               contentType: 'image/png',
//               filePath: 'images/rentmyog.png',
//               pageType: 'rentmy',
//             },
//           },
//         };
//         store.dispatch(metaBuilder({ pageInfo }));

//         return {
//           props: {
//             headerData: header,
//             footerData: footer,
//           },
//         };
//       } catch (error) {
//         console.error(error);
//         return {
//           props: {
//             headerData: null,
//             footerData: null,
//           },
//           notFound: true,
//         };
//       }
//     }
// );
