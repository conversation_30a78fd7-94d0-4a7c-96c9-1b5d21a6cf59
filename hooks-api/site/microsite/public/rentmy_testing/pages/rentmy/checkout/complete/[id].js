import {
  Alert,
  AlertIcon,
  Box,
  Button,
  Center,
  CloseButton,
  Container,
  Image,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import Layout from 'components/layout/layout';
import { completeOrder } from 'components/main/rentmy/service';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { pageSetup, subscriberFromDomain } from 'service/initialSetting';
import { metaBuilder } from 'store/slices/metaSlice';
import { subscriberData } from 'store/slices/subscriberSlice';
import { wrapper } from 'store/store';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyCheckoutSuccess({ headerData, footerData }) {
  const [order, setOrder] = useState(null);
  const router = useRouter();
  const { id } = router.query;
  // const { id: subscriberId } = useSelector(subscriberData);
  const subscriberId = '636a25eb5d1fa80f73143ea4'


  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
        '/products/' +
        rentMyStoreId +
        '/' +
        product.id +
        '/' +
        product.images[0]?.image_small
        : '/static/media/product-image-placeholder.jpg';
      return src;
    },
    [order]
  );

  function getTwoDecimal(x) {
    return Number.parseFloat(x).toFixed(2);
  }

  useEffect(() => {
    if (!router.isReady) {
      return;
    }

    completeOrder(id, subscriberId)
      .then((response) => {
        if (response.data.result.hasOwnProperty('data')) {
          setOrder(response.data.result.data);
        }
      })
      .catch((error) => console.log(error.message));
  }, [router.isReady]);

  return (
    <Layout header={headerData} footer={footerData}>

      <Box textAlign={'center'} my={5}>
        <Text fontSize={35} mt={2}>Order placed successfully</Text>
        <Button
          onClick={() => router.push('/')}
        >
          Continue Shopping
        </Button>
      </Box>

      <Box bg="gray.100" mb={10}>
        <Container maxW={1240} py={4}>
          <Box bg="white" rounded={8} p={8}>
            <Box display="flex" bg="gray.100" mb={4}>
              <Image
                src={
                  'https://b1547765.smushcdn.com/1547765/wp-content/uploads/2019/11/logo.png?lossy=1&strip=1&webp=1'
                }
                alt='rentmy logo'
                width="150px"
                mr={4}
              />
              <Center>
                <Text
                  fontSize="1.4rem"
                  textTransform="uppercase"
                  fontWeight={600}
                  my={2}
                >
                  Checkout
                </Text>
              </Center>
            </Box>
            <Alert status="success" mb={4}>
              <AlertIcon />
              Thank you for your order.
              <CloseButton position="absolute" right="8px" top="8px" />
            </Alert>
            <Box display="flex" justifyContent="space-between">
              <Box
                flex="0 0"
                flexBasis="auto"
                width={['100%', '100%', '60%', '60%']}
              >
                <Table size="sm">
                  <Thead>
                    <Tr>
                      <Th></Th>
                      <Th>Product</Th>
                      <Th>Unit Price</Th>
                      <Th>Quantity</Th>
                      <Th>Sub Total</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {order &&
                      order?.order_items &&
                      order?.order_items.map((item) => (
                        <Tr key={item.id}>
                          <Td>
                            <Image
                              src={getImage(item.product)}
                              alt='product image'
                              width="60px"
                              height="auto"
                              priority
                            />
                          </Td>
                          <Td>
                            <Text>{item.product.name}</Text>
                          </Td>
                          <Td>
                            <Text as="span" fontSize=".8rem" color="gray.500">
                              ${item.price}
                            </Text>
                          </Td>
                          <Td>
                            <Text
                              as="span"
                              fontSize=".8rem"
                              color="gray.500"
                              mr={4}
                            >
                              {item.quantity}
                            </Text>
                          </Td>
                          <Td>
                            <Text
                              as="span"
                              fontSize=".8rem"
                              color="gray.500"
                              mr={4}
                            >
                              ${item.sub_total}
                            </Text>
                          </Td>
                        </Tr>
                      ))}
                  </Tbody>
                </Table>
              </Box>
              <Box
                flex="0 0"
                flexBasis="auto"
                width={['100%', '100%', '36%', '36%']}
              >
                {order && (
                  <Table size="sm" variant="striped">
                    <Tbody>
                      {order ? (
                        <>
                          <Tr>
                            <Td fontWeight={700}>Subtotal</Td>
                            <Td>${order?.sub_total}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Deposit Amount</Td>
                            <Td>${order?.deposit_amount}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Tax</Td>
                            <Td>{order?.tax?.length ? '$0' : '$0'}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Shipping Charge</Td>
                            <Td>${getTwoDecimal(order?.delivery_charge)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontWeight={700}>Delivery Tax</Td>
                            <Td>${getTwoDecimal(order?.delivery_tax)}</Td>
                          </Tr>
                          <Tr>
                            <Td fontSize="1.1rem" fontWeight={700}>
                              Total
                            </Td>
                            <Td fontSize="1.1rem">
                              ${getTwoDecimal(order?.total)}
                            </Td>
                          </Tr>
                        </>
                      ) : null}
                    </Tbody>
                  </Table>
                )}
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>
    </Layout>
  );
}

export default RentMyCheckoutSuccess;

// export const getServerSideProps = wrapper.getServerSideProps(
//   (store) =>
//     async ({ req }) => {
//       try {
//         //Global common fetch
//         const subscriberId = await subscriberFromDomain(
//           req?.headers?.host,
//           store
//         );
//         const { settings, header, footer } = await pageSetup(
//           subscriberId,
//           store
//         );

//         // Page specific fetch
//         const pageInfo = {
//           pageName: 'Checkout complete',
//           pageTitle: 'Checkout complete',
//           _id: 'rent product-101',
//           meta: {
//             description: 'Checkout complete',
//             keywords: 'Checkout complete',
//             // image: '/images/rentmyog.png',
//             image: {
//               contentType: 'image/png',
//               filePath: 'images/rentmyog.png',
//               pageType: 'rentmy',
//             },
//           },
//         };
//         store.dispatch(metaBuilder({ pageInfo }));

//         return {
//           props: {
//             headerData: header,
//             footerData: footer,
//           },
//         };
//       } catch (error) {
//         console.error(error);
//         return {
//           props: {
//             headerData: null,
//             footerData: null,
//           },
//           notFound: true,
//         };
//       }
//     }
// );
