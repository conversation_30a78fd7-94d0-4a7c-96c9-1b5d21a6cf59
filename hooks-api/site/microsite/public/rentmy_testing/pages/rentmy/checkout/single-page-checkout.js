import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import Layout from 'components/layout/layout';
import Loader from 'components/loader/Loader';
import { getGetwaySettings } from '@/components/main/rentmy/service';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
//import RentMyCheckout from 'components/main/rentmy/RentMyCheckout';
const RentMyCheckout = dynamic(() =>
  import('components/main/rentmy/RentMyCheckout')
);

function RentMyCheckoutWrapper({ headerData, footerData }) {
  const [stripePromise, setStripePromise] = useState(null);
  const [loading, setLoading] = useState(true);

  // const { id: subscriberId } = useSelector(subscriberData);

  const subscriberId = '636a25eb5d1fa80f73143ea4'


  useEffect(() => {
    getSettings()
  }, []);
  
  
  //Don't delete this. This chunk of code need to implment stripe
  const getSettings = () => {
    getGetwaySettings(subscriberId)
      .then((response) => {
        if (response.data.result.hasOwnProperty('data')) {
          const { data: getways } = response.data.result;
          const cardGetway = getways.find((item) => item.name === 'Stripe');
          if (cardGetway) {
            // const stripePromiseData = loadStripe('pk_test_51NdGdWJqnOVwQ3ld469E8jtOXbjeM1IqKdhjI0z5wPIo3czZNwY6aPxbe24xGLbBEe1e0ZumtylgXnqVFmgENemQ00LIehl6VN'); //cardGetway.config.publishable_key
            const stripePromiseData = loadStripe(cardGetway.config.publishable_key); //cardGetway.config.publishable_key
            setStripePromise(stripePromiseData);
          }
        }
        setLoading(false);
      })
      .catch((error) => {
        console.log(error.message);
        setLoading(false);
      });
  }

  if (loading) {
    return (
      <Layout header={headerData} footer={footerData}>
        <Loader />
      </Layout>
    );
  }
  //Don't delete this. ==== <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
  // <Elements stripe={stripePromise} key={stripePromise}>
  //               <RentMyCheckout />
  //           </Elements>
  return (
    <Layout header={headerData} footer={footerData}>
      {/* <RentMyCheckout subscriberId={subscriberId} /> */}
      <Elements stripe={stripePromise} key={stripePromise}>
        <RentMyCheckout subscriberId={subscriberId} />
      </Elements>
    </Layout>
  );
}

export default RentMyCheckoutWrapper;

// export const getServerSideProps = wrapper.getServerSideProps(
//   (store) =>
//     async ({ req }) => {
//       try {
//         //Global common fetch
//         const subscriberId = await subscriberFromDomain(
//           req?.headers?.host,
//           store
//         );
//         const { settings, header, footer } = await pageSetup(
//           subscriberId,
//           store
//         );

//         // Page specific fetch
//         const pageInfo = {
//           pageName: 'Checkout',
//           pageTitle: 'Checkout',
//           _id: 'rent product-101',
//           meta: {
//             description: 'checkout product',
//             keywords: 'checkout product',
//             // image: '/images/rentmyog.png',
//             image: {
//               contentType: 'image/png',
//               filePath: 'images/rentmyog.png',
//               pageType: 'rentmy',
//             },
//           },
//         };
//         store.dispatch(metaBuilder({ pageInfo }));

//         return {
//           props: {
//             headerData: header,
//             footerData: footer,
//           },
//         };
//       } catch (error) {
//         console.error(error);
//         return {
//           props: {
//             headerData: null,
//             footerData: null,
//           },
//           notFound: true,
//         };
//       }
//     }
// );
