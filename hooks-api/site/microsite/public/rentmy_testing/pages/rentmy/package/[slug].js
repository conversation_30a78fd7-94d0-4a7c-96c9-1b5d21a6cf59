import {
  Box,
  Button,
  Container,
  HStack,
  Image,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Text,
  useNumberInput,
  useToast,
} from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import { useCallback, useEffect, useRef, useState } from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import { BiChevronRightSquare } from 'react-icons/bi';
const DatePicker = dynamic(() => import('react-datepicker'));
//import './Datepicker.css';
import axios from 'axios';
import Layout from 'components/layout/layout';
import Loader from 'components/loader/Loader';
import {
  addToCartForPackage,
  getDatePriceDuration,
  getPackagePriceValueForRent,
} from 'components/main/rentmy/service';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { getRentMyToken } from 'service/rentmyAuth';
import { incrementCartItem } from 'store/slices/rentmySlice';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyPackageDetails({ headerData, footerData }) {
  const toast = useToast();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [isAddToCartBtnDisable, setIsAddToCartBtnDisable] = useState(false);
  const router = useRouter();
  const { slug } = router.query;
  const packageRef = useRef(null);
  let isApply = useRef(false);

  // const { id: subscriberId } = useSelector(subscriberData);

  const subscriberId = '636a25eb5d1fa80f73143ea4'


  // Product states
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [pickUpDate, setPickupDate] = useState(new Date());
  const [product, setProduct] = useState(null);
  const [rentalType, setRentalType] = useState({
    buy: false,
    rent: false,
    type: '',
    base: null,
  });
  const [isRentOption, setIsRentOption] = useState(true);
  const [rentOptions, setRentOption] = useState([]);
  const [selectedRentOption, setSelectedRentOption] = useState(null);
  const [available, setAvailable] = useState(0);
  const [productPrice, setProductPrice] = useState(null);
  const [itemNumber, setItemNumber] = useState(1);
  const [variants, setVariants] = useState([]);

  const { getInputProps, getIncrementButtonProps, getDecrementButtonProps } =
    useNumberInput({
      step: 1,
      defaultValue: 1,
      min: 1,
      max: available,
      onChange: (value) => itemNumberChangHandler(value),
    });
  const inc = getIncrementButtonProps();
  const dec = getDecrementButtonProps();
  const input = getInputProps({ readOnly: true });

  const itemNumberChangHandler = (value) => {
    const itemValue = parseInt(value);
    setItemNumber(itemValue);
  };

  // init rental type "buy" or "rent"
  const initRentType = (price) => {
    const priceKeys = Object.keys(price);
    if (priceKeys.length > 1 && price.base !== '') {
      setRentalType({
        buy: true,
        rent: true,
        type: 'rent',
        base: price.base,
      });
    }
    if (priceKeys.length > 1 && price.base === '') {
      setRentalType({
        buy: false,
        rent: true,
        type: 'rent',
        base: price.base,
      });
    }
    if (priceKeys.length === 1 && price.base !== '') {
      setRentalType({
        ...rentalType,
        buy: true,
        type: 'buy',
        base: price.base,
      });
    }
  };

  // Init Prices
  const initRentOption = async (price) => {
    const newPrices = { ...price };
    delete newPrices.base;
    const priceKeys = Object.keys(newPrices);
    let options = [];
    priceKeys.forEach((item) => {
      options.push(...price[item]);
    });

    if (options.length) {
      setRentOption(options);
      setSelectedRentOption(options[0].id);
      setProductPrice(options[0].price);
    }
  };

  // Initialization variants
  const initVariantsOfProduct = (products) => {
    const variantProducts = [];
    products.forEach((product, index) => {
      const item = {
        product_id: product.id,
        quantity: product.quantity,
        available: 1,
        variants_products_id: product.variants[0].id,
        product_index: index,
      };
      variantProducts.push(item);
    });
    setVariants(variantProducts);
  };

  const rentalTypeChangeHandler = (value) => {
    setRentalType({
      ...rentalType,
      type: value,
    });
  };

  const rentOptionChangeHandler = (value) => {
    const intValue = parseInt(value);
    setSelectedRentOption(intValue);
  };

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
          '/products/' +
          rentMyStoreId +
          '/' +
          product.id +
          '/' +
          product.images[0]?.image_small
        : '/static/media/product-image-placeholder.jpg';
      return src;
    },
    [product]
  );

  // Accurate round price number
  const get2DecPoint = useCallback((number = 0.0) => {
    return number.toLocaleString('en-US', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }, []);

  // Add to cart
  const addToCartHandler = async () => {
    setIsAddToCartBtnDisable(true);
    const rent = rentOptions.find((option) => option.id === selectedRentOption);
    addToCartForPackage(
      packageRef.current,
      variants,
      rentalType,
      rent,
      itemNumber,
      isApply.current,
      startDate,
      endDate,
      pickUpDate,
      subscriberId
    )
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          dispatch(incrementCartItem(1));
          setIsRentOption(false);
          localStorage.setItem('rentMyCartToken', result.data.token);
          localStorage.setItem('rentMyUserCart', JSON.stringify(result.data));

          router.push(`/rentmy/cart/${result.data.token}`);
          toast({
            title: 'Success',
            description: 'Item added to cart successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }
        setIsAddToCartBtnDisable(false);
      })
      .catch((error) => {
        console.log(error.message);
        setIsAddToCartBtnDisable(false);
        toast({
          title: 'Cannot add to cart',
          status: 'error',
          duration: 4000,
          isClosable: true,
        });
      });
  };

  const fetchData = async () => {
    setLoading(true);
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    const tokenObject = await getRentMyToken(subscriberId);
    let apiUrl = `/package-details/${slug}/360?location=${tokenObject.locationId}&token=`;

    if (tokenObject.success) {
      axios
        .request({
          url: apiUrl,
          method: 'GET',
          baseURL: apiBaseURL,
          headers: {
            Authorization: `Bearer ${tokenObject.token}`,
            location: tokenObject.locationId,
          },
        })
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            setProduct(result.data);
            let priceObject = result.data.price[0];
            initVariantsOfProduct(result.data.products);
            initRentType(priceObject, result.data.buy_price);
            initRentOption(priceObject);
            setAvailable(result.data.available);
            packageRef.current = result.data;
          }
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  };

  useEffect(() => {
    const rentMyUserCart = localStorage.getItem('rentMyUserCart');
    if (rentMyUserCart) {
      const cart = JSON.parse(rentMyUserCart);
      if (cart?.cart_items.length) {
        setIsRentOption(false);
      } else {
        setIsRentOption(true);
      }
    }
    fetchData();
  }, [slug]);

  useEffect(() => {
    isApply.current = false;
    if (rentalType.type === 'buy') {
      const { price } = packageRef.current;
      if (price[0].base) {
        setProductPrice(price[0].base.price);
      }
    }
  }, [rentalType]);

  useEffect(() => {
    isApply.current = false;
    if (rentOptions.length && rentalType.type === 'rent') {
      const rentOption = rentOptions.find(
        (option) => option.id === selectedRentOption
      );
      getDatePriceDuration(rentOption.rent_start, rentOption.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            getPackagePriceValueForRent(
              result,
              packageRef.current,
              rentOption,
              itemNumber,
              subscriberId
            )
              .then((response) => {
                // Check later
                setProductPrice(response.data.result.data);
              })
              .catch((error) => error.message);

            setStartDate(new Date(result.data.start_date));
            setEndDate(new Date(result.data.end_date));
          }
        })
        .catch((error) => console.log(error.message));
    }
  }, [selectedRentOption, rentalType]);

  useEffect(() => {
    isApply.current = true;
    if (rentOptions.length && rentalType.type === 'rent') {
      const rentOption = rentOptions.find(
        (option) => option.id === selectedRentOption
      );
      getDatePriceDuration(pickUpDate, rentOption.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            setStartDate(new Date(result.data.start_date));
            setEndDate(new Date(result.data.end_date));
            getPackagePriceValueForRent(
              result,
              packageRef.current,
              rentOption,
              itemNumber,
              true,
              subscriberId
            )
              .then((response) => {
                // Check later
                // console.log({ response: response.data.result.data });
                setProductPrice(response.data.result.data);
              })
              .catch((error) => error.message);
          }
        })
        .catch((error) => console.log(error.message));
    }
  }, [pickUpDate]);

  if (loading) {
    return (
      <Layout header={headerData} footer={footerData}>
        <Loader />
      </Layout>
    );
  }

  return (
    <>
      <Layout header={headerData} footer={footerData}>
        {loading ? (
          'Loading...'
        ) : product ? (
          <Box bg="gray.100">
            <Container maxW={1240} py={4}>
              <Box display="flex" p={5} flexWrap="wrap" bg="white" rounded={8}>
                <Box flex="1 1" flexBasis="40%">
                  <Image src={getImage(product)} width="100%" height="auto" alt='product image' priority />
                </Box>
                <Box
                  flex="1 1"
                  flexBasis={['100%', '60%']}
                  px={['0px', '20px', '20px', '40px']}
                  py={4}
                >
                  <Text fontSize="1.4rem" fontWeight={700} mb={2}>
                    {product.name}
                  </Text>
                  <Text fontSize="2.6rem" fontWeight={300} mb={4}>
                    ${get2DecPoint(productPrice * itemNumber)} USD
                  </Text>

                  <Box display="flex" flexWrap="wrap">
                    <Box flex="3 1">
                      <RadioGroup
                        mb={4}
                        colorScheme="gray"
                        value={rentalType.type}
                        onChange={rentalTypeChangeHandler}
                      >
                        <Stack direction="row" spacing="20px">
                          {rentalType.buy ? (
                            <Radio value="buy" cursor="pointer">
                              <Text
                                cursor="pointer"
                                as="span"
                                fontWeight={700}
                                fontSize="1.1rem"
                              >
                                Buy
                              </Text>
                            </Radio>
                          ) : null}
                          {rentalType.rent ? (
                            <Radio value="rent" cursor="pointer">
                              <Text
                                cursor="pointer"
                                as="span"
                                fontWeight={700}
                                fontSize="1.1rem"
                              >
                                Rent
                              </Text>
                            </Radio>
                          ) : null}
                        </Stack>
                      </RadioGroup>

                      {rentalType.type === 'rent' ? (
                        isRentOption ? (
                          <RadioGroup
                            size="md"
                            mb={4}
                            value={selectedRentOption}
                            onChange={rentOptionChangeHandler}
                          >
                            <Stack
                              direction="column"
                              spacing="8px"
                              cursor="pointer"
                            >
                              {rentOptions.map((option) => (
                                <Radio
                                  value={option.id}
                                  key={option.id}
                                  cursor="pointer"
                                >
                                  <Text
                                    cursor="pointer"
                                    as="span"
                                    fontWeight={300}
                                    color="black"
                                    fontFamily="Open Sans"
                                    fontSize="1.1rem"
                                  >
                                    ${option.price} USD / {option.duration}{' '}
                                    {option.label}
                                  </Text>
                                </Radio>
                              ))}
                            </Stack>
                          </RadioGroup>
                        ) : (
                          <Stack direction="column" spacing="8px" mb={4}>
                            {rentOptions.map((option) => (
                              <Box value={option.id} key={option.id}>
                                <Text
                                  as="span"
                                  fontWeight={500}
                                  color="black"
                                  fontSize="1rem"
                                  width={'100%'}
                                  display={'flex'}
                                  alignItems={'center'}
                                  gap={'10px'}
                                >
                                  <BiChevronRightSquare /> ${option.price} USD /{' '}
                                  {option.duration} {option.label}
                                </Text>
                              </Box>
                            ))}
                          </Stack>
                        )
                      ) : null}

                      <Box mb={10}>
                        <HStack maxW="220px">
                          <Button
                            {...dec}
                            size="sm"
                            colorScheme="blue"
                            rounded={4}
                          >
                            -
                          </Button>
                          <Input {...input} size="sm" readOnly />
                          <Button
                            {...inc}
                            size="sm"
                            colorScheme="blue"
                            rounded={4}
                          >
                            +
                          </Button>
                        </HStack>
                        <Text as="span" fontSize=".9rem" fontWeight={300}>
                          Available: {available - itemNumber}
                        </Text>
                      </Box>

                      {rentalType.type === 'rent' ? (
                        <Box mb={8} maxW="220px">
                          <Text mb={2} fontWeight={700} fontSize="1rem">
                            Pick your delivery date
                          </Text>
                          <DatePicker
                            disabled={!isRentOption}
                            showTimeSelect
                            selected={startDate}
                            startDate={startDate}
                            endDate={endDate}
                            dateFormat="d MMM, yyyy h:mm aa"
                            onChange={(date) => setPickupDate(date)}
                          />
                        </Box>
                      ) : null}
                      <Button
                        mb={4}
                        disabled={
                          rentalType.type == '' ||
                          available == 0 ||
                          isAddToCartBtnDisable
                        }
                        colorScheme="blue"
                        rounded={4}
                        fontWeight={700}
                        size="sm"
                        onClick={addToCartHandler}
                        isLoading={isAddToCartBtnDisable}
                      >
                        ADD TO CART
                      </Button>
                    </Box>
                    <Box
                      flex="1 1"
                      alignSelf="flex-start"
                      borderWidth={1}
                      p={4}
                      rounded={8}
                      shadow="lg"
                    >
                      <Text fontSize="1.1rem" fontWeight={700} mb={2}>
                        Package Includes:
                      </Text>
                      {packageRef.current &&
                        packageRef.current.products.map((product) => (
                          <Box
                            key={product.id}
                            fontSize=".9rem"
                            fontWeight={500}
                            borderBottomWidth={1}
                          >
                            <Text py={1}>
                              {product.name} ({product.quantity})
                            </Text>
                          </Box>
                        ))}
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Container>
          </Box>
        ) : null}
      </Layout>
    </>
  );
}

export default RentMyPackageDetails;

// export const getServerSideProps = wrapper.getServerSideProps(
//   (store) =>
//     async ({ req }) => {
//       try {
//         //Global common fetch
//         const subscriberId = await subscriberFromDomain(
//           req?.headers?.host,
//           store
//         );
//         const { settings, header, footer } = await pageSetup(
//           subscriberId,
//           store
//         );

//         // Page specific fetch
//         const pageInfo = {
//           pageName: 'Package details',
//           pageTitle: 'Package details',
//           _id: 'rent package-101',
//           meta: {
//             description: 'Rentmy package details',
//             keywords: 'Rentmy package details',
//             // image: '/images/rentmyog.png',
//             image: {
//               contentType: 'image/png',
//               filePath: 'images/rentmyog.png',
//               pageType: 'rentmy',
//             },
//           },
//         };
//         store.dispatch(metaBuilder({ pageInfo }));

//         return {
//           props: {
//             headerData: header,
//             footerData: footer,
//           },
//         };
//       } catch (error) {
//         // console.error(error);
//         return {
//           props: {
//             headerData: null,
//             footerData: null,
//           },
//           notFound: true,
//         };
//       }
//     }
// );
