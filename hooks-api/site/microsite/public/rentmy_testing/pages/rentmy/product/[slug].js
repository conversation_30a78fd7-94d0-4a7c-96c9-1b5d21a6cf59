import {
  Box,
  Button,
  Container,
  HStack,
  Image,
  Input,
  Radio,
  RadioGroup,
  Select,
  Stack,
  Text,
  useNumberInput,
  useToast,
} from '@chakra-ui/react';
import dynamic from 'next/dynamic';
import { useCallback, useEffect, useRef, useState } from 'react';
import 'react-datepicker/dist/react-datepicker.css';
import { BiChevronRightSquare } from 'react-icons/bi';
const DatePicker = dynamic(() => import('react-datepicker'));
// import 'components/main/rentmy/Datepicker.css';
import {
  addToCart,
  getDatePriceDuration,
  getPathOfChain,
  getPriceValueForBuy,
  getPriceValueForRent,
  getVariantChain,
} from '@/components/main/rentmy/service';
import axios from 'axios';
import Layout from 'components/layout/layout';
import Loader from 'components/loader/Loader';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { incrementCartItem } from 'store/slices/rentmySlice';

const mediaUrl = 'https://s3.us-east-2.amazonaws.com/images.rentmy.co';

function RentMyProductDetails({ headerData, footerData }) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { slug } = router.query;
  let productRef = useRef(null);
  let isApply = useRef(false);
  const toast = useToast();

  const dispatch = useDispatch();
  // const { id: subscriberId } = useSelector(subscriberData);

  const subscriberId = '636a25eb5d1fa80f73143ea4'

  // Product states
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [pickUpDate, setPickupDate] = useState(new Date());
  const [product, setProduct] = useState(null);
  const [rentalType, setRentalType] = useState({
    buy: false,
    rent: false,
    type: '',
    base: null,
  });
  const [isRentOption, setIsRentOption] = useState(true);
  const [isAddToCartBtnDisable, setIsAddToCartBtnDisable] = useState(false);
  const [rentOptions, setRentOption] = useState([]);
  const [selectedRentOption, setSelectedRentOption] = useState(null);
  const [available, setAvailable] = useState(0);
  const [productPrice, setProductPrice] = useState(null);
  const [itemNumber, setItemNumber] = useState(1);
  const [variants, setVariants] = useState([]);
  const [variantValue, setVariantValue] = useState([]);
  const { getInputProps, getIncrementButtonProps, getDecrementButtonProps } =
    useNumberInput({
      step: 1,
      value: itemNumber,
      min: 1,
      max: available,
      onChange: (value) => itemNumberChangHandler(value),
    });

  const inc = getIncrementButtonProps();
  const dec = getDecrementButtonProps();
  const input = getInputProps({ readOnly: true });

  const itemNumberChangHandler = (value) => {
    const itemValue = parseInt(value);
    setItemNumber(itemValue);
  };

  // init rental type "buy" or "rent"
  const initRentType = (price) => {
    // console.log('price');
    // console.log(price);
    const priceKeys = Object.keys(price);
    if (priceKeys.length > 1 && price.base !== '') {
      setRentalType({
        buy: true,
        rent: true,
        type: 'rent',
        base: price.base,
      });
    }
    if (priceKeys.length > 1 && price.base === '') {
      setRentalType({
        buy: false,
        rent: true,
        type: 'rent',
        base: price.base,
      });
    }
    if (priceKeys.length === 1 && price.base !== '') {
      setRentalType({
        ...rentalType,
        buy: true,
        type: 'buy',
        base: price.base,
      });
    }
  };

  // Init Prices
  const initRentOption = async (price) => {
    const newPrices = { ...price };
    delete newPrices.base;
    const priceKeys = Object.keys(newPrices);
    let options = [];
    // console.log("priceKeys ==> ", priceKeys);

    if (priceKeys?.length == 1 && priceKeys[0] == 'fixed') {
      setRentalType({ ...rentalType, buy: true, rent: false, type: 'buy' });
      return
    }

    priceKeys.forEach((item) => {
      options.push(...price[item]);
    });

    if (options.length) {
      setRentOption(options);
      setSelectedRentOption(options[0].id);
      setProductPrice(options[0].price);
    }
  };

  const rentalTypeChangeHandler = (value) => {
    setRentalType({
      ...rentalType,
      type: value,
    });
  };

  const rentOptionChangeHandler = (value) => {
    const intValue = parseInt(value);
    setSelectedRentOption(intValue);
  };

  const getImage = useCallback(
    (product) => {
      const rentMyStoreId = localStorage.getItem('rentMystrId');
      const src = product.images.length
        ? mediaUrl +
        '/products/' +
        rentMyStoreId +
        '/' +
        product.id +
        '/' +
        product.images[0]?.image_small
        : '/static/media/product-image-placeholder.jpg';
      return src;
    },
    [product]
  );

  // Accurate round price number
  const get2DecPoint = useCallback((number = 0.0) => {
    return number.toLocaleString('en-US', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }, []);

  // Initialization variants
  const initVariantsOfProduct = (variantSet, variantsList) => {
    const variantSetArray = [];
    const variantSetLength = variantSet.length;
    let variantValuesArray = [];
    if (
      productRef.current.default_variant.variant_chain_name !== 'Unassigned'
    ) {
      variantValuesArray =
        productRef.current.default_variant.variant_chain_id.split('-');
      variantSet.forEach((set, index) => {
        const variantSetIds = variantsList.filter(
          (v) => v.variant_set_id === set.id
        );
        variantSetArray.push({
          id: set.id,
          name: set.name,
          values: variantSetIds,
          optionValue: variantValuesArray[index],
          handleVariant:
            variantSetLength - 1 === index ? 'path-of-chain' : 'variant-chain',
        });
      });
      setVariantValue(variantValuesArray);
      setVariants(variantSetArray);
    }
  };

  // Get Variant chain
  const handleVariantChain = async (value, setId, setIndex) => {
    if (!value || value === 'DEFAULT') return;
    let variantChain = '';

    if (setIndex > 0) {
      const newVariantValue = [...variantValue];
      const variantChainArray = [];
      for (let index = 0; index < setIndex; index++) {
        variantChainArray[index] = newVariantValue[index];
      }
      variantChain = variantChainArray.join();
    }

    const params = {
      product_id: productRef.current.id,
      variant_id: value,
      variant_chain: variantChain,
      rent_type: rentalType.type,
    };

    getVariantChain(params, subscriberId)
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          const newVariantValue = [...variantValue];
          newVariantValue[setIndex] = value;

          const extraItem = { id: 'DEFAULT', name: 'Select One' };
          const newVariantOptions = [extraItem, ...result.data];

          const newVariants = [...variants];
          newVariants[setIndex].optionValue = value;

          const updateItemIndex = setIndex + 1;
          newVariants[updateItemIndex].values = newVariantOptions;
          newVariants[updateItemIndex].optionValue = 'DEFAULT';
          if (updateItemIndex + 1 <= newVariants.length) {
            for (
              let index = updateItemIndex + 1;
              index < newVariants.length;
              index++
            ) {
              newVariants[index].values = [
                { id: 'DEFAULT', name: 'Select One' },
              ];
              newVariants[index].optionValue = 'DEFAULT';
            }
          }
          setVariantValue(newVariantValue);
          setVariants(newVariants);
        }
      })
      .catch((error) => console.log(error.message));
  };

  // Get path of chain
  const handlePathOfChain = (value, setId, setIndex) => {
    if (!value || value === 'DEFAULT') return;
    let variantChain = '';
    const newVariantValue = [...variantValue];
    newVariantValue[setIndex] = value;

    const newVariants = [...variants];
    newVariants[setIndex].optionValue = value;

    if (variants.length > 1) {
      const variantChainArray = [];
      for (let index = 0; index < setIndex; index++) {
        variantChainArray[index] = newVariantValue[index];
      }
      variantChain = variantChainArray.join();
    }
    setVariantValue(newVariantValue);
    setVariants(newVariants);

    const rentMyCartToken = localStorage.getItem('rentMyCartToken');
    const cartToken = rentMyCartToken ? rentMyCartToken : '';
    const params = {
      product_id: productRef.current.id,
      variant_id: value,
      variant_chain: variantChain,
      rent_type: rentalType.type,
      t: 360,
      token: cartToken,
    };
    getPathOfChain(params, subscriberId)
      .then((response) => {
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          productRef.current = {
            ...productRef.current,
            available: result.data.available,
            cart_available: result.data.cart_available,
            images: result.data.images,
            prices: result.data.prices,
            default_variant: {
              ...product.default_variant,
              barcode: result.data.variant.barcode,
              price_type: result.data.variant.price_type,
              quantity: result.data.variant.quantity,
              variant_chain_id: result.data.variant_chain_id,
              variant_chain_name: result.data.variant_chain_name,
              variants_products_id: result.data.variants_products_id,
            },
          };

          setProduct({
            ...product,
            available: result.data.available,
            cart_available: result.data.cart_available,
            images: result.data.images,
            prices: result.data.prices,
            default_variant: {
              ...product.default_variant,
              barcode: result.data.variant.barcode,
              price_type: result.data.variant.price_type,
              quantity: result.data.variant.quantity,
              variant_chain_id: result.data.variant_chain_id,
              variant_chain_name: result.data.variant_chain_name,
              variants_products_id: result.data.variants_products_id,
            },
          });

          let priceObject = result.data.prices[0];
          if (result.data.rent_type == 'buy') {
            initRentType(priceObject);
          }
          // console.log('priceObject ==>', priceObject)
          initRentOption(priceObject);
          setItemNumber(1);
        }
      })
      .catch((error) => console.log(error.message));
  };

  // Add to cart
  const addToCartHandler = async () => {
    setIsAddToCartBtnDisable(true);
    const rent = rentOptions.find((option) => option.id === selectedRentOption);
    addToCart(
      productRef.current,
      rentalType,
      rent,
      itemNumber,
      isApply.current,
      startDate,
      endDate,
      pickUpDate,
      subscriberId
    )
      .then((response) => {
        // console.log("data",response)
        dispatch(incrementCartItem(1));
        const { result } = response.data;
        // if (result.data) {
        if (result.hasOwnProperty('data')) {
          setIsRentOption(false);
          localStorage.setItem('rentMyCartToken', result.data.token);
          localStorage.setItem('rentMyUserCart', JSON.stringify(result.data));

          router.push(`/rentmy/cart/${result.data.token}`);
          toast({
            title: 'Success',
            description: 'Item added to cart successfully',
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
        }
        setIsAddToCartBtnDisable(false);
      })
      .catch((error) => {
        console.log('error.message', error.message);
        setIsAddToCartBtnDisable(false);
        toast({
          title: 'Cannot add to cart',
          status: 'error',
          duration: 4000,
          isClosable: true,
        });
      });
  };

  // Fetch product details and init
  const fetchData = async () => {
    setLoading(true);
    const apiBaseURL = `https://clientapi.rentmy.co/api`;
    // const tokenObject = await getRentMyToken(subscriberId);
    const tokenObject = {
      "success": true,
      "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNC0wOC0wNyAwNToyMTowOCIsInN0b3JlX2lkIjozNTEsImxvY2F0aW9uIjozOTEsImFwcF9pZCI6Mzc4LCJleHBpcmUiOiIyMDI0LTA4LTA4IDA1OjIxOjAwIiwiaXNfb25saW5lIjowLCJzb3VyY2UiOiJhcGkiLCJkaXNhYmxlX2RlZmF1bHRfdGltZSI6ZmFsc2V9.Nv9_qboMtedRRtEgP7eFl8oMxOVSDPlv9V4rcocZceg",
      "locationId": "391",
      "storeId": "351"
    }
    const rentMyCartToken = localStorage.getItem('rentMyCartToken');
    const cartToken = rentMyCartToken ? rentMyCartToken : '';
    let apiUrl = `/products/${slug}/user/details/360?token=${cartToken}`;

    if (tokenObject.success) {
      axios
        .request({
          url: apiUrl,
          method: 'GET',
          baseURL: apiBaseURL,
          headers: {
            Authorization: `Bearer ${tokenObject.token}`,
            location: tokenObject.locationId,
          },
        })
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            productRef.current = result.data;
            setProduct(result.data);
            let priceObject = result.data.prices[0];
            initRentType(priceObject, result.data.buy_price);
            // console.log('priceObject ==>', priceObject)
            initRentOption(priceObject);
            setAvailable(result.data.available);
            initVariantsOfProduct(
              result.data.variant_set_list,
              result.data.variant_list
            );
          }
        })
        .catch((error) => {
          console.log(error);
        })
        .finally(() => setLoading(false));
    } else {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!router.isReady) {
      return;
    }
    const rentMyUserCart = localStorage.getItem('rentMyUserCart');
    if (rentMyUserCart) {
      const cart = JSON.parse(rentMyUserCart);
      if (cart?.cart_items.length) {
        setIsRentOption(false);
      } else {
        setIsRentOption(true);
      }
    }
    fetchData();
  }, [slug, router.isReady]);

  useEffect(() => {
    isApply.current = false;
    if (rentalType.type === 'buy') {
      getPriceValueForBuy(productRef.current, itemNumber, subscriberId)
        .then((res) => {
          setProductPrice(res.data.result.data);
          setAvailable(res.data.result.available);
        })
        .catch((error) => console.log(error.message));
    }

    if (rentOptions.length && rentalType.type === 'rent') {
      const rentOption = rentOptions.find(
        (option) => option.id === selectedRentOption
      );
      getDatePriceDuration(rentOption.rent_start, rentOption.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            setStartDate(new Date(result.data.start_date));
            setEndDate(new Date(result.data.end_date));
            getPriceValueForRent(
              response.data.result,
              product,
              rentOption,
              itemNumber,
              subscriberId
            )
              .then((response) => {
                setAvailable(response.data.result.available);
                setProductPrice(response.data.result.data);
              })
              .catch((error) => error.message);
          }
        })
        .catch((error) => console.log(error.message));
    }
  }, [rentalType]);

  useEffect(() => {
    isApply.current = false;
    if (rentOptions.length && rentalType.type === 'rent') {
      const rentOption = rentOptions.find(
        (option) => option.id === selectedRentOption
      );
      getDatePriceDuration(rentOption.rent_start, rentOption.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            setStartDate(new Date(result.data.start_date));
            setEndDate(new Date(result.data.end_date));
            getPriceValueForRent(
              response.data.result,
              product,
              rentOption,
              itemNumber,
              subscriberId
            )
              .then((response) => {
                setAvailable(response.data.result.available);
                setProductPrice(response.data.result.data);
              })
              .catch((error) => error.message);
          }
        })
        .catch((error) => console.log(error.message));
    }
  }, [selectedRentOption]);

  useEffect(() => {
    isApply.current = true;
    if (rentOptions.length && rentalType.type === 'rent') {
      const rentOption = rentOptions.find(
        (option) => option.id === selectedRentOption
      );
      getDatePriceDuration(pickUpDate, rentOption.id, subscriberId)
        .then((response) => {
          const { result } = response.data;
          if (result.hasOwnProperty('data')) {
            setStartDate(new Date(result.data.start_date));
            setEndDate(new Date(result.data.end_date));
            getPriceValueForRent(
              response.data.result,
              product,
              rentOption,
              itemNumber,
              true,
              subscriberId
            )
              .then((response) => {
                setAvailable(response.data.result.available);
                setProductPrice(response.data.result.data);
              })
              .catch((error) => error.message);
          }
        })
        .catch((error) => console.log(error.message));
    }
  }, [pickUpDate]);

  if (loading) {
    return (
      <Layout header={headerData} footer={footerData} loader={true}>
        <Loader />
      </Layout>
      // <></>
    );
  }

  return (
    <>
      <Layout header={headerData} footer={footerData}>

        <Box textAlign={'center'} my={5}>
          <Text fontSize={35} mt={2}>Product Details</Text>
          <Button
            onClick={() => router.push('/')}
          >
            See Products list
          </Button>
        </Box>

        {product ? (
          <Box bg="gray.100">

            <Container maxW={1240} py={4}>
              <Box display="flex" p={5} flexWrap="wrap" bg="white" rounded={8}>
                <Box flex="1 1" flexBasis="40%">
                  <Image src={getImage(product)} width="100%" height="auto" alt='product image' priority/>
                </Box>
                <Box
                  flex="1 1"
                  flexBasis={['100%', '60%']}
                  px={['0px', '20px', '20px', '40px']}
                  py={4}
                >
                  <Text fontSize="1.4rem" fontWeight={700} mb={2}>
                    {product.name}
                  </Text>
                  <Text fontSize="2.6rem" fontWeight={300} mb={4}>
                    ${get2DecPoint(productPrice * itemNumber)} USD
                  </Text>

                  <RadioGroup
                    mb={4}
                    colorScheme="gray"
                    value={rentalType.type}
                    onChange={rentalTypeChangeHandler}
                  >
                    <Stack direction="row" spacing="20px">
                      {rentalType.buy ? (
                        <Radio value="buy" cursor="pointer">
                          <Text
                            as="span"
                            fontWeight={700}
                            fontSize="1.1rem"
                            cursor="pointer"
                          >
                            Buy
                          </Text>
                        </Radio>
                      ) : null}
                      {rentalType.rent ? (
                        <Radio value="rent" cursor="pointer">
                          <Text
                            as="span"
                            fontWeight={700}
                            fontSize="1.1rem"
                            cursor="pointer"
                          >
                            Rent
                          </Text>
                        </Radio>
                      ) : null}
                    </Stack>
                  </RadioGroup>

                  {rentalType.type === 'rent' ? (
                    isRentOption ? (
                      <RadioGroup
                        size="md"
                        mb={4}
                        value={selectedRentOption}
                        onChange={rentOptionChangeHandler}
                      >
                        <Stack direction="column" spacing="8px">
                          {rentOptions.map((option) => (
                            <Radio
                              value={option.id}
                              key={option.id}
                              cursor="pointer"
                            >
                              <Text
                                as="span"
                                fontWeight={300}
                                color="black"
                                fontFamily="Open Sans"
                                fontSize="1.1rem"
                              >
                                ${option.price} USD / {option.duration}{' '}
                                {option.label}
                              </Text>
                            </Radio>
                          ))}
                        </Stack>
                      </RadioGroup>
                    ) : (
                      <Stack direction="column" spacing="8px">
                        {rentOptions.map((option) => (
                          <Box value={option.id} key={option.id}>
                            <Text
                              as="span"
                              fontWeight={500}
                              color="black"
                              fontSize="1rem"
                              width={'100%'}
                              display={'flex'}
                              alignItems={'center'}
                              gap={'10px'}
                            >
                              <BiChevronRightSquare /> ${option.price} USD /{' '}
                              {option.duration} {option.label}
                            </Text>
                          </Box>
                        ))}
                      </Stack>
                    )
                  ) : null}

                  <Box mb={8}>
                    {variants.map((set, index) => (
                      <Box key={set.id} maxW="220px" mb={2}>
                        <Text fontSize=".9rem" fontWeight={600}>
                          {set.name}
                        </Text>
                        {set.handleVariant === 'variant-chain' ? (
                          <Select
                            size="xs"
                            value={set.optionValue}
                            onChange={(e) =>
                              handleVariantChain(e.target.value, set.id, index)
                            }
                          >
                            {set.values.map((option) => (
                              <option key={option.id} value={option.id}>
                                {option.name}
                              </option>
                            ))}
                          </Select>
                        ) : (
                          <Select
                            size="xs"
                            value={set.optionValue}
                            onChange={(e) =>
                              handlePathOfChain(e.target.value, set.id, index)
                            }
                          >
                            {set.values.map((option) => (
                              <option key={option.id} value={option.id}>
                                {option.name}
                              </option>
                            ))}
                          </Select>
                        )}
                      </Box>
                    ))}
                  </Box>

                  <Box mb={10}>
                    <HStack maxW="220px">
                      <Button {...dec} size="sm" colorScheme="blue" rounded={4}>
                        -
                      </Button>
                      <Input {...input} size="sm" readOnly />
                      <Button {...inc} size="sm" colorScheme="blue" rounded={4}>
                        +
                      </Button>
                    </HStack>
                    <Text as="span" fontSize=".9rem" fontWeight={300}>
                      Available:{' '}
                      {available - itemNumber < 0 ? 0 : available - itemNumber}
                    </Text>
                  </Box>

                  {rentalType.type === 'rent' ? (
                    <Box mb={8} maxW="220px">
                      <Text mb={2} fontWeight={700} fontSize="1rem">
                        Pick your delivery date
                      </Text>
                      <DatePicker
                        disabled={!isRentOption}
                        showTimeSelect
                        selected={startDate}
                        startDate={startDate}
                        endDate={endDate}
                        dateFormat="d MMM, yyyy h:mm aa"
                        onChange={(date) => setPickupDate(date)}
                      />
                    </Box>
                  ) : null}

                  <Button
                    disabled={
                      rentalType.type == '' ||
                      available == 0 ||
                      available - itemNumber < 0 ||
                      isAddToCartBtnDisable
                    }
                    colorScheme="blue"
                    size="sm"
                    rounded={4}
                    fontWeight={700}
                    onClick={addToCartHandler}
                    isLoading={isAddToCartBtnDisable}
                  >
                    ADD TO CART
                  </Button>
                </Box>
              </Box>
            </Container>
          </Box>
        ) : null}
      </Layout>
    </>
  );
}

export default RentMyProductDetails;


// export const getServerSideProps = wrapper.getServerSideProps(
//   (store) =>
//     async ({ req }) => {
//       try {
//         //Global common fetch
//         const subscriberId = await subscriberFromDomain(
//           req?.headers?.host,
//           store
//         );
//         const { settings, header, footer } = await pageSetup(
//           subscriberId,
//           store
//         );

//         // Page specific fetch
//         const pageInfo = {
//           pageName: 'Product details',
//           pageTitle: 'Product details',
//           _id: 'rent product-101',
//           meta: {
//             description: 'Rentmy product details',
//             keywords: 'Rentmy product details',
//             // image: '/images/rentmyog.png',
//             image: {
//               contentType: 'image/png',
//               filePath: 'images/rentmyog.png',
//               pageType: 'rentmy',
//             },
//           },
//         };
//         store.dispatch(metaBuilder({ pageInfo }));

//         return {
//           props: {
//             headerData: header,
//             footerData: footer,
//           },
//         };
//       } catch (error) {
//         // console.error(error);
//         return {
//           props: {
//             headerData: null,
//             footerData: null,
//           },
//           notFound: true,
//         };
//       }
//     }
// );
