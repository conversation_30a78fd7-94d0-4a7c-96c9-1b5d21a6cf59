const errorInterceptor = (axiosInstance) => {
  axiosInstance.interceptors.response.use(
    (response) => {
      //Response Successful
    },
    (error) => {
      // console.log('in interceptor');
      console.log(error.response.status);
      //   if (error?.status?.code === 401) {
      //   } else {
      //     if (true) {
      //       console.group('Error');
      //       console.log(error);
      //       console.groupEnd();
      //     }
      //   }
    }
  );
};
export default errorInterceptor;
