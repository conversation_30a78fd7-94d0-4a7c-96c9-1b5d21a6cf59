import axios from 'axios';
import { apiBaseUrlV2, baseUrl, basicToken } from 'const/api';
import {
  builder_by_slug_ca_api,
  builder_sa_api,
  global_setting_ca_api,
  global_setting_sa_api,
  page_api
} from 'const/api-endpoint';

export const fetchCABuilder = async (subscriberID) => {
  try {
    //Version 1
    // const builder = await axios(`${builder_api}/${subscriberID}`);
    //Version 2
    const builder = await axios({
      url: `${page_api}/${subscriberID}?adminType=CA&isHomePage=true&builderType=FE_PAGE`,
      method: 'get',
      baseURL: apiBaseUrlV2,
      //headers: { 'Authorization': `Bearer ${token}` }
    });
    return builder;
  } catch (error) {
    throw 'Failed to paint your website.';
  }
};

export const fetchCABlogBuilder = async (subscriberID) => {
  try {
    //Version 1
    // const builder = await axios(`${builder_api}/${subscriberID}`);
    //Version 2
    const builder = await axios({
      url: `${page_api}/${subscriberID}?adminType=CA&isBlogPage=true&builderType=FE_BLOG`,
      method: 'get',
      baseURL: apiBaseUrlV2,
    });
    return builder;
  } catch (error) {
    throw 'Failed to paint your website.';
  }
};

export const fetchCABuilderBySlug = async (subscriberID, pageRoute) => {
  try {
    const builder = await axios({
      url: `${page_api}/${subscriberID}?adminType=CA&slug=${pageRoute}&status=publish`,
      method: 'get',
      baseURL: apiBaseUrlV2,
      //headers: { 'Authorization': `Bearer ${token}` }
    });

    // if (builder.data.data?.status == 'unpublish') {
    //   throw new Error('Page not published');
    // }
    console.log(builder)
    return builder;
  } catch (error) {
    console.log(error);
  }
};

export const fetchSABuilder = async () => {
  try {
    const builder = axios(`${builder_sa_api}`);
    return builder;
  } catch (error) {
    console.log(error);
  }
};

export const fetchCAGlobalSettings = async (subscriberID) => {
  try {
    const settings = await axios(
      `${baseUrl}/api/v2${global_setting_ca_api}&franchiseeId=${subscriberID}&limit=1&fields=theme,shopLayout,pReview,favicon,brand,showShop,showSignIn,active,content,advanceSeoSettings,siteName,seo`,
      { headers: { Authorization: basicToken } }
    );
    return settings;
  } catch (error) {
    //return error;
    throw 'Failed to fetch GS';
  }
};

export const fetchSAGlobalSettings = async () => {
  try {
    const settings = await axios(`${global_setting_sa_api}`);
    return settings;
  } catch (error) {
    console.log(error);
  }
};

export const globalSettingsApiEndpoint = (type, subscriberID) => {
  const settingsAPI =
    type == 'CA'
      ? `${global_setting_ca_api}&franchiseeId=${subscriberID}&limit=1&fields=theme,shopLayout,pReview,favicon,brand,showShop,showSignIn`
      : `${global_setting_sa_api}`;
  return settingsAPI;
};

export const builderApiEndpoint = (type, subscriberID) => {
  // const builderAPI =
  //   type == 'CA' ? `${builder_api}/${subscriberID}` : `${builder_sa_api}`;

  //v2
  const builderAPI = `${page_api}/${subscriberID}?adminType=CA&isHomePage=true&builderType=FE_PAGE`;
  return builderAPI;
};

export const builderBySlugApiEndpoint = (type, subscriberID, slug) => {
  const builderAPI = `${builder_by_slug_ca_api}&franchiseeId=${subscriberID}&slug=${slug}`;
  return builderAPI;
};
