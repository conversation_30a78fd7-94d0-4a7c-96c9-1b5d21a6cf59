import axios from 'axios';
import { apiBaseUrl, baseUrl } from 'const/api';
import { chatConfigApi } from 'const/api-endpoint';

// const chatType = {

// }
export const chatBotConfig = async (subscriberID) => {
  try {
    let api = `${baseUrl}/api/v2${chatConfigApi}?adminType=CA&franchiseeId=${subscriberID}&limit=1`;
    const chatData = await axios(api);
    const chat = chatData.data.data.chatConfig;
    const twak = chat.chats.find((element) => element.chatType == 'TWAKTO' && element?.active);
    const mfb = chat.chats.find((element) => element.chatType == 'MC-FB');
    const mw = chat.chats.find((element) => element.chatType == 'MC-WHATSAPP');
    const mt = chat.chats.find((element) => element.chatType == 'MC-TELEGRAM');
    const manyChat = [mfb, mw, mt];

    return { twakTo: twak, manyChatDetails: manyChat };
  } catch (error) {
    return error;
  }
};
