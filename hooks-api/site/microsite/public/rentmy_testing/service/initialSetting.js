/* eslint-disable no-useless-catch */
import { setGlobalSettings } from 'store/slices/globalSettingSlice';
import { updateHeaderState } from 'store/slices/headerSlice';
import { setMeta } from 'store/slices/metaSlice';
import { setSubscriber } from 'store/slices/subscriberSlice';
import { fetchCAGlobalSettings } from './builder';
import {
  fetchCAFooters,
  fetchCAHeaders,
  validateHeaderContent
} from './navigation';
import { fetchSubscriberID, getSubDomain } from './subscriber';

export const subscriberFromDomain = async (url, store) => {
  try {
    const subdomain = getSubDomain(url);
    const subscriber = await fetchSubscriberID(subdomain);

    if (!subscriber) {
      throw 'Subscriber is undefined.';
    }
    if (!subscriber.data.isValid) {
      throw 'Invalid Domain/SubDomain.';
    }
    const subscriberId = subscriber.data?.franchisee;

    if (store) {

      store.dispatch(setSubscriber(subscriber.data));
    }

    return subscriberId;
  } catch (error) {
    throw error;
  }
};

export const pageSetup = async (subscriberId, store) => {
  try {
    //Global Settings
    const settings = await fetchCAGlobalSettings(subscriberId);
    let meta = null;
    // console.log({ hello: 'world', settings: JSON.stringify(settings?.data) })
    if (settings && settings?.data.data.length != 0) {
      const { theme, showSignIn, logo, favicon, brand, advanceSeoSettings, siteName, } =
        settings?.data.data[0];


      store.dispatch(
        setGlobalSettings({ theme, showSignIn, logo, favicon, brand, advanceSeoSettings, siteName, seo: settings?.data['seo'] })
      );
      //Google Search Console,Google Analytics,Pinterest
      meta = settings?.data?.seo;
      store.dispatch(setMeta(meta));
    }
    //Header/Footer
    const headerRes = await fetchCAHeaders(subscriberId);
    const footerRes = await fetchCAFooters(subscriberId);
    const { data: headerData } = headerRes.data;
    const { data: footerData } = footerRes.data;

    const validatedHeader = headerData?.content
      ? validateHeaderContent(headerData.content)
      : null;
    validatedHeader && store.dispatch(updateHeaderState(validatedHeader));

    const footer = footerData?.content ? footerData : null;
    const header = headerData?.content ? headerData : null;
    // const validatedFooter = footer
    //   ? validateFooter(footer.content)
    //   : null;

    return { settings, header, footer, meta };
  } catch (error) {
    throw 'Failed to fetch your settings.';
  }
};
