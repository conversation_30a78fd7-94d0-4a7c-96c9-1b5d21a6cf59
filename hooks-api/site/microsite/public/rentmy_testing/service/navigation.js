import axios from 'axios';
import {
  footer_ca_api,
  header_ca_api,
  header_sa_api,
  menus_ca_api,
  menus_sa_api,
  page_api,
} from 'const/api-endpoint';
import { apiBaseUrlV2 } from 'const/api';


export const fetchCAMenus = async (subscriberID) => {
  try {
    const menus = await axios(
      `${menus_ca_api}&franchiseeId=${subscriberID}&primaryMenu=true`
    );
    return menus;
  } catch (error) {
    console.log(error);
  }
};

export const fetchSAMenus = async () => {
  try {
    const menus = await axios(`${menus_sa_api}`);
    return menus;
  } catch (error) {
    console.log(error);
  }
};

export const fetchCAHeaders = async (subscriberID) => {
  try {
    // const headers = await axios(`${header_ca_api}/${subscriberID}`);

    const headers = await axios({
      url: `${page_api}/${subscriberID}?adminType=CA&builderType=FE_HEADER&active=true`,
      method: 'get',
      baseURL: apiBaseUrlV2,
      //headers: { 'Authorization': `Bearer ${token}` }
    });
    return headers;
  } catch (error) {
    throw 'Failed to fetch Header.';
  }
};

export const fetchSAHeaders = async () => {
  try {
    const headers = await axios(`${header_sa_api}`);
    return headers;
  } catch (error) {
    console.log(error);
  }
};

export const validateHeaderContent = (content) => {
  const { section, column, component, name } = content;
  const payload = {
    name: name ? name : '',
    section: {
      ids: section && section.ids ? section.ids : [],
      entries: section && section.entries ? section.entries : {},
    },
    column: column ? column : {},
    component: component ? component : {},
  };
  return payload;
};

export const fetchCAFooters = async (subscriberID) => {
  try {
    // const footers = await axios(`${footer_ca_api}/${subscriberID}`);
    // console.log("Footer api==>", `${footer_ca_api}/${subscriberID}`);

    const footers = await axios({
      url: `${page_api}/${subscriberID}?adminType=CA&builderType=FE_FOOTER&active=true`,
      method: 'get',
      baseURL: apiBaseUrlV2,
      //headers: { 'Authorization': `Bearer ${token}` }
    });
    return footers;
  } catch (error) {
    throw 'Failed to fetch Footer.';
  }
};

export const validateFooter = (content) => {
  const { section, column, component, name } = content;
  const payload = {
    name: name ? name : '',
    section: {
      ids: section && section.ids ? section.ids : [],
      entries: section && section.entries ? section.entries : {},
    },
    column: column ? column : {},
    component: component ? component : {},
  };
  return payload;
};

export const headerApiEndpoint = (type, subscriberID) => {
  const headerAPI =
    `${page_api}/${subscriberID}?adminType=CA&builderType=FE_HEADER&active=true`;
  return headerAPI;
};

export const footerApiEndpoint = (type, subscriberID) => {
  const footerAPI = `${footer_ca_api}/${subscriberID}`;
  return footerAPI;
};

export const menusApiEndpoint = (type, subscriberID) => {
  const headerAPI =
    type == 'CA'
      ? `${menus_ca_api}&franchiseeId=${subscriberID}&primaryMenu=true`
      : `${menus_sa_api}`;
  return headerAPI;
};

export const menusFooterApiEndpoint = (type, subscriberID) => {
  const headerAPI =
    type == 'CA'
      ? `${menus_ca_api}&franchiseeId=${subscriberID}&primaryMenu=true`
      : `${menus_sa_api}`;
  return headerAPI;
};
