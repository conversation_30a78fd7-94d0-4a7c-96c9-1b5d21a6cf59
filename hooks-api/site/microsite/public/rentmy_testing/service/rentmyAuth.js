import axios from 'axios';
import { apiBaseUrl } from 'const/api';

export const getRentMyKey = async (franchisee) => {
  const baseURL = apiBaseUrl;
  //const token = localStorage.getItem('token');
  // const token =
  //   'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.STb6X4OShG77fnN1ai3Po08OZ2NDrzcXAV9TUKdC3-I';

  try {
    const response = await axios({
      url: `services?subscriberId=${franchisee}`,
      method: 'GET',
      baseURL: baseURL,
    });
    const { data } = response.data;
    let service = data.find((item) => item.serviceName === 'rentmy');
    return service;
  } catch (error) {
    console.log(error.name + ': ' + error.message);
    return null;
  }
};

export const getRentMyToken = async (franchisee) => {
  const apiBaseURL = `https://clientapi.rentmy.co/api`;
  const rentMyToken = localStorage.getItem('rentMyToken');
  const rentMyStoreLocation = localStorage.getItem('rentMystrLId');
  const rentMyStoreId = localStorage.getItem('rentMystrId');
  if (rentMyToken && rentMyStoreLocation & rentMyStoreId) {
    return {
      success: true,
      token: rentMyToken,
      locationId: rentMyStoreLocation,
      storeId: rentMyStoreId,
    };
  } else {
    try {
      const rentMyKeys = await getRentMyKey(franchisee);
      if (rentMyKeys) {
        const response = await axios({
          url: 'apps/access-token',
          method: 'POST',
          baseURL: apiBaseURL,
          data: {
            api_key: rentMyKeys.apiKey,
            api_secret: rentMyKeys.secretKey,
          },
        });
        const { result } = response.data;
        if (result.hasOwnProperty('data')) {
          localStorage.setItem('rentMystrLId', result.data.location_id);
          localStorage.setItem('rentMystrId', result.data.store_id);
          localStorage.setItem('rentMyToken', result.data.token);
          return {
            success: true,
            token: result.data.token,
            locationId: result.data.location_id,
            storeId: result.data.store_id,
          };
        }
        return {
          sucess: false,
          message: result.message,
        };
      } else {
        throw 'No integration found';
      }
    } catch (error) {
      console.error(error);
      return error;
    }
  }
};
