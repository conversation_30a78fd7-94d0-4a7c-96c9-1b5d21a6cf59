import axios from 'axios';
import { ipV4, siteDomain } from 'const/api';
import { subscriber_api } from 'const/api-endpoint';

export const fetchSubscriberID = async (subdomain) => {
  try {
    //const ip = await getIPv4();
    // const subscriber = await axios(`${subscriber_api}/${subdomain}`, {
    //   headers: {
    //     'X-Real-Ip': ip,
    //     'X-Forwarded-For': ip,
    //   },
    // });
    const subscriber = await axios(`${subscriber_api}/${subdomain}`);
    return subscriber;
  } catch (error) {
    //console.log(error);
    throw 'Invalid Subscriber';
  }
};

export const getSubDomain = (url) => {
  try {
    const host = (url);
    const domains = host?.split('.');
    const initialDomain = domains.find((element) => element == siteDomain);

    if (initialDomain) {
      const subdomain = host?.split('.')[0];
      return subdomain;
    } else {
      return host;
    }
  } catch (error) {
    console.log(error);
  }
};

const checkWWW = (host) => {
  const domains = host?.split('.');
  //const domains = ['www', 'bbn', 'localhost:3001', 'com'];
  const www = domains.shift();
  if (www === 'www') {
    console.log('We found www in url. Removing WWW..');
    let customDomain = '';
    domains.map((element, index) => {
      const dot = index == 0 ? '' : '.';
      customDomain = customDomain + dot + `${element}`;
    });
    return customDomain;
  } else {
    return host;
  }
};

export const getIPv4 = async () => {
  const response = await axios(ipV4);
  return response.data.ip;
};
