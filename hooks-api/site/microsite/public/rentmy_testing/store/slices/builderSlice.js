import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  name: '',
  section: {
    ids: [],
    entries: {},
  },
  column: {},
  component: {},
};

export const builderSlice = createSlice({
  name: 'builder',
  initialState,
  reducers: {
    setBuilder: (state, action) => {
      Object.assign(state, {
        section: action.payload.section || null,
        column: action.payload.column || null,
        component: action.payload.component || null,
        name: action.payload.name || null,
      });
    },
    reset: () => initialState,
  },
});

export const { setBuilder } = builderSlice.actions;

export const builderData = (state) => state.builder;

export default builderSlice.reducer;
