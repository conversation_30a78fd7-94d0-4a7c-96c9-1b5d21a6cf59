import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  settingsId: null,
  showSignIn: true,
  primaryColor: '#2c80b9',
  secondaryColor: '#007A91',
  accentColor: '#2c80b9',
  logo: null,
  favIcon: null,
  brand: null,
  siteFont: null,
  advanceSeoSettings: null,
  siteName: '',
  seo: null
};

export const globalSettingSlice = createSlice({
  name: 'globalSetting',
  initialState,
  reducers: {
    setGlobalSettings: (state, action) => {
      state.showSignIn = action.payload.showSignIn || null;
      state.primaryColor = action.payload.theme?.color?.primary || null;
      state.secondaryColor = action.payload.theme?.color?.secondary || null;
      state.accentColor = action.payload.theme?.color?.accent || null;
      state.logo = action.payload.logo ? action.payload.logo : null;
      state.favIcon = action.payload.favicon ? action.payload.favicon : null;
      state.brand = action.payload.brand ? action.payload.brand : null;
      state.siteFont = action.payload.theme?.font || null;
      state.advanceSeoSettings = action.payload.advanceSeoSettings || null;
      state.siteName = action.payload.siteName ? action.payload.siteName : '';
      state.seo = action.payload.seo ? action.payload.seo : null;
    },
    reset: () => initialState,
  },
});

export const { setGlobalSettings } = globalSettingSlice.actions;

export const globalSettingData = (state) => state.globalSetting;

export default globalSettingSlice.reducer;
