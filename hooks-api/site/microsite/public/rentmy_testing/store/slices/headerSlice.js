import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  name: '',
  section: {
    ids: [],
    entries: {},
  },
  column: {},
  component: {},
};

export const headerSlice = createSlice({
  name: 'header',
  initialState,
  reducers: {
    updateHeaderState: (state, action) => {
      Object.assign(state, {
        section: action.payload.section,
        column: action.payload.column,
        component: action.payload.component,
        name: action.payload.name,
      });
    },
  },
});

// Action creators are generated for each case reducer function
export const { updateHeaderState } = headerSlice.actions;

export const headerData = (state) => state.header;

export default headerSlice.reducer;
