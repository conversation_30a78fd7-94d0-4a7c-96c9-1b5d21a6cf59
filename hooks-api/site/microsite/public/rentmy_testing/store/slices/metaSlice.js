import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  description: null,
  keywords: null,
  pageName: null,
  pageTitle: null,
  image: null,
  pageId: null,
  gscContent: null,
  pinterestContent: null,
  adsenseContent: null,
  schema: null,
  meta: null,
};

export const metaSlice = createSlice({
  name: 'pageMeta',
  initialState,
  reducers: {
    metaBuilder: (state, action) => {
      state.description = action.payload.pageInfo?.meta?.description || null;
      state.keywords = action.payload.pageInfo?.meta?.keywords || null;
      state.image = action.payload.pageInfo?.meta?.image || null;
      state.pageName = action.payload.pageInfo?.pageName || null;
      state.pageTitle = action.payload.pageInfo?.pageTitle || null;
      state.pageId = action.payload.pageInfo?._id || null;
      state.meta = action.payload.pageInfo?.meta || null;
    },
    metaBuilderV2: (state, action) => {
      state.meta = action.payload?.meta || null;
      state.description = action.payload?.meta?.description || null;
      state.keywords = action.payload?.meta?.keywords || null;
      state.image = action.payload?.meta?.imageInfo || null;
      state.pageName = action.payload.pageName || null;
      state.pageTitle = action.payload.pageTitle || null;
      state.pageId = action.payload.slug || null;
      state.schema = action.payload.schema || null;
    },
    setMeta: (state, action) => {
      if (action.payload.adsense && action.payload.adsense.active) {
        state.adsenseContent = action.payload.adsense.content || null;
      }
      if (action.payload.searchConsole && action.payload.searchConsole.active) {
        state.gscContent = action.payload.searchConsole.content || null;
      }
      if (action.payload.facebookPixel && action.payload.facebookPixel.active) {
        state.fbPixelContent = action.payload.facebookPixel.content || null;
      }
      if (action.payload.pinterest && action.payload.pinterest.active) {
        state.pinterestContent = action.payload.pinterest?.content || null;
      }
    },
    reset: () => initialState,
  },
});

export const { metaBuilder, setMeta, setPinterest, metaBuilderV2 } =
  metaSlice.actions;

export const pageMetaData = (state) => state.pageMeta;

export default metaSlice.reducer;
