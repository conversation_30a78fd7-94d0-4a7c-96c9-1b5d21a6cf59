import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  cartItem: 0,
};

export const rentmySlice = createSlice({
  name: 'rentmy',
  initialState,
  reducers: {
    setCartItem: (state, action) => {
      state.cartItem = action.payload;
    },
    incrementCartItem: (state, action) => {
      state.cartItem += action.payload;
    },
    decrementCartItem: (state, action) => {
      state.cartItem -= action.payload;
    },
    resestCartItem: (state, action) => {
      state.cartItem = 0;
    },
  },
});

export const rentMyData = (state) => state.rentmy;

// Action creators are generated for each case reducer function
export const {
  setCartItem,
  incrementCartItem,
  decrementCartItem,
  resestCartItem,
} = rentmySlice.actions;

export default rentmySlice.reducer;
