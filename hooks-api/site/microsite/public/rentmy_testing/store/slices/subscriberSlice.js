import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axios from 'axios';
import { subscriber_api } from '@/const/api-endpoint';

const initialState = {
  loading: false,
  valid: false,
  id: null,
  code: null,
  subDomain: null,
  type: 'CA',
  hasSubdomain: true,
  domainKey: ''
};

export const subscriberSlice = createSlice({
  name: 'subscriber',
  initialState,
  reducers: {
    setLoader: (state, action) => { },
    setDomain: (state, action) => {
      state.subDomain = action.payload;
      localStorage.setItem('subdomain', action.payload);
    },
    setSubscriber: (state, action) => {
      state.code = action.payload.code ?? null;
      state.valid = action.payload.isValid;
      state.id = action.payload.franchisee;
      state.domainKey = action?.payload?.domainKey ?? '';

    },
    setAppType: (state, action) => {
      state.type = action.payload;
      state.hasSubdomain = action.payload == 'SA' ? false : true;
    },
    removeDomain: (state, action) => {
      state.subDomain = null;
      localStorage.removeItem('subdomain');
    },
    reset: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSubscriber.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSubscriber.fulfilled, (state, action) => {
        state.loading = false;
        if (!action.payload.isValid) {
          state.subDomain = null;
          //localStorage.removeItem('subdomain');
          //localStorage.removeItem('subscriber');
        } else {
          state.code = action.payload.code ?? null;
          state.valid = action.payload.isValid;
          state.id = action.payload.franchisee;
          //localStorage.setItem('subscriber', JSON.stringify(action.payload));
        }
      })
      .addCase(getSubscriber.rejected, (state, action) => {
        state.loading = false;
        state.valid = false;
        state.subDomain = null;
        // localStorage.removeItem('subdomain');
        // localStorage.removeItem('subscriber');
      });
  },
});

export const getSubscriber = createAsyncThunk(
  'subscriber/data',
  async (domain, { getState }) => {
    const res = await axios(`${subscriber_api}/${domain}`);
    return res.data;
  }
);

export const { setDomain, setSubscriber, setAppType } = subscriberSlice.actions;

export const subscriberData = (state) => state.subscriber;

export default subscriberSlice.reducer;
