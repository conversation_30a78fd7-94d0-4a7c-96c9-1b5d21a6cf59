import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { HYDRATE, createWrapper } from 'next-redux-wrapper';
import subscriber from './slices/subscriberSlice';
import pageMeta from './slices/metaSlice';
import builder from './slices/builderSlice';
import globalSetting from './slices/globalSettingSlice';
import header from './slices/headerSlice';
import rentmy from './slices/rentmySlice';

const combinedReducer = combineReducers({
  subscriber,
  pageMeta,
  builder,
  globalSetting,
  header,
  rentmy,
});

//master reducer for the server side rendereing
const masterReducer = (state, action) => {
  if (action.type === HYDRATE) {
    const nextState = {
      ...state,
      pageMeta: { ...action.payload.pageMeta },
      subscriber: { ...action.payload.subscriber },
      builder: { ...action.payload.builder },
      globalSetting: { ...action.payload.globalSetting },
      header: { ...action.payload.header },
      rentmy: { ...action.payload.rentmy },
    };
    return nextState;
  } else {
    return combinedReducer(state, action);
  }
};

export const makeStore = () =>
  configureStore({
    reducer: masterReducer,
  });

export const wrapper = createWrapper(makeStore, { debug: false });
