html,
body {
  padding: 0;
  margin: 0;
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  font-size: 18px;
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
}

a {
  color: #0070f3;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

img {
  max-width: 100%;
  display: block;
}

/* Slider Legecy Style */

.rec-carousel-wrapper {
  position: relative;
}

.rec-slider-container {
  margin: 0;
}

.rec-pagination {
  position: absolute;
  bottom: 10px;
}

.rec .rec-dot {
  box-sizing: border-box;
  padding: 0;
  -webkit-transition: all 250ms ease;
  transition: all 250ms ease;
  border: none;
  margin: 5px;
  background-color: transparent;
  font-size: 1.3em;
  content: '';
  height: 8px;
  width: 8px;
  box-shadow: 0 0 1px 2px rgb(0 0 0 / 50%);
  border-radius: 50%;
  outline: none;
}

.rec .rec-dot .rec .rec-dot_active {
  box-sizing: border-box;
  padding: 0;
  -webkit-transition: all 250ms ease;
  transition: all 250ms ease;
  border: none;
  margin: 5px;
  background-color: rgb(158 158 158);
  font-size: 1.3em;
  content: '';
  height: 8px;
  width: 8px;
  box-shadow: 0 0 0px 4px rgb(197 197 197);
  border-radius: 50%;
  outline: none;
}

/* Font size for Headings */
.html-container h1 {
  font-size: 2em !important;
}

.html-container h2 {
  font-size: 1.5em !important;
}

.html-container h3 {
  font-size: 1.17em !important;
}

.html-container h4 {
  font-size: 1em !important;
}

.html-container h5 {
  font-size: 0.83em !important;
}

.html-container h6 {
  font-size: 0.67em !important;
}

/* styles for ul & ol Tag */
.html-container ul,
.html-container ol {
  list-style-position: inside;
}

.html-container a {
  color: #1b1bef;
}

.html-container button {
  background: #e9e9ed;
  padding: 0.2em 0.5em;
}

.html-container blockquote {
  margin-left: 1.5em;
}

.html-container hr {
  border-top: 1px solid black;
}

/* Nuka Carousel */
.slider-control-bottomcenter {
  width: 20px;
  margin: 0 auto;
  /* position: fixed !important; */
  /* padding:0px 0 5px; */
}

.slider-control-bottomcenter ul {
  justify-content: center;
  align-items: center;
}

.paging-dot {
  width: 16px;
  height: 16px;
  /* border-radius: 50%; */
}

/* .paging-dot > circle {
  r: 8px;
  cx: 8px;
  cy: 8px;
} */

/* Menu Nav */

.web-primary-menu-nav {
  /* nav */
  position: sticky;
  min-height: 50px;
  background-color: rgb(255, 255, 255);
  /* padding-bottom: 10px;  */
  top: 0;
  width: 100%;
  z-index: 9999;
  font-family: 'Poppins';
}

.web-primary-menu-container {
  /* div */
  /* max-width: 1140px;  */
  margin: 0 auto;
}

.web-primary-menu-main-navbar {
  /* Ul */
  display: flex;
  flex-wrap: wrap;
  /* height: 50px;  */
  align-items: center;
  /* background-color: rgb(255, 255, 255);  */
  /* padding: 10px 20px;  */
}

.web-primary-menu-main-navbar > .web-primary-menu-nav-item {
  /* li */
  /* padding: 10px 20px;  */
  list-style-type: none;
}

.web-primary-menu-dropdown {
  /* dropable li */
  /* position: relative; */
}

.web-primary-menu-dropdown > .web-primary-menu-dropdown-menu {
  /* li > ul */
  position: absolute;
  /* top: 86%; */
  /* left: 50px;  */
  /* padding: 10px;  */
  background-color: white;
  font-size: 15px;
  list-style-type: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  min-width: 250px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity 200ms ease, transform 200ms ease;
}

.web-primary-menu-dropdown
  > .web-primary-menu-dropdown-menu.web-primary-menu-show {
  z-index: 100;
  opacity: 1;
  visibility: unset;
  transform: translateY(0px);
}

/* .ml-auto { 
  margin-left: auto;
} */

@media (max-width: 767px) {
  .web-primary-menu-container {
    overflow-x: hidden;
  }
}

/* NProgress Bar */
/* #nprogress .bar { */
/* background: #2b6cb0 !important; */
/* background: #3d8cc0 !important; */
/* height: 4px !important; */
/* z-index: 10000 !important; */
/* } */

/* Web Pagination */
.web-pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
  margin-bottom: 30px;
}

/* .web-pagination-page-link:hover {
  z-index: 2;
  color: black;
  text-decoration: none;
  background-color: #c6f6d5;
  border-color: #c6f6d5;
} */

.web-pagination-page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: black;
  background-color: #fff;
  border: 1px solid #dee2e6;
}

/* .web-pagination-page-item.web-pagination-active .web-pagination-page-link {
  z-index: 1;
  color: #fff;
  background-color: #2c7a7b;
  border-color: #2c7a7b;
} */

.web-pagination-disabledClassName > a:hover {
  cursor: not-allowed;
  background: white;
}

/* ============================ CSS for menus2 ==================================== */

.sub-menu {
  pointer-events: none;
  opacity: 0;
  overflow: hidden;
  max-height: 0;
  transform: translateY(-10px);
  transition: max-height 300ms ease, opacity 200ms ease, transform 300ms ease;
}

.show-sub-menu {
  max-height: 400px;
  pointer-events: inherit;
  opacity: 1;
  height: auto;
  transform: translateY(0px);
  transition: max-height 300ms ease, opacity 200ms ease, transform 300ms ease;
}

.sub-menu-icon {
  transition: transform 200ms ease;
  transform: rotate(360deg);
}

.rotate-icon {
  transition: transform 200ms ease;
  transform: rotate(180deg);
}

.rich-editor a {
  color: #2c80b9;
  font-weight: bold;
}

.rich-editor a:hover {
  opacity: 0.8;
}

::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 2px gray;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: gray;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #666;
}
