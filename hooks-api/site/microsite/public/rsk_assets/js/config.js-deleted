/**
 * SUB<PERSON><PERSON><PERSON>IN vairalbe for testing purpose for developer mode
 * It will work wehen, IS_SINGLE_SITE == false
 * Example: 
 * var SUBDOMAIN = 'teststore02'
 */
var SUBDOMAIN = ''; 
var IS_SINGLE_SITE = true; 
var DOMAIN = 'http://microsite.localhost/'; //'WILL_SET_WITH_NODE';

var PAGES_ROUTE_PATHS = {
    
    products_list: "/rsk/products-list",
    products_list_by_category: "/category/{uuid}/{url}",

    product_details: "/rsk/products/{url}",  // ------- Approved Pattern --- for SEO
    package_details: "/rsk/package/{url}",  // ------- Approved Pattern --- for SEO 
    
    cart: "/rsk/cart",
    checkout: "/rsk/checkout",
    login: "customer-login",
    registration: "customer-registration",
    partner_login: "client/login",
    partner_registration: "/client/sign-up",
    reset_password: "customer-reset-password",
    customer_profile: "customer-profile",
    customer_change_password: "customer-change-password",
    customer_change_avatar: "customer-change-avatar",
    customer_order_history: "customer-order-history",
    order_details: "customer-order-details",
    order_complete: "order-complete",
    wish_list: "wish-list", 
}

// Client specified routes
var OTHER_PAGES_ROUTES = {
    home: {
        title: 'Home',
        file: "/pages/home.html",
        path: '/',
        /**
         * in meta key, you can object with (title, description, keywords, imageUrl)
         * or you can pass 'string' key accroding to meta.js file, store wise hard coded meta stucture
         * 
         * Exmaple: 1
         * 
         meta: {
            title: '',
            description: '',
            keywords: '',
            imageUrl: '',
         }
            ---- or ----
         meta: 'pass key from meta.js'
         */
        
        
    },
    sitemap: {
        title: 'sitemap',
        file: "/pages/sitemap.html",
        path: '/sitemap',
    }, 
}

var RENTMY_GLOBAL = {

    IS_SINGLE_SITE,
    SUBDOMAIN,

    store_id: "346",
    locationId: "386",
    store_name: "mountain-side-gear-rental",
    access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAxOS0wNy0xMFQxOTo0OTowOCswMDowMCIsInN0b3JlX2lkIjozNDYsInN0b3JlX3VpZCI6bnVsbCwic3RvcmVfbmFtZSI6bnVsbCwic291cmNlIjoib25saW5lIiwiaXNfb25saW5lIjoxLCJsb2NhdGlvbiI6Mzg2fQ.XkHvvt-SrPlP_m3mfCYJSUBojyegcNx4Z9C3DmzNsPk",

    env: { // optional 
        PORT: "3040",

        CSS_URL: "/css/index.css",
        SCRIPT_URL: "/js/script_prod.js",        
        DATEPICKER_CSS_URL: "/css/em-datetimepicker.min.css",
        DATEPICKER_SCRIPT_URL: "/js/em-datetimepicker.min.js",
        
        /* === new === */
        // CSS_URL: "http://localhost:4444/assets/index.css",                              // developer local url
        // SCRIPT_URL: "http://localhost:4444/assets/script_prod.js",                      // developer local url
        
        // DATEPICKER_CSS_URL: "http://localhost:4545/assets/em-datetimepicker.min.css",   // developer local url
        // DATEPICKER_SCRIPT_URL: "http://localhost:4545/assets/em-datetimepicker.min.js", // developer local url 


        API_BASE_URL : "https://api-premium.rentmy.co/api/", //------------- optional
        ASSET_URL : "https://s3.us-east-2.amazonaws.com/pimg.rentmy.co/",     //------------- optional 
        PAYMENT_DOMAIN : "", //------------- optional
        CACHE_TIME_IN_SECONDS: 600,
    },
    emDateTimePicker: {
      
      /* ------------------------------------------- */
      /*                 Details Page                */
      /* ------------------------------------------- */  

      // End Date config
      detailsPage_endDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_endDate: true, //required for wix
      detailsPage_endDate_allowRightSideTimePicker: true, //required for wix
      
    
      /* ------------------------------------------- */
      /*               In Page Cart Widget           */
      /* ------------------------------------------- */ 
      afterAddtoCart_open_widget_datePicker: false,
    },

    home_url: "/",
    product_pacakge_by_slug: true, // required for node server

    contents: {
        pages: true,
        navigation: false,
        use_dynamic_labels: false,
    },

    page: {
        ...PAGES_ROUTE_PATHS
    },
    routes: {
        products_list: {
            title: 'Products List',
            file: "products-list.html",
            path: PAGES_ROUTE_PATHS.products_list,
        },
        ...(!PAGES_ROUTE_PATHS?.products_list_by_category ? {} : {
            products_list_by_category: {
                title: 'Products List By Category',
                file: "products-list.html",
                path: PAGES_ROUTE_PATHS?.products_list_by_category,
            }
        }),
        product_details: {
            title: 'Product Details',
            file: "product-details.html",
            path: PAGES_ROUTE_PATHS.product_details,
        },
        package_details: {
            title: 'Package Details',
            file: "package-details.html",
            path: PAGES_ROUTE_PATHS.package_details,
            query: ['uid'],
        },
        cart: {
            title: 'Cart',
            file: "cart.html",
            path: PAGES_ROUTE_PATHS.cart,
        },
        checkout: {
            title: 'Checkout',
            file: "checkout.html",
            path: PAGES_ROUTE_PATHS.checkout,
        },
        login: {
            title: 'User Login',
            file: "customer-login.html",
            path: PAGES_ROUTE_PATHS.login,
        },
        registration: {
            title: 'User Registration',
            file: "customer-registration.html",
            path: PAGES_ROUTE_PATHS.registration,
        },
        partner_login: {
            title: 'Partner Login',
            file: "partner-login.html",
            path: PAGES_ROUTE_PATHS.partner_login,
        },
        partner_registration: {
            title: 'Partner Sign Up',
            file: "partner-registration.html",
            path: PAGES_ROUTE_PATHS.partner_registration,
        },
        reset_password: {
            title: 'Reset Password',
            file: "customer-reset-password.html",
            path:PAGES_ROUTE_PATHS.reset_password,
        },
        customer_profile: {
            title: 'Profile',
            file: "customer-profile.html",
            path: PAGES_ROUTE_PATHS.customer_profile,
        },
        customer_change_password: {
            title: 'Update Password',
            file: "customer-change-password.html",
            path: PAGES_ROUTE_PATHS.customer_change_password,
        },
        customer_change_avatar: {
            title: 'Change Avatar',
            file: "customer-change-avatar.html",
            path: PAGES_ROUTE_PATHS.customer_change_avatar,
        },
        customer_order_history: {
            title: 'Order History',
            file: "customer-order-history.html",
            path: PAGES_ROUTE_PATHS.customer_order_history,
        },
        order_details: {
            title: 'Order Details',
            file: "customer-order-details.html",
            path: PAGES_ROUTE_PATHS.order_details,
        },
        order_complete: {
            title: 'Order Complete',
            file: "order-complete.html",
            path: PAGES_ROUTE_PATHS.order_complete,
        },
        wish_list: {
            title: 'Wish list',
            file: "wish-list.html",
            path: PAGES_ROUTE_PATHS.wish_list,
        },
        blog: {
            title: 'blog',
            file: "/blog.html",
            path: '/blog',
            meta: {
                title: 'VGC Blog - Vivian Grace Creations',
                description: `Blog Page Contents`,
            },
            limit: 10
        },
        singleBlog: {
            title: 'sitemap', //
            file: '/pages/blog-details.html',
            path: '/blog/{page_slug}',
            rentmy_page: true,
        },
        ...OTHER_PAGES_ROUTES
    },
}

