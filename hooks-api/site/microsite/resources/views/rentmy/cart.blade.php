{{-- resources/views/products/show.blade.php --}}
@extends('rentmy.layout')

@section('title', 'Cart')

@section('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .image-container {
            width: 100%;
            padding-bottom: 56.25%;
            /* 16:9 aspect ratio */
            position: relative;
            overflow: hidden;
        }

        .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .theme-btn {
            background-color: rgb(97, 175, 175);
            color: #ffffff;
        }

        .cart-img {
            width: 100px;
            height: 100px;
            object-fit: contain;
        }

        a.btn, .theme-btn {
            background-color: rgb(97, 175, 175);
            color: #ffffff;
        }

    </style>
@endsection

@section('content')

    <?php if( !empty($cart['cart_items']) ): ?>
    <div class="container mt-4">

        <h1>Cart View</h1>

        <p>Drop Off Date? {{ $cart['rent_start'] }} <a id="rentmy_start_link" href="javascript:void(0);"><svg style="width:16px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><path d="M402.6 83.2l90.2 90.2c3.8 3.8 3.8 10 0 13.8L274.4 405.6l-92.8 10.3c-12.4 1.4-22.9-9.1-21.5-21.5l10.3-92.8L388.8 83.2c3.8-3.8 10-3.8 13.8 0zm162-22.9l-48.8-48.8c-15.2-15.2-39.9-15.2-55.2 0l-35.4 35.4c-3.8 3.8-3.8 10 0 13.8l90.2 90.2c3.8 3.8 10 3.8 13.8 0l35.4-35.4c15.2-15.3 15.2-40 0-55.2zM384 346.2V448H64V128h229.8c3.2 0 6.2-1.3 8.5-3.5l40-40c7.6-7.6 2.2-20.5-8.5-20.5H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V306.2c0-10.7-12.9-16-20.5-8.5l-40 40c-2.2 2.3-3.5 5.3-3.5 8.5z"/></svg></a></p>
        <div class="flatpickr-input">
            <input id="rent_start" type="hidden" value="{{ $cart['rent_start'] }}">
        </div>

        <div class="table-responsive">
            <table class="table mb-5 pb-5 cart table-border">
                <thead>
                    <tr>
                        <th></th>
                        <th></th>
                        <th>Product</th>
                        <th>Unit Price</th>
                        <th>Quantity</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($cart['cart_items'] as $item): ?>
                    @php $imgURL = ( !empty($settings->store_id) && isset($item['product']['images'][0]['image_small']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->store_id . '/' . $item['product']['id'] . '/' . $item['product']['images'][0]['image_small'] : ''; @endphp
                    <tr id="cart_item_{{ $item['id'] }}">
                        <td class="remove-cart">
                            <a class="btn m-btn m-btn--hover-danger m-btn--icon m-btn--icon-only m-btn--pill"
                                onclick="deleteCartItem('{{ $item['id'] }}', '{{ $item['product_id'] }}', '{{ $item['token'] }}')">
                                <svg style="fill:black" height="20" xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 448 512">
                                    <path
                                        d="M135.2 17.7L128 32 32 32C14.3 32 0 46.3 0 64S14.3 96 32 96l384 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0-7.2-14.3C307.4 6.8 296.3 0 284.2 0L163.8 0c-12.1 0-23.2 6.8-28.6 17.7zM416 128L32 128 53.2 467c1.6 25.3 22.6 45 47.9 45l245.8 0c25.3 0 46.3-19.7 47.9-45L416 128z" />
                                </svg>
                            </a>
                        </td>
                        <td class="cart-img">
                            @if (!empty($imgURL))
                                <img src="{{ $imgURL }}" class="img-fluid">
                            @else
                                <div class="bg-secondary"
                                    style="width: 100px; height: 100px; display: flex; justify-content: center; align-items: center; color: white;">
                                    No Image</div>
                            @endif
                        </td>
                        <td>
                            <div class="product p-0">
                                <span class="font-weight-bold">{{ $item['product']['name'] }}</span>
                                <div>
                                    @if( !empty($item['products']) )
                                        @foreach($item['products'] as $product)
                                            <div class="p_variants">
                                                <span><small>{{ $product['name'] }} ({{ $product['quantity'] }})</small></span>
                                            </div>
                                        @endforeach
                                    @endif
                                
                                    @if( !empty($item['cart_product_options']) )
                                        @foreach($item['cart_product_options'] as $cpo)
                                            @if( is_array($cpo['options']) && !empty($cpo['options']) )
                                                @foreach($cpo['options'] as $option)
                                                    <div class="p_variants">
                                                        <span><small>{{ $option['label'] }}: {{ $option['value'] }}</small></span>
                                                    </div>
                                                @endforeach
                                            @endif
                                        @endforeach
                                    @endif

                            </div>
                        </td>
                        <td> ${{ $item['price'] }} </td>
                        <td>
                            <div><app-cart-qty>
                                    <div class="custom-alert"></div>
                                    <div class="quantity clearfix">
                                        <span onclick="updateCartItem('{{ $item['id'] }}', '{{ $item['token'] }}', 0, '{{ $item['sales_tax'] }}', '{{ $item['price'] }}', '')" class="btn btn-sm btn-dark no-m"> - </span>
                                        <span class="cart-qunt btn btn-sm no-m"> {{ $item['quantity'] }} </span>
                                        <span onclick="updateCartItem('{{ $item['id'] }}', '{{ $item['token'] }}', 1, '{{ $item['sales_tax'] }}', '{{ $item['price'] }}', '')" class="btn btn-sm btn-dark no-m"> + </span>
                                    </div>
                                </app-cart-qty></div>
                        </td>
                        <td> ${{ $item['sub_total'] }} @if ( isset($item['discount']['coupon_amount']) && $item['discount']['coupon_amount'] > 0 ) <br> <small>(${{ number_format($item['discount']['coupon_amount'], 2) }} Coupon applied)</small> @endif </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>


        <div class="row">
            <div class="col-md-6"><app-cart-coupon>
                    <form novalidate="" class="form coupon-form" onsubmit="return false;">
                        <div class="form-group" style="flex: 2;">
                            <input id="coupon_code" type="text" name="couponCode" required=""
                                class="form-control ng-untouched ng-pristine ng-invalid"
                                placeholder="Enter Coupon Code">
                        </div>
                        <div class="form-group text-right" style="flex: 1;">
                            <button onclick="cartsApplyCoupon()" type="button" class="btn caps ml-2 theme-btn"> Apply Coupon </button>
                        </div>
                    </form>
                    <div class="custom-alert"></div>
                </app-cart-coupon>
                <div class="cart-action">
                    <button onclick="window.location.href = '{{ route('rentmy.checkout') }}'"
                        class="btn theme-btn caps cart-btn-checkout"> Proceed Checkout </button>
                    <button onclick="window.location.href = '{{ route('rentmy.index') }}'"
                        class="btn theme-btn caps cart-btn-continue ml-0"> Continue Shopping </button>
                </div>
            </div>
            <div class="col-md-6">
                    <h4 class="pb-2 cart-total-title">Cart Totals</h4>
                    <div class="table-responsive">
                        <table class="table cart">
                            <tbody>
                                @if( !empty($cart['coupon_amount']) )
                                <tr>
                                    <td> Coupon applied </td>
                                    <td><span class="cart_p"> ${{ number_format($cart['coupon_amount'], 2) }} </span></td>
                                </tr>
                                @endif
                                <tr>
                                    <td> Subtotal </td>
                                    <td><span class="cart_p"><b>${{ number_format($cart['sub_total'], 2) }}</b></span></td>
                                </tr>
                                <tr>
                                    <td> Taxes &amp; Fees </td>
                                    <td><span class="cart_p"><b>${{ number_format($cart['additional_charge'], 2) }}</b></span></td>
                                </tr>
                                <tr>
                                    <td> Deposit Amount </td>
                                    <td><span class="cart_p"> ${{ number_format($cart['deposit_amount'], 2) }} </span></td>
                                </tr>
                                <tr>
                                    <td> Delivery Charge </td>
                                    <td><small class="cart_p"> Calculated in the next step</small></td>
                                </tr>
                                <tr>
                                    <td> Delivery Tax </td>
                                    <td><small class="cart_p"> Calculated in the next step</small></td>
                                </tr>
                                <tr>
                                    <td>
                                        <h5>Total</h5>
                                    </td>
                                    <td>
                                        <h5><span class="cart_p">${{ number_format($cart['total'], 2) }}</span></h5>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
            </div>
        </div>


    </div>
    <?php else: ?>
    <div class="container MT-4">
        <div class="row">
            <h1>Cart View</h1>
            <div class="col-md-12 mb-7 pb-7">
                <div class="card">
                    <div class="card-body text-center"><img
                            src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/embed/images/icons/emptybag.png"
                            class="img-fluid emptybag-img">
                        <br>
                        <h4 class="mb-4 pb-2"> Your cart is empty </h4>
                        <a  href="{{ route('rentmy.index') }}" class="btn btn-secondary"
                            style="cursor: pointer;">Continue Shopping</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

@endsection

@section('scripts')

 <?php       
    $showStartDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_start_date']) && $contents['result']['data'][0]['contents']['confg']['show_start_date'] == 1 ) ? true : false;
    $showStartTime = ( isset($contents['result']['data'][0]['contents']['confg']['show_start_time']) && $contents['result']['data'][0]['contents']['confg']['show_start_time'] == 1 ) ? true : false;
    $showEndDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_end_date']) && $contents['result']['data'][0]['contents']['confg']['show_end_date'] == 1 ) ? true : false;
    $showEndTime = ( isset($contents['result']['data'][0]['contents']['confg']['show_end_time']) && $contents['result']['data'][0]['contents']['confg']['show_end_time'] == 1 ) ? true : false;
    $rentalPriceOption = ( isset($contents['result']['data'][0]['contents']['confg']['rental_price_option']) && $contents['result']['data'][0]['contents']['confg']['rental_price_option'] == 1 ) ? true : false;
    $showEarliestStartDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_earliest_start_date']) && $contents['result']['data'][0]['contents']['confg']['show_earliest_start_date'] == 1 ) ? true : false;
?>

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

        <script type="text/javascript">
        let calendarOptions = {
            minDate: "today",
            altInput: true,
            clickOpens: false,
            enableTime: false,
            dateFormat: "Y-m-d",
            time_24hr: true,
            closeOnSelect: false, // Keep calendar open until Apply/Cancel is clicked
            // mode: "range",    

            onValueUpdate: function(selectedDates, dateStr, instance) {
                instance.input.value = instance._initialValue;
console.log('instance.input.value', instance.input.value);
            },

            // Store the initial value when the calendar opens
            // This is useful for reverting on "Cancel"
            onOpen: function(selectedDates, dateStr, instance) {
                instance._initialValue = instance.input.value;
console.log('instance._initialValue', instance._initialValue);
            },

            onReady: function(selectedDates, dateStr, instance) {
                // Create a div to hold the buttons
                const buttonContainer = document.createElement("div");
                buttonContainer.className = "flatpickr-button-container"; // Add a class for styling

                // Create the Apply button
                const applyButton = document.createElement("button");
                applyButton.innerHTML = "Apply";
                applyButton.className = "flatpickr-apply-button";

                // Create the Cancel button
                const cancelButton = document.createElement("button");
                cancelButton.innerHTML = "Cancel";
                cancelButton.className = "flatpickr-cancel-button";

                // Append buttons to the container
                buttonContainer.appendChild(cancelButton); // Add Cancel first to appear on the left
                buttonContainer.appendChild(applyButton);

                // Append the button container to the calendar container
                // You might want to adjust the exact placement based on Flatpickr's structure.
                // A common place is just before the time or day picker.
                // instance.calendarContainer.appendChild(buttonContainer); // Appends at the very end
                // A more common placement for action buttons is at the bottom before any close button.
                // Let's try to append it before the time/date picker elements if they exist.
                const flatpickrDays = instance.calendarContainer.querySelector('.flatpickr-days');
                const flatpickrTime = instance.calendarContainer.querySelector('.flatpickr-time');

                instance.calendarContainer.appendChild(buttonContainer);

                // Add event listener to the "Apply" button
                applyButton.addEventListener("click", function() {

                    if( instance.selectedDates[1] != undefined ) {
                        const startDate = instance.selectedDates[0];
                        const endDate = instance.selectedDates[1];
                        $('.date_start_detail').val( instance.formatDate(startDate, instance.config.dateFormat) )
                        $('.date_end_detail').val( instance.formatDate(endDate, instance.config.dateFormat) )
                        $('.rent_start_to_rent_end').text( instance.formatDate(startDate, instance.config.dateFormat) + ' to ' + instance.formatDate(endDate, instance.config.dateFormat) )
                        instance.close(); // Manually close the calendar
                        return;
                    } else {
                        const selectedDate = instance.selectedDates[0];
                        instance.input.value = instance.formatDate(selectedDate, instance.config.dateFormat);
                        $('.date_start_detail').val( instance.formatDate(selectedDate, instance.config.dateFormat) )
                        $('.rent_start_to_rent_end').text( instance.formatDate(selectedDate, instance.config.dateFormat) )
                    }

                    const selectedDate = instance.selectedDates[0];
                    if (selectedDate) {
                        // The input is already updated by flatpickr's selection.
                        // If you had custom display logic, you'd apply it here.
                        console.log("Applied Date:", instance.formatDate(selectedDate, instance.config.dateFormat));
                    } else {
                        console.log("No date selected to apply.");
                    }

                    checkAvailability();

                    instance.close(); // Manually close the calendar
                });

                // Add event listener to the "Cancel" button
                cancelButton.addEventListener("click", function() {
                    // Revert the input field to its value before the calendar opened
                    // instance.input.value = instance._initialValue;
                    // Optionally, you might want to clear selectedDates internally if needed,
                    // but simply reverting the input value and closing is usually enough for "Cancel".
                    // instance.clear(); // Use with caution, as it also clears the internal selectedDates.
                    instance.close(); // Manually close the calendar
                });
            },

            // Optional: on-close logic
            onClose: function(selectedDates, dateStr, instance) {
                // This hook fires regardless of how the calendar closes (Apply, Cancel, clicking outside).
                // If you need specific logic for when "Cancel" is used versus "Apply" or external close,
                // you'd need a flag set by the button clicks.
                // For simple cancel, the onOpen and onCancel logic above handles it.
            }

        }

        @if( $showStartDate && $showStartTime )
            calendarOptions.enableTime = true;
            calendarOptions.dateFormat = "Y-m-d H:i";
        @endif

        @if( $showEndDate )
            calendarOptions.mode = 'range';
        @endif

        @if( isset($cart['rent_start']) )
            calendarOptions.defaultDate = new Date("{{ $cart['rent_start'] . ' 12:00' }}");
        @else
            calendarOptions.defaultDate = new Date();
        @endif
        const fp1 = flatpickr("#rent_start", calendarOptions);
        // Get all elements with the class 'flatpickr-input'
        let flatpickrInputs = document.querySelectorAll('.flatpickr-input');
        // Iterate over each element in the NodeList
        flatpickrInputs.forEach(input => {
        input.style.display = 'none';
        });
        // Get all elements with the class 'flatpickr-input'
        flatpickrInputs = document.querySelectorAll('.date_start_detail.form-control');
        // Iterate over each element in the NodeList
        flatpickrInputs.forEach(input => {
            input.style.visibility = 'hidden';
            input.style.height = 0;
        });
        jQuery(document).ready(function($) {
            let gettingPrice = false;
            $('#rentmy_start_link').on('click', function() {
                fp1.toggle()
            });

        })


        function checkAvailability() {

            let date_start = $('#rent_start').val();

            $.ajax({
                url: "{{ $settings->api_base_url }}products/availability",
                type: "POST",
                headers: {
                    "accept": "application/json",
                    "authorization": "Bearer {{ $settings->access_token }}",
                    "content-type": "application/json",
                    "location": "{{ $settings->location_id }}"
                },
                data: JSON.stringify({
                    "token": "{{ $cart['token'] ?? null }}",
                    "type": "cart", 
                    "start_date": date_start,
                    "end_date": date_start,
                    "source": "online",
                    "fullfilment_option": null
                }),
                success: function(response) {
                    console.log('Availability check successful:', response);
                    if(response?.status == 'OK') {
                        updateCartData(response?.result?.data);                            
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error checking availability:', error);
                }
            });


        }


        function updateCartData(data) {

            $.ajax({
                url: '/rentmy/update-cart',
                type: 'POST',
                headers: {
                    "accept": "application/json",
                    "authorization": "Bearer {{ $settings->access_token }}",
                    "content-type": "application/json",
                    "location": "{{ $settings->location_id }}"
                },                
                data: JSON.stringify({
                    "_token": "{{ csrf_token() }}",
                    "token": "{{ $cart['token'] ?? null }}",
                    "data": data
                }),
                success: function(response) {
                    console.log('response', response);
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Error updating cart:', error);
                }
            });

        }



        function deleteCartItem(cart_item_id, product_id, token) {

            $.ajax({
                url: '/rentmy/delete-cart-item',
                type: 'POST',
                data: {
                    "_token": "{{ csrf_token() }}",
                    "cart_item_id": cart_item_id,
                    "product_id": product_id,
                    "token": token
                },
                success: function(response) {
                    console.log('response', response);
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting cart item:', error);
                }
            });
        }

        function updateCartItem(id, token, increment, sales_tax, price, view_token) {
            if( increment == 0 && $('#cart_item_' + id).find('.cart-qunt').text() == 1 ) {
                return;
            }
            $.ajax({
                url: '/rentmy/update-cart-item',
                type: 'POST',
                data: {
                    "_token": "{{ csrf_token() }}",
                    "id": id,
                    "token": token,
                    "increment": increment,
                    "sales_tax": sales_tax,
                    "price": price,
                    "view_token": view_token
                },
                success: function(response) {
                    console.log('response', response);
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Error updating cart item:', error);
                }
            });
        }

        function cartsApplyCoupon() {
            let coupon = $('#coupon_code').val();
            if( coupon == '' ) {
                return;
            }
            $.ajax({
                url: '/rentmy/carts-apply-coupon',
                type: 'POST',
                data: {
                    "_token": "{{ csrf_token() }}",
                    "coupon": coupon,
                    "token": "{{ $cart['token'] ?? null }}"
                },
                success: function(response) {
                    console.log('response', response);
                    if(response.success == false) {
                        alert(response.message);
                        return;
                    }
                    location.reload();
                },
                error: function(xhr, status, error) {
                    // console.error('Error applying coupon:', error);
                }
            });
        }


    </script>

@endsection

