{{-- resources/views/products/show.blade.php --}}
@extends('rentmy.layout')

@section('title', 'Checkout')

@section('styles')
    <style>
        .image-container {
            width: 100%;
            padding-bottom: 56.25%;
            /* 16:9 aspect ratio */
            position: relative;
            overflow: hidden;
        }

        .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        a.btn,
        button.btn,
        .theme-btn {
            background-color: rgb(97, 175, 175);
            color: #ffffff;
        }
    </style>
@endsection

@section('content')

    <div class="container mt-4">



        <section class="rentmy-checkout-content">

            <form action="{{ route('rentmy.checkout') }}" method="POST" onsubmit="return false;">
                @csrf

                {{-- <input type="hidden" name="company" value="">
                <input type="hidden" name="address_line1" value="">
                <input type="hidden" name="combinedAddress" value="">
                <input type="hidden" name="address_line2" value="">
                <input type="hidden" name="city" value="">
                <input type="hidden" name="state" value="">
                <input type="hidden" name="country" value="US">
                <input type="hidden" name="zipcode" value="">
                <input type="hidden" name="special_instructions" value="">
                <input type="hidden" name="special_requests" value="">
                <input type="hidden" name="driving_license" value="">
                <input type="hidden" name="fieldText" value="">
                <input type="hidden" name="fieldSelection" value="">
                <input type="hidden" name="custom_checkout[fields]" value="">
                <input type="hidden" name="shipping_first_name" value="FirstName">
                <input type="hidden" name="shipping_last_name" value="LasttName">
                <input type="hidden" name="shipping_mobile" value="">
                <input type="hidden" name="shipping_email" value="<EMAIL>">
                <input type="hidden" name="shipping_address1" value="Location">
                <input type="hidden" name="combinedDeliveryAddress" value="">
                <input type="hidden" name="shipping_address2" value="Address Line 222">
                <input type="hidden" name="shipping_city" value="Gallipolis">
                <input type="hidden" name="shipping_state" value="Ohio">
                <input type="hidden" name="shipping_country" value="US">
                <input type="hidden" name="shipping_zipcode" value="45631">
                <input type="hidden" name="type" value="2">
                <input type="hidden" name="note" value="Testing">
                <input type="hidden" name="payment_gateway_name" value="Testing unique payments">
                <input type="hidden" name="additional_charges" value="">
                <input type="hidden" name="delivery[charge]" value="179.91">
                <input type="hidden" name="delivery[handling_amount]" value="2.22">
                <input type="hidden" name="delivery[two_way]" value="false">
                <input type="hidden" name="delivery[name]" value="Wolf place">
                <input type="hidden" name="delivery[carrier_code]" value="Standard shipping">
                <input type="hidden" name="delivery[charge_by]" value="standard shipping">
                <input type="hidden" name="delivery[location][id]" value="474">
                <input type="hidden" name="delivery[location][pickup_from]" value="123 Main st, Massena, NY, 13662, US">
                <input type="hidden" name="delivery[location][delivery_to]" value="Location, 45631, Gallipolis, OH, us">
                <input type="hidden" name="delivery[location][distance]" value="710.75">
                <input type="hidden" name="delivery[location][unit]" value="Mile">
                <input type="hidden" name="delivery[location][unit_price]" value=".25">
                <input type="hidden" name="delivery[location][weight]" value="1">
                <input type="hidden" name="delivery[method]" value="6">
                <input type="hidden" name="currency" value="USD">
                <input type="hidden" name="token" value="{{ $cart['token'] }}">
                <input type="hidden" name="custom_values" value="">
                <input type="hidden" name="signature" value="">
                <input type="hidden" name="gateway_id" value="17455">
                <input type="hidden" name="order_source" value="Online">
                <input type="hidden" name="shipping_method" value="6">
                <input type="hidden" name="location_id" value="474"> --}}

                <input type="hidden" name="location_id" value="{{ $settings->location }}">
                <input type="hidden" name="order_source" value="Online">
                <input type="hidden" name="token" value="{{ $cart['token'] }}">
                <input type="hidden" name="gateway_id" value="17455">
                <input type="hidden" name="shipping_method" value="4">


                {{-- <input type="hidden" name="shipping_first_name" value="FirstName">
                <input type="hidden" name="shipping_last_name" value="LasttName">
                <input type="hidden" name="shipping_mobile" value="">
                <input type="hidden" name="shipping_email" value="<EMAIL>">
                <input type="hidden" name="shipping_address1" value="Location">
                <input type="hidden" name="combinedDeliveryAddress" value="">
                <input type="hidden" name="shipping_address2" value="Address Line 222">
                <input type="hidden" name="shipping_city" value="Gallipolis">
                <input type="hidden" name="shipping_state" value="Ohio">
                <input type="hidden" name="shipping_country" value="US">
                <input type="hidden" name="shipping_zipcode" value="45631">
                <input type="hidden" name="type" value="2">
                <input type="hidden" name="note" value="Testing">
                <input type="hidden" name="payment_gateway_name" value="Testing unique payments">
                <input type="hidden" name="additional_charges" value=""> --}}


                <div class="container-fluid m-0">
                    <div class="row">
                        <div class="col-xl-7 col-lg-6 col-md-6 col-sm-12">
                            <div class="row">
                                <div class="col-xl-12"></div>
                            </div>
                            <div class="billing-details-leftside">
                                <div class="billingdetails-leftside-inner mb-5">
                                    <h2 class="wc-checkout-title">{{ $site_specific['checkout_info']['title_billing'] ?? 'Billing Details' }}</h2><app-billing-info>
                                        <div class="custom-alert"></div>
                                            <div class="row">
                                                @if( isset($site_specific['confg']['checkout']['billing']['first_name']['show']) && $site_specific['confg']['checkout']['billing']['first_name']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="exampleInputText1">First Name</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input id="billing_first_name" name="first_name" type="text" formcontrolname="first_name" autocomplete="billing given-name" class="form-control ng-untouched ng-pristine ng-valid">
                                                    </div>
                                                </div>
                                                @endif
                                                @if( isset($site_specific['confg']['checkout']['billing']['last_name']['show']) && $site_specific['confg']['checkout']['billing']['last_name']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="exampleInputText1">Last Name</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input id="billing_last_name" name="last_name" type="text" formcontrolname="last_name" autocomplete="billing family-name" class="form-control ng-untouched ng-pristine ng-valid">
                                                    </div>
                                                </div>
                                                @endif
                                                @if( isset($site_specific['confg']['checkout']['billing']['mobile']['show']) && $site_specific['confg']['checkout']['billing']['mobile']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="exampleInputText1">Mobile Number</label>
                                                        <input id="billing_mobile" name="mobile" type="text" numberonly="" formcontrolname="mobile" autocomplete="billing phone" class="form-control ng-untouched ng-pristine ng-valid"
                                                        id="mobile">
                                                    </div>
                                                </div>
                                                @endif
                                                @if( isset($site_specific['confg']['checkout']['billing']['email']['show']) && $site_specific['confg']['checkout']['billing']['email']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="exampleInputText1">Email Name</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input id="billing_email" name="email" type="email" formcontrolname="email" class="form-control ng-untouched ng-pristine ng-valid" autocomplete="billing email">
                                                    </div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['company']['show']) && $site_specific['confg']['checkout']['billing']['company']['show'] == true )
                                                <div class="col-xl-12">
                                                    <div class="form-group"><label for="exampleInputText1">Company Name(Optional)</label>
                                                        <input type="text" formcontrolname="company" class="form-control ng-untouched ng-pristine ng-valid">
                                                    </div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-12">
                                                    <div class="form-group"><label for="exampleInputText1">Country</label><sup style="color: red;">*</sup>
                                                        <select formcontrolname="country" id="billing_country"
                                                            class="form-control m-input dropdown-cls ng-untouched ng-pristine ng-valid">
                                                            <option value="af"> Afghanistan </option>
                                                            <option value="al"> Albania </option>
                                                            <option value="dz"> Algeria </option>
                                                            <option value="ad"> Andorra </option>
                                                            <option value="ao"> Angola </option>
                                                            <option value="ai"> Anguilla </option>
                                                            <option value="ag"> Antigua and Barbuda </option>
                                                            <option value="ar"> Argentina </option>
                                                            <option value="am"> Armenia </option>
                                                            <option value="aw"> Aruba </option>
                                                            <option value="au"> Australia </option>
                                                            <option value="at"> Austria </option>
                                                            <option value="az"> Azerbaijan </option>
                                                            <option value="bs"> Bahamas </option>
                                                            <option value="bh"> Bahrain </option>
                                                            <option value="bd"> Bangladesh </option>
                                                            <option value="bb"> Barbados </option>
                                                            <option value="by"> Belarus </option>
                                                            <option value="be"> Belgium </option>
                                                            <option value="bz"> Belize </option>
                                                            <option value="bj"> Benin </option>
                                                            <option value="bm"> Bermuda </option>
                                                            <option value="bt"> Bhutan </option>
                                                            <option value="bo"> Bolivia </option>
                                                            <option value="ba"> Bosnia and Herzegovina </option>
                                                            <option value="bw"> Botswana </option>
                                                            <option value="br"> Brazil </option>
                                                            <option value="bn"> Brunei Darussalam </option>
                                                            <option value="bg"> Bulgaria </option>
                                                            <option value="bf"> Burkina Faso </option>
                                                            <option value="bi"> Burundi </option>
                                                            <option value="kh"> Cambodia </option>
                                                            <option value="cm"> Cameroon </option>
                                                            <option value="ca"> Canada </option>
                                                            <option value="cv"> Cape Verde </option>
                                                            <option value="ky"> Cayman Islands </option>
                                                            <option value="cf"> Central African Republic </option>
                                                            <option value="td"> Chad </option>
                                                            <option value="cl"> Chile </option>
                                                            <option value="cn"> China </option>
                                                            <option value="cx"> Christmas Island </option>
                                                            <option value="cc"> Cocos (Keeling) Islands </option>
                                                            <option value="co"> Colombia </option>
                                                            <option value="km"> Comoros </option>
                                                            <option value="cg"> Congo </option>
                                                            <option value="ck"> Cook Islands </option>
                                                            <option value="cr"> Costa Rica </option>
                                                            <option value="ci"> Cote D'Ivoire (Ivory Coast) </option>
                                                            <option value="hr"> Croatia (Hrvatska) </option>
                                                            <option value="cu"> Cuba </option>
                                                            <option value="cy"> Cyprus </option>
                                                            <option value="cz"> Czech Republic </option>
                                                            <option value="cd"> Democratic Republic of the Congo </option>
                                                            <option value="dk"> Denmark </option>
                                                            <option value="dj"> Djibouti </option>
                                                            <option value="dm"> Dominica </option>
                                                            <option value="do"> Dominican Republic </option>
                                                            <option value="ec"> Ecuador </option>
                                                            <option value="eg"> Egypt </option>
                                                            <option value="sv"> El Salvador </option>
                                                            <option value="gq"> Equatorial Guinea </option>
                                                            <option value="er"> Eritrea </option>
                                                            <option value="ee"> Estonia </option>
                                                            <option value="et"> Ethiopia </option>
                                                            <option value="fk"> Falkland Islands (Malvinas) </option>
                                                            <option value="fo"> Faroe Islands </option>
                                                            <option value="fm"> Federated States of Micronesia </option>
                                                            <option value="fj"> Fiji </option>
                                                            <option value="fi"> Finland </option>
                                                            <option value="fr"> France </option>
                                                            <option value="gf"> French Guiana </option>
                                                            <option value="pf"> French Polynesia </option>
                                                            <option value="tf"> French Southern Territories </option>
                                                            <option value="ga"> Gabon </option>
                                                            <option value="gm"> Gambia </option>
                                                            <option value="ge"> Georgia </option>
                                                            <option value="de"> Germany </option>
                                                            <option value="gh"> Ghana </option>
                                                            <option value="gi"> Gibraltar </option>
                                                            <option value="gr"> Greece </option>
                                                            <option value="gl"> Greenland </option>
                                                            <option value="gd"> Grenada </option>
                                                            <option value="gp"> Guadeloupe </option>
                                                            <option value="gt"> Guatemala </option>
                                                            <option value="gn"> Guinea </option>
                                                            <option value="gw"> Guinea-Bissau </option>
                                                            <option value="gy"> Guyana </option>
                                                            <option value="ht"> Haiti </option>
                                                            <option value="hn"> Honduras </option>
                                                            <option value="hk"> Hong Kong </option>
                                                            <option value="hu"> Hungary </option>
                                                            <option value="is"> Iceland </option>
                                                            <option value="in"> India </option>
                                                            <option value="id"> Indonesia </option>
                                                            <option value="ir"> Iran </option>
                                                            <option value="iq"> Iraq </option>
                                                            <option value="ie"> Ireland </option>
                                                            <option value="il"> Israel </option>
                                                            <option value="it"> Italy </option>
                                                            <option value="jm"> Jamaica </option>
                                                            <option value="jp"> Japan </option>
                                                            <option value="jo"> Jordan </option>
                                                            <option value="kz"> Kazakhstan </option>
                                                            <option value="ke"> Kenya </option>
                                                            <option value="ki"> Kiribati </option>
                                                            <option value="kp"> Korea (North) </option>
                                                            <option value="kr"> Korea (South) </option>
                                                            <option value="kw"> Kuwait </option>
                                                            <option value="kg"> Kyrgyzstan </option>
                                                            <option value="la"> Laos </option>
                                                            <option value="lv"> Latvia </option>
                                                            <option value="lb"> Lebanon </option>
                                                            <option value="ls"> Lesotho </option>
                                                            <option value="lr"> Liberia </option>
                                                            <option value="ly"> Libya </option>
                                                            <option value="li"> Liechtenstein </option>
                                                            <option value="lt"> Lithuania </option>
                                                            <option value="lu"> Luxembourg </option>
                                                            <option value="mo"> Macao </option>
                                                            <option value="mk"> Macedonia </option>
                                                            <option value="mg"> Madagascar </option>
                                                            <option value="mw"> Malawi </option>
                                                            <option value="my"> Malaysia </option>
                                                            <option value="mv"> Maldives </option>
                                                            <option value="ml"> Mali </option>
                                                            <option value="mt"> Malta </option>
                                                            <option value="mh"> Marshall Islands </option>
                                                            <option value="mq"> Martinique </option>
                                                            <option value="mr"> Mauritania </option>
                                                            <option value="mu"> Mauritius </option>
                                                            <option value="yt"> Mayotte </option>
                                                            <option value="mx"> Mexico </option>
                                                            <option value="md"> Moldova </option>
                                                            <option value="mc"> Monaco </option>
                                                            <option value="mn"> Mongolia </option>
                                                            <option value="ms"> Montserrat </option>
                                                            <option value="ma"> Morocco </option>
                                                            <option value="mz"> Mozambique </option>
                                                            <option value="mm"> Myanmar </option>
                                                            <option value="na"> Namibia </option>
                                                            <option value="nr"> Nauru </option>
                                                            <option value="np"> Nepal </option>
                                                            <option value="nl"> Netherlands </option>
                                                            <option value="an"> Netherlands Antilles </option>
                                                            <option value="nc"> New Caledonia </option>
                                                            <option value="nz"> New Zealand (Aotearoa) </option>
                                                            <option value="ni"> Nicaragua </option>
                                                            <option value="ne"> Niger </option>
                                                            <option value="ng"> Nigeria </option>
                                                            <option value="nu"> Niue </option>
                                                            <option value="nf"> Norfolk Island </option>
                                                            <option value="mp"> Northern Mariana Islands </option>
                                                            <option value="no"> Norway </option>
                                                            <option value="gg"> NULL </option>
                                                            <option value="om"> Oman </option>
                                                            <option value="pk"> Pakistan </option>
                                                            <option value="pw"> Palau </option>
                                                            <option value="ps"> Palestinian Territory </option>
                                                            <option value="pa"> Panama </option>
                                                            <option value="pg"> Papua New Guinea </option>
                                                            <option value="py"> Paraguay </option>
                                                            <option value="pe"> Peru </option>
                                                            <option value="ph"> Philippines </option>
                                                            <option value="pn"> Pitcairn </option>
                                                            <option value="pl"> Poland </option>
                                                            <option value="pt"> Portugal </option>
                                                            <option value="qa"> Qatar </option>
                                                            <option value="re"> Reunion </option>
                                                            <option value="ro"> Romania </option>
                                                            <option value="ru"> Russian Federation </option>
                                                            <option value="rw"> Rwanda </option>
                                                            <option value="gs"> S. Georgia and S. Sandwich Islands </option>
                                                            <option value="sh"> Saint Helena </option>
                                                            <option value="kn"> Saint Kitts and Nevis </option>
                                                            <option value="lc"> Saint Lucia </option>
                                                            <option value="pm"> Saint Pierre and Miquelon </option>
                                                            <option value="vc"> Saint Vincent and the Grenadines </option>
                                                            <option value="ws"> Samoa </option>
                                                            <option value="sm"> San Marino </option>
                                                            <option value="st"> Sao Tome and Principe </option>
                                                            <option value="sa"> Saudi Arabia </option>
                                                            <option value="sn"> Senegal </option>
                                                            <option value="sc"> Seychelles </option>
                                                            <option value="sl"> Sierra Leone </option>
                                                            <option value="sg"> Singapore </option>
                                                            <option value="sk"> Slovakia </option>
                                                            <option value="si"> Slovenia </option>
                                                            <option value="sb"> Solomon Islands </option>
                                                            <option value="so"> Somalia </option>
                                                            <option value="za"> South Africa </option>
                                                            <option value="es"> Spain </option>
                                                            <option value="lk"> Sri Lanka </option>
                                                            <option value="sd"> Sudan </option>
                                                            <option value="sr"> Suriname </option>
                                                            <option value="sj"> Svalbard and Jan Mayen </option>
                                                            <option value="sz"> Swaziland </option>
                                                            <option value="se"> Sweden </option>
                                                            <option value="ch"> Switzerland </option>
                                                            <option value="sy"> Syria </option>
                                                            <option value="tw"> Taiwan </option>
                                                            <option value="tj"> Tajikistan </option>
                                                            <option value="tz"> Tanzania </option>
                                                            <option value="th"> Thailand </option>
                                                            <option value="tg"> Togo </option>
                                                            <option value="tk"> Tokelau </option>
                                                            <option value="to"> Tonga </option>
                                                            <option value="tt"> Trinidad and Tobago </option>
                                                            <option value="tn"> Tunisia </option>
                                                            <option value="tr"> Turkey </option>
                                                            <option value="tm"> Turkmenistan </option>
                                                            <option value="tc"> Turks and Caicos Islands </option>
                                                            <option value="tv"> Tuvalu </option>
                                                            <option value="ug"> Uganda </option>
                                                            <option value="ua"> Ukraine </option>
                                                            <option value="ae"> United Arab Emirates </option>
                                                            <option value="gb"> United Kingdom </option>
                                                            <option value="us" selected> United States of America </option>
                                                            <option value="uy"> Uruguay </option>
                                                            <option value="uz"> Uzbekistan </option>
                                                            <option value="vu"> Vanuatu </option>
                                                            <option value="ve"> Venezuela </option>
                                                            <option value="vn"> Viet Nam </option>
                                                            <option value="vg"> Virgin Islands (British) </option>
                                                            <option value="vi"> Virgin Islands (U.S.) </option>
                                                            <option value="wf"> Wallis and Futuna </option>
                                                            <option value="eh"> Western Sahara </option>
                                                            <option value="ye"> Yemen </option>
                                                            <option value="zr"> Zaire (former) </option>
                                                            <option value="zm"> Zambia </option>
                                                            <option value="zw"> Zimbabwe </option>
                                                        </select></div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                    <label for="billing_address_line1">Address Line 1</label>
                                                        <sup style="color: red;">*</sup>
                                                        <app-address-google-search>
                                                            <input 
                                                                id="billing_address_line1" type="text" name="location" autocomplete="off" ngx-gp-autocomplete=""
                                                                class="form-control m-input ng-untouched ng-pristine ng-valid pac-target-input"
                                                                placeholder="Enter a location">
                                                        </app-address-google-search>
                                                    </div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="exampleInputText1">Address Line 2</label>
                                                        <input type="text" id="billing_address_line2" autocomplete="billing address-line2" formcontrolname="address_line2"
                                                            class="form-control ng-untouched ng-pristine ng-valid">
                                                    </div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="billing_city">City</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input type="text" id="billing_city" autocomplete="billing address-level2" formcontrolname="city"
                                                            class="form-control ng-untouched ng-pristine ng-invalid">
                                                    </div>
                                                </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="billing_state">State</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input type="text" id="billing_state" formcontrolname="state" autocomplete="billing region"
                                                            class="form-control ng-untouched ng-pristine ng-invalid"></div>
                                                    </div>
                                                @endif

                                                @if( isset($site_specific['confg']['checkout']['billing']['address']['show']) && $site_specific['confg']['checkout']['billing']['address']['show'] == true )
                                                <div class="col-xl-6">
                                                    <div class="form-group">
                                                        <label for="billing_zipcode"> Zipcode</label>
                                                        <sup style="color: red;">*</sup>
                                                        <input type="text" id="billing_zipcode" autocomplete="billing postal-code" formcontrolname="zipcode"
                                                            class="form-control ng-untouched ng-pristine ng-invalid">
                                                    </div>
                                                </div>
                                                @endif
                                            </div>


                                            <div class="mt-3">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="checkout-header">
                                                            <h5 class="mb-0"><i class="fa fa-plus main"></i><i class="fa fa-angle-double-right ml-3"></i> {{ $site_specific['checkout_info']['title_custom_checkout'] ?? 'Custom checkout information' }} </h5>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div formgroupname="custom_checkout" class="ng-untouched ng-pristine ng-invalid">
                                                    <div formarrayname="fields" class="ng-untouched ng-pristine ng-invalid">
                                                        <div class="row mt-4">
                                                            @foreach($customFields as $field)
                                                                <div class="col-md-6">
                                                                    <div class="checkout-custom-fields">
                                                                        <div class="form-group">
                                                                            <label for="">{{ $field['field_label'] }}</label>
                                                                            @if( $field['field_is_required'] == 1 )
                                                                                <label class="colorPurpel">
                                                                                    <sup style="color: red;">*</sup>
                                                                                </label>
                                                                            @endif
                                                                            @if($field['field_type'] == 1)
                                                                            <select class="form-control" name="custom_fields[{{ $field['field_name'] }}]">
                                                                                @if( !empty($field['field_values']) )
                                                                                    <option value=""> -Select-- </option>
                                                                                    @foreach(explode(';',$field['field_values']) as $value)
                                                                                        <option                                                                                         data-id="{{ $field['id'] }}"
                                                                                            data-type="{{ $field['field_type'] }}"
                                                                                            data-name="{{ $field['field_name'] }}"
                                                                                            data-label="{{ $field['field_label'] }}"
                                                                                            name="name2"
                                                                                            value="{{ $value }}"
                                                                                        >{{ $value }}</option>
                                                                                    @endforeach
                                                                                @endif
                                                                                </select>                                                                                
                                                                            @else
                                                                                <input 
                                                                                    data-id="{{ $field['id'] }}"
                                                                                    data-type="{{ $field['field_type'] }}"
                                                                                    data-name="{{ $field['field_name'] }}"
                                                                                    data-label="{{ $field['field_label'] }}"
                                                                                    name="custom_fields[{{ $field['field_name'] }}]"
                                                                                    type="text"
                                                                                    autocomplete="none"
                                                                                    class="form-control"
                                                                                >
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>



                                    </app-billing-info>
                                </div>
                                @if( !isset($site_specific['confg']['checkout']['hide_fulfillment']) || $site_specific['confg']['checkout']['hide_fulfillment'] == false )
                                <app-checkout-fullfilment>
                                    <div class="custom-alert col-md-6 mb-5"></div>
                                    <div class="billingdetails-leftside-inner fulfillment-form-area">
                                        <h2 class="wc-checkout-title pt-0 pb-3">Fulfillment</h2>
                                        <div class="tab-button-fullfillment">
                                            <ul id="myTab" role="tablist" class="nav nav-tabs fullfilment-tab shipping-tab mt-4">
                                                <li class="nav-item"><a id="shipping-tab" data-toggle="tab" href="#shipping" role="tab" aria-controls="shipping" aria-selected="false" class="nav-link active">
                                                        <div class="fullfilment-btn"><img src="../assets/img/home/<USER>/shipping.png" alt="" class="icon"><img src="../assets/img/home/<USER>/shipping-white.png" alt="" class="icon-active"></div>
                                                        <h5>Shipping </h5>
                                                    </a></li>
                                                @php /*
                                                <li class="nav-item"><a id="delivery-tab" data-toggle="tab" href="#delivery" role="tab" aria-controls="delivery" aria-selected="false" class="nav-link">
                                                        <div class="fullfilment-btn"><img src="../assets/img/home/<USER>/delivery.png" alt="" class="icon"><img src="../assets/img/home/<USER>/delivery-white.png" alt="" class="icon-active"></div>
                                                        <h5> Delivery </h5>
                                                    </a></li>
                                                */ @endphp
                                            </ul>
                                            <div id="myTabContent" class="tab-content">
                                                <div id="pickup" role="tabpanel" aria-labelledby="pickup-tab" class="tab-pane fade"></div>
                                                <div id="shipping" role="tabpanel" aria-labelledby="shipping-tab" class="tab-pane fade show active">
                                                    <div class="card card-body p-0"><app-shipping-overall-checkout>
                                                            @php /*
                                                            <div class="checkbox"><label class="m-checkbox"><input type="checkbox" id="sameAsAbove" name="same_as_above" class="ng-untouched ng-pristine ng-valid"> Same As Billing Address <span style="top: 0px;"></span></label></div>
                                                            */ @endphp
                                                            <br/>
                                                            <app-overall-delivery-address>
                                                                <div class="custom-alert"></div>
                                                                <div class="order_details block">
                                                                        <div class="col-md-12 order_details">
                                                                            <div class="row">
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="shippingFirstName">First Name</label><sup style="color: red;">*</sup><input type="text" id="shippingFirstName" formcontrolname="shipping_first_name" autocomplete="shipping first-name" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-valid"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="shippingLastName">Last Name</label><input type="text" id="shippingLastName" formcontrolname="shipping_last_name" autocomplete="shipping last name" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-valid"></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="shippingEmail"> Email Name </label><sup style="color: red;">*</sup><input type="email" formcontrolname="shipping_email" autocomplete="shipping email" id="shippingEmail" class="form-control ng-untouched ng-pristine ng-valid"></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Country</label><sup style="color: red;">*</sup><select formcontrolname="shipping_country" id="country_online" class="form-control dropdown-cls m-input ng-untouched ng-pristine ng-valid" name="country">
                                                                                            <option value="af">Afghanistan</option>
                                                                                            <option value="al">Albania</option>
                                                                                            <option value="dz">Algeria</option>
                                                                                            <option value="ad">Andorra</option>
                                                                                            <option value="ao">Angola</option>
                                                                                            <option value="ai">Anguilla</option>
                                                                                            <option value="ag">Antigua and Barbuda</option>
                                                                                            <option value="ar">Argentina</option>
                                                                                            <option value="am">Armenia</option>
                                                                                            <option value="aw">Aruba</option>
                                                                                            <option value="au">Australia</option>
                                                                                            <option value="at">Austria</option>
                                                                                            <option value="az">Azerbaijan</option>
                                                                                            <option value="bs">Bahamas</option>
                                                                                            <option value="bh">Bahrain</option>
                                                                                            <option value="bd">Bangladesh</option>
                                                                                            <option value="bb">Barbados</option>
                                                                                            <option value="by">Belarus</option>
                                                                                            <option value="be">Belgium</option>
                                                                                            <option value="bz">Belize</option>
                                                                                            <option value="bj">Benin</option>
                                                                                            <option value="bm">Bermuda</option>
                                                                                            <option value="bt">Bhutan</option>
                                                                                            <option value="bo">Bolivia</option>
                                                                                            <option value="ba">Bosnia and Herzegovina</option>
                                                                                            <option value="bw">Botswana</option>
                                                                                            <option value="br">Brazil</option>
                                                                                            <option value="bn">Brunei Darussalam</option>
                                                                                            <option value="bg">Bulgaria</option>
                                                                                            <option value="bf">Burkina Faso</option>
                                                                                            <option value="bi">Burundi</option>
                                                                                            <option value="kh">Cambodia</option>
                                                                                            <option value="cm">Cameroon</option>
                                                                                            <option value="ca">Canada</option>
                                                                                            <option value="cv">Cape Verde</option>
                                                                                            <option value="ky">Cayman Islands</option>
                                                                                            <option value="cf">Central African Republic</option>
                                                                                            <option value="td">Chad</option>
                                                                                            <option value="cl">Chile</option>
                                                                                            <option value="cn">China</option>
                                                                                            <option value="cx">Christmas Island</option>
                                                                                            <option value="cc">Cocos (Keeling) Islands</option>
                                                                                            <option value="co">Colombia</option>
                                                                                            <option value="km">Comoros</option>
                                                                                            <option value="cg">Congo</option>
                                                                                            <option value="ck">Cook Islands</option>
                                                                                            <option value="cr">Costa Rica</option>
                                                                                            <option value="ci">Cote D'Ivoire (Ivory Coast)</option>
                                                                                            <option value="hr">Croatia (Hrvatska)</option>
                                                                                            <option value="cu">Cuba</option>
                                                                                            <option value="cy">Cyprus</option>
                                                                                            <option value="cz">Czech Republic</option>
                                                                                            <option value="cd">Democratic Republic of the Congo</option>
                                                                                            <option value="dk">Denmark</option>
                                                                                            <option value="dj">Djibouti</option>
                                                                                            <option value="dm">Dominica</option>
                                                                                            <option value="do">Dominican Republic</option>
                                                                                            <option value="ec">Ecuador</option>
                                                                                            <option value="eg">Egypt</option>
                                                                                            <option value="sv">El Salvador</option>
                                                                                            <option value="gq">Equatorial Guinea</option>
                                                                                            <option value="er">Eritrea</option>
                                                                                            <option value="ee">Estonia</option>
                                                                                            <option value="et">Ethiopia</option>
                                                                                            <option value="fk">Falkland Islands (Malvinas)</option>
                                                                                            <option value="fo">Faroe Islands</option>
                                                                                            <option value="fm">Federated States of Micronesia</option>
                                                                                            <option value="fj">Fiji</option>
                                                                                            <option value="fi">Finland</option>
                                                                                            <option value="fr">France</option>
                                                                                            <option value="gf">French Guiana</option>
                                                                                            <option value="pf">French Polynesia</option>
                                                                                            <option value="tf">French Southern Territories</option>
                                                                                            <option value="ga">Gabon</option>
                                                                                            <option value="gm">Gambia</option>
                                                                                            <option value="ge">Georgia</option>
                                                                                            <option value="de">Germany</option>
                                                                                            <option value="gh">Ghana</option>
                                                                                            <option value="gi">Gibraltar</option>
                                                                                            <option value="gr">Greece</option>
                                                                                            <option value="gl">Greenland</option>
                                                                                            <option value="gd">Grenada</option>
                                                                                            <option value="gp">Guadeloupe</option>
                                                                                            <option value="gt">Guatemala</option>
                                                                                            <option value="gn">Guinea</option>
                                                                                            <option value="gw">Guinea-Bissau</option>
                                                                                            <option value="gy">Guyana</option>
                                                                                            <option value="ht">Haiti</option>
                                                                                            <option value="hn">Honduras</option>
                                                                                            <option value="hk">Hong Kong</option>
                                                                                            <option value="hu">Hungary</option>
                                                                                            <option value="is">Iceland</option>
                                                                                            <option value="in">India</option>
                                                                                            <option value="id">Indonesia</option>
                                                                                            <option value="ir">Iran</option>
                                                                                            <option value="iq">Iraq</option>
                                                                                            <option value="ie">Ireland</option>
                                                                                            <option value="il">Israel</option>
                                                                                            <option value="it">Italy</option>
                                                                                            <option value="jm">Jamaica</option>
                                                                                            <option value="jp">Japan</option>
                                                                                            <option value="jo">Jordan</option>
                                                                                            <option value="kz">Kazakhstan</option>
                                                                                            <option value="ke">Kenya</option>
                                                                                            <option value="ki">Kiribati</option>
                                                                                            <option value="kp">Korea (North)</option>
                                                                                            <option value="kr">Korea (South)</option>
                                                                                            <option value="kw">Kuwait</option>
                                                                                            <option value="kg">Kyrgyzstan</option>
                                                                                            <option value="la">Laos</option>
                                                                                            <option value="lv">Latvia</option>
                                                                                            <option value="lb">Lebanon</option>
                                                                                            <option value="ls">Lesotho</option>
                                                                                            <option value="lr">Liberia</option>
                                                                                            <option value="ly">Libya</option>
                                                                                            <option value="li">Liechtenstein</option>
                                                                                            <option value="lt">Lithuania</option>
                                                                                            <option value="lu">Luxembourg</option>
                                                                                            <option value="mo">Macao</option>
                                                                                            <option value="mk">Macedonia</option>
                                                                                            <option value="mg">Madagascar</option>
                                                                                            <option value="mw">Malawi</option>
                                                                                            <option value="my">Malaysia</option>
                                                                                            <option value="mv">Maldives</option>
                                                                                            <option value="ml">Mali</option>
                                                                                            <option value="mt">Malta</option>
                                                                                            <option value="mh">Marshall Islands</option>
                                                                                            <option value="mq">Martinique</option>
                                                                                            <option value="mr">Mauritania</option>
                                                                                            <option value="mu">Mauritius</option>
                                                                                            <option value="yt">Mayotte</option>
                                                                                            <option value="mx">Mexico</option>
                                                                                            <option value="md">Moldova</option>
                                                                                            <option value="mc">Monaco</option>
                                                                                            <option value="mn">Mongolia</option>
                                                                                            <option value="ms">Montserrat</option>
                                                                                            <option value="ma">Morocco</option>
                                                                                            <option value="mz">Mozambique</option>
                                                                                            <option value="mm">Myanmar</option>
                                                                                            <option value="na">Namibia</option>
                                                                                            <option value="nr">Nauru</option>
                                                                                            <option value="np">Nepal</option>
                                                                                            <option value="nl">Netherlands</option>
                                                                                            <option value="an">Netherlands Antilles</option>
                                                                                            <option value="nc">New Caledonia</option>
                                                                                            <option value="nz">New Zealand (Aotearoa)</option>
                                                                                            <option value="ni">Nicaragua</option>
                                                                                            <option value="ne">Niger</option>
                                                                                            <option value="ng">Nigeria</option>
                                                                                            <option value="nu">Niue</option>
                                                                                            <option value="nf">Norfolk Island</option>
                                                                                            <option value="mp">Northern Mariana Islands</option>
                                                                                            <option value="no">Norway</option>
                                                                                            <option value="gg">NULL</option>
                                                                                            <option value="om">Oman</option>
                                                                                            <option value="pk">Pakistan</option>
                                                                                            <option value="pw">Palau</option>
                                                                                            <option value="ps">Palestinian Territory</option>
                                                                                            <option value="pa">Panama</option>
                                                                                            <option value="pg">Papua New Guinea</option>
                                                                                            <option value="py">Paraguay</option>
                                                                                            <option value="pe">Peru</option>
                                                                                            <option value="ph">Philippines</option>
                                                                                            <option value="pn">Pitcairn</option>
                                                                                            <option value="pl">Poland</option>
                                                                                            <option value="pt">Portugal</option>
                                                                                            <option value="qa">Qatar</option>
                                                                                            <option value="re">Reunion</option>
                                                                                            <option value="ro">Romania</option>
                                                                                            <option value="ru">Russian Federation</option>
                                                                                            <option value="rw">Rwanda</option>
                                                                                            <option value="gs">S. Georgia and S. Sandwich Islands</option>
                                                                                            <option value="sh">Saint Helena</option>
                                                                                            <option value="kn">Saint Kitts and Nevis</option>
                                                                                            <option value="lc">Saint Lucia</option>
                                                                                            <option value="pm">Saint Pierre and Miquelon</option>
                                                                                            <option value="vc">Saint Vincent and the Grenadines</option>
                                                                                            <option value="ws">Samoa</option>
                                                                                            <option value="sm">San Marino</option>
                                                                                            <option value="st">Sao Tome and Principe</option>
                                                                                            <option value="sa">Saudi Arabia</option>
                                                                                            <option value="sn">Senegal</option>
                                                                                            <option value="sc">Seychelles</option>
                                                                                            <option value="sl">Sierra Leone</option>
                                                                                            <option value="sg">Singapore</option>
                                                                                            <option value="sk">Slovakia</option>
                                                                                            <option value="si">Slovenia</option>
                                                                                            <option value="sb">Solomon Islands</option>
                                                                                            <option value="so">Somalia</option>
                                                                                            <option value="za">South Africa</option>
                                                                                            <option value="es">Spain</option>
                                                                                            <option value="lk">Sri Lanka</option>
                                                                                            <option value="sd">Sudan</option>
                                                                                            <option value="sr">Suriname</option>
                                                                                            <option value="sj">Svalbard and Jan Mayen</option>
                                                                                            <option value="sz">Swaziland</option>
                                                                                            <option value="se">Sweden</option>
                                                                                            <option value="ch">Switzerland</option>
                                                                                            <option value="sy">Syria</option>
                                                                                            <option value="tw">Taiwan</option>
                                                                                            <option value="tj">Tajikistan</option>
                                                                                            <option value="tz">Tanzania</option>
                                                                                            <option value="th">Thailand</option>
                                                                                            <option value="tg">Togo</option>
                                                                                            <option value="tk">Tokelau</option>
                                                                                            <option value="to">Tonga</option>
                                                                                            <option value="tt">Trinidad and Tobago</option>
                                                                                            <option value="tn">Tunisia</option>
                                                                                            <option value="tr">Turkey</option>
                                                                                            <option value="tm">Turkmenistan</option>
                                                                                            <option value="tc">Turks and Caicos Islands</option>
                                                                                            <option value="tv">Tuvalu</option>
                                                                                            <option value="ug">Uganda</option>
                                                                                            <option value="ua">Ukraine</option>
                                                                                            <option value="ae">United Arab Emirates</option>
                                                                                            <option value="gb">United Kingdom</option>
                                                                                            <option value="us" selected>United States of America</option>
                                                                                            <option value="uy">Uruguay</option>
                                                                                            <option value="uz">Uzbekistan</option>
                                                                                            <option value="vu">Vanuatu</option>
                                                                                            <option value="ve">Venezuela</option>
                                                                                            <option value="vn">Viet Nam</option>
                                                                                            <option value="vg">Virgin Islands (British)</option>
                                                                                            <option value="vi">Virgin Islands (U.S.)</option>
                                                                                            <option value="wf">Wallis and Futuna</option>
                                                                                            <option value="eh">Western Sahara</option>
                                                                                            <option value="ye">Yemen</option>
                                                                                            <option value="zr">Zaire (former)</option>
                                                                                            <option value="zm">Zambia</option>
                                                                                            <option value="zw">Zimbabwe</option>
                                                                                        </select></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Address Line 1*</label><sup style="color: red;">*</sup><app-address-google-search ><input id="address_line1" type="text" autocomplete="off" ngx-gp-autocomplete="" class="form-control m-input ng-untouched ng-pristine ng-valid pac-target-input" placeholder="Enter a location" name="address_line1">
                                                                                    </app-address-google-search></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Address Line 2</label><input type="text" id="address_line2" formcontrolname="shipping_address2" autocomplete="shipping address-line2" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-valid" name="address_line2"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">City*</label><sup style="color: red;">*</sup><input type="text" id="sh_city" formcontrolname="city" autocomplete="address-level2" class="form-control ng-untouched ng-pristine ng-invalid"
                                                                                    name="city"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">State*</label><sup style="color: red;">*</sup><input type="text" id="sh_state" formcontrolname="state" autocomplete="shipping address-level1" class="col-md-12 form-control ng-untouched ng-pristine ng-invalid" name="state"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">Zipcode*</label><sup style="color: red;">*</sup><input type="text" id="sh_zipcode" formcontrolname="shipping_zipcode" autocomplete="shipping postal-code" class="col-md-12 form-control ng-untouched ng-pristine ng-invalid" name="zipcode"></div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </app-overall-delivery-address>
                                                            <div class="row mt-0">
                                                                <div class="col-md-12"></div>
                                                            </div>
                                                            @php /*
                                                            <div class="row mt-3">
                                                                <div class="col-md-8 offset-md-4 text-right">
                                                                    <button type="button" class="btn theme-btn caps" onclick="shipping_rate()">Get shipping method &nbsp;<i class="fa fa-forward"></i></button>
                                                                </div>
                                                            </div>
                                                            */ @endphp
                                                        </app-shipping-overall-checkout>


@php /*
<div  class="row mt-0">
    <div  class="col-md-12 shipping-list-wrapper">
        <p  class="shipping-method-title"> Select shipping method </p>

    </div>
</div>
*/ @endphp

<style>
.service-part {
    padding: 10px 10px 6px !important;
    padding: 0 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
}

</style>


                                                    </div>
                                                </div>
                                                <div id="delivery" role="tabpanel" aria-labelledby="delivery-tab" class="tab-pane fade show">
                                                    <div class="card card-body p-0"><app-delivery-checkout>
                                                            <div class="checkbox"><label class="m-checkbox"><input type="checkbox" id="sameAsAbove" name="same_as_above" class="ng-untouched ng-pristine ng-valid"> Same As Billing Address <span style="top: 0px;"></span></label></div><app-overall-delivery-address _nghost-serverapp-c1230337839="">
                                                                <div class="custom-alert"></div>
                                                                <div class="order_details block">
                                                                        <div class="col-md-12 order_details">
                                                                            <div class="row">
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="shippingFirstName">First Name</label><sup style="color: red;">*</sup><input type="text" id="shippingFirstName" formcontrolname="shipping_first_name" autocomplete="shipping first-name" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-invalid"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="shippingLastName">Last Name</label><input type="text" id="shippingLastName" formcontrolname="shipping_last_name" autocomplete="shipping last name" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-valid"></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="shippingEmail"> Email Name </label><sup style="color: red;">*</sup><input type="email" formcontrolname="shipping_email" autocomplete="shipping email" id="shippingEmail" class="form-control ng-untouched ng-pristine ng-invalid"></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Country</label><sup style="color: red;">*</sup><select formcontrolname="shipping_country" id="country_online" class="form-control dropdown-cls m-input ng-untouched ng-pristine ng-valid">
                                                                                            <option value="af">Afghanistan</option>
                                                                                            <option value="al">Albania</option>
                                                                                            <option value="dz">Algeria</option>
                                                                                            <option value="ad">Andorra</option>
                                                                                            <option value="ao">Angola</option>
                                                                                            <option value="ai">Anguilla</option>
                                                                                            <option value="ag">Antigua and Barbuda</option>
                                                                                            <option value="ar">Argentina</option>
                                                                                            <option value="am">Armenia</option>
                                                                                            <option value="aw">Aruba</option>
                                                                                            <option value="au">Australia</option>
                                                                                            <option value="at">Austria</option>
                                                                                            <option value="az">Azerbaijan</option>
                                                                                            <option value="bs">Bahamas</option>
                                                                                            <option value="bh">Bahrain</option>
                                                                                            <option value="bd">Bangladesh</option>
                                                                                            <option value="bb">Barbados</option>
                                                                                            <option value="by">Belarus</option>
                                                                                            <option value="be">Belgium</option>
                                                                                            <option value="bz">Belize</option>
                                                                                            <option value="bj">Benin</option>
                                                                                            <option value="bm">Bermuda</option>
                                                                                            <option value="bt">Bhutan</option>
                                                                                            <option value="bo">Bolivia</option>
                                                                                            <option value="ba">Bosnia and Herzegovina</option>
                                                                                            <option value="bw">Botswana</option>
                                                                                            <option value="br">Brazil</option>
                                                                                            <option value="bn">Brunei Darussalam</option>
                                                                                            <option value="bg">Bulgaria</option>
                                                                                            <option value="bf">Burkina Faso</option>
                                                                                            <option value="bi">Burundi</option>
                                                                                            <option value="kh">Cambodia</option>
                                                                                            <option value="cm">Cameroon</option>
                                                                                            <option value="ca">Canada</option>
                                                                                            <option value="cv">Cape Verde</option>
                                                                                            <option value="ky">Cayman Islands</option>
                                                                                            <option value="cf">Central African Republic</option>
                                                                                            <option value="td">Chad</option>
                                                                                            <option value="cl">Chile</option>
                                                                                            <option value="cn">China</option>
                                                                                            <option value="cx">Christmas Island</option>
                                                                                            <option value="cc">Cocos (Keeling) Islands</option>
                                                                                            <option value="co">Colombia</option>
                                                                                            <option value="km">Comoros</option>
                                                                                            <option value="cg">Congo</option>
                                                                                            <option value="ck">Cook Islands</option>
                                                                                            <option value="cr">Costa Rica</option>
                                                                                            <option value="ci">Cote D'Ivoire (Ivory Coast)</option>
                                                                                            <option value="hr">Croatia (Hrvatska)</option>
                                                                                            <option value="cu">Cuba</option>
                                                                                            <option value="cy">Cyprus</option>
                                                                                            <option value="cz">Czech Republic</option>
                                                                                            <option value="cd">Democratic Republic of the Congo</option>
                                                                                            <option value="dk">Denmark</option>
                                                                                            <option value="dj">Djibouti</option>
                                                                                            <option value="dm">Dominica</option>
                                                                                            <option value="do">Dominican Republic</option>
                                                                                            <option value="ec">Ecuador</option>
                                                                                            <option value="eg">Egypt</option>
                                                                                            <option value="sv">El Salvador</option>
                                                                                            <option value="gq">Equatorial Guinea</option>
                                                                                            <option value="er">Eritrea</option>
                                                                                            <option value="ee">Estonia</option>
                                                                                            <option value="et">Ethiopia</option>
                                                                                            <option value="fk">Falkland Islands (Malvinas)</option>
                                                                                            <option value="fo">Faroe Islands</option>
                                                                                            <option value="fm">Federated States of Micronesia</option>
                                                                                            <option value="fj">Fiji</option>
                                                                                            <option value="fi">Finland</option>
                                                                                            <option value="fr">France</option>
                                                                                            <option value="gf">French Guiana</option>
                                                                                            <option value="pf">French Polynesia</option>
                                                                                            <option value="tf">French Southern Territories</option>
                                                                                            <option value="ga">Gabon</option>
                                                                                            <option value="gm">Gambia</option>
                                                                                            <option value="ge">Georgia</option>
                                                                                            <option value="de">Germany</option>
                                                                                            <option value="gh">Ghana</option>
                                                                                            <option value="gi">Gibraltar</option>
                                                                                            <option value="gr">Greece</option>
                                                                                            <option value="gl">Greenland</option>
                                                                                            <option value="gd">Grenada</option>
                                                                                            <option value="gp">Guadeloupe</option>
                                                                                            <option value="gt">Guatemala</option>
                                                                                            <option value="gn">Guinea</option>
                                                                                            <option value="gw">Guinea-Bissau</option>
                                                                                            <option value="gy">Guyana</option>
                                                                                            <option value="ht">Haiti</option>
                                                                                            <option value="hn">Honduras</option>
                                                                                            <option value="hk">Hong Kong</option>
                                                                                            <option value="hu">Hungary</option>
                                                                                            <option value="is">Iceland</option>
                                                                                            <option value="in">India</option>
                                                                                            <option value="id">Indonesia</option>
                                                                                            <option value="ir">Iran</option>
                                                                                            <option value="iq">Iraq</option>
                                                                                            <option value="ie">Ireland</option>
                                                                                            <option value="il">Israel</option>
                                                                                            <option value="it">Italy</option>
                                                                                            <option value="jm">Jamaica</option>
                                                                                            <option value="jp">Japan</option>
                                                                                            <option value="jo">Jordan</option>
                                                                                            <option value="kz">Kazakhstan</option>
                                                                                            <option value="ke">Kenya</option>
                                                                                            <option value="ki">Kiribati</option>
                                                                                            <option value="kp">Korea (North)</option>
                                                                                            <option value="kr">Korea (South)</option>
                                                                                            <option value="kw">Kuwait</option>
                                                                                            <option value="kg">Kyrgyzstan</option>
                                                                                            <option value="la">Laos</option>
                                                                                            <option value="lv">Latvia</option>
                                                                                            <option value="lb">Lebanon</option>
                                                                                            <option value="ls">Lesotho</option>
                                                                                            <option value="lr">Liberia</option>
                                                                                            <option value="ly">Libya</option>
                                                                                            <option value="li">Liechtenstein</option>
                                                                                            <option value="lt">Lithuania</option>
                                                                                            <option value="lu">Luxembourg</option>
                                                                                            <option value="mo">Macao</option>
                                                                                            <option value="mk">Macedonia</option>
                                                                                            <option value="mg">Madagascar</option>
                                                                                            <option value="mw">Malawi</option>
                                                                                            <option value="my">Malaysia</option>
                                                                                            <option value="mv">Maldives</option>
                                                                                            <option value="ml">Mali</option>
                                                                                            <option value="mt">Malta</option>
                                                                                            <option value="mh">Marshall Islands</option>
                                                                                            <option value="mq">Martinique</option>
                                                                                            <option value="mr">Mauritania</option>
                                                                                            <option value="mu">Mauritius</option>
                                                                                            <option value="yt">Mayotte</option>
                                                                                            <option value="mx">Mexico</option>
                                                                                            <option value="md">Moldova</option>
                                                                                            <option value="mc">Monaco</option>
                                                                                            <option value="mn">Mongolia</option>
                                                                                            <option value="ms">Montserrat</option>
                                                                                            <option value="ma">Morocco</option>
                                                                                            <option value="mz">Mozambique</option>
                                                                                            <option value="mm">Myanmar</option>
                                                                                            <option value="na">Namibia</option>
                                                                                            <option value="nr">Nauru</option>
                                                                                            <option value="np">Nepal</option>
                                                                                            <option value="nl">Netherlands</option>
                                                                                            <option value="an">Netherlands Antilles</option>
                                                                                            <option value="nc">New Caledonia</option>
                                                                                            <option value="nz">New Zealand (Aotearoa)</option>
                                                                                            <option value="ni">Nicaragua</option>
                                                                                            <option value="ne">Niger</option>
                                                                                            <option value="ng">Nigeria</option>
                                                                                            <option value="nu">Niue</option>
                                                                                            <option value="nf">Norfolk Island</option>
                                                                                            <option value="mp">Northern Mariana Islands</option>
                                                                                            <option value="no">Norway</option>
                                                                                            <option value="gg">NULL</option>
                                                                                            <option value="om">Oman</option>
                                                                                            <option value="pk">Pakistan</option>
                                                                                            <option value="pw">Palau</option>
                                                                                            <option value="ps">Palestinian Territory</option>
                                                                                            <option value="pa">Panama</option>
                                                                                            <option value="pg">Papua New Guinea</option>
                                                                                            <option value="py">Paraguay</option>
                                                                                            <option value="pe">Peru</option>
                                                                                            <option value="ph">Philippines</option>
                                                                                            <option value="pn">Pitcairn</option>
                                                                                            <option value="pl">Poland</option>
                                                                                            <option value="pt">Portugal</option>
                                                                                            <option value="qa">Qatar</option>
                                                                                            <option value="re">Reunion</option>
                                                                                            <option value="ro">Romania</option>
                                                                                            <option value="ru">Russian Federation</option>
                                                                                            <option value="rw">Rwanda</option>
                                                                                            <option value="gs">S. Georgia and S. Sandwich Islands</option>
                                                                                            <option value="sh">Saint Helena</option>
                                                                                            <option value="kn">Saint Kitts and Nevis</option>
                                                                                            <option value="lc">Saint Lucia</option>
                                                                                            <option value="pm">Saint Pierre and Miquelon</option>
                                                                                            <option value="vc">Saint Vincent and the Grenadines</option>
                                                                                            <option value="ws">Samoa</option>
                                                                                            <option value="sm">San Marino</option>
                                                                                            <option value="st">Sao Tome and Principe</option>
                                                                                            <option value="sa">Saudi Arabia</option>
                                                                                            <option value="sn">Senegal</option>
                                                                                            <option value="sc">Seychelles</option>
                                                                                            <option value="sl">Sierra Leone</option>
                                                                                            <option value="sg">Singapore</option>
                                                                                            <option value="sk">Slovakia</option>
                                                                                            <option value="si">Slovenia</option>
                                                                                            <option value="sb">Solomon Islands</option>
                                                                                            <option value="so">Somalia</option>
                                                                                            <option value="za">South Africa</option>
                                                                                            <option value="es">Spain</option>
                                                                                            <option value="lk">Sri Lanka</option>
                                                                                            <option value="sd">Sudan</option>
                                                                                            <option value="sr">Suriname</option>
                                                                                            <option value="sj">Svalbard and Jan Mayen</option>
                                                                                            <option value="sz">Swaziland</option>
                                                                                            <option value="se">Sweden</option>
                                                                                            <option value="ch">Switzerland</option>
                                                                                            <option value="sy">Syria</option>
                                                                                            <option value="tw">Taiwan</option>
                                                                                            <option value="tj">Tajikistan</option>
                                                                                            <option value="tz">Tanzania</option>
                                                                                            <option value="th">Thailand</option>
                                                                                            <option value="tg">Togo</option>
                                                                                            <option value="tk">Tokelau</option>
                                                                                            <option value="to">Tonga</option>
                                                                                            <option value="tt">Trinidad and Tobago</option>
                                                                                            <option value="tn">Tunisia</option>
                                                                                            <option value="tr">Turkey</option>
                                                                                            <option value="tm">Turkmenistan</option>
                                                                                            <option value="tc">Turks and Caicos Islands</option>
                                                                                            <option value="tv">Tuvalu</option>
                                                                                            <option value="ug">Uganda</option>
                                                                                            <option value="ua">Ukraine</option>
                                                                                            <option value="ae">United Arab Emirates</option>
                                                                                            <option value="gb">United Kingdom</option>
                                                                                            <option value="us" selected>United States of America</option>
                                                                                            <option value="uy">Uruguay</option>
                                                                                            <option value="uz">Uzbekistan</option>
                                                                                            <option value="vu">Vanuatu</option>
                                                                                            <option value="ve">Venezuela</option>
                                                                                            <option value="vn">Viet Nam</option>
                                                                                            <option value="vg">Virgin Islands (British)</option>
                                                                                            <option value="vi">Virgin Islands (U.S.)</option>
                                                                                            <option value="wf">Wallis and Futuna</option>
                                                                                            <option value="eh">Western Sahara</option>
                                                                                            <option value="ye">Yemen</option>
                                                                                            <option value="zr">Zaire (former)</option>
                                                                                            <option value="zm">Zambia</option>
                                                                                            <option value="zw">Zimbabwe</option>
                                                                                        </select></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Address Line 1*</label><sup style="color: red;">*</sup><app-address-google-search ><input id="address" type="text" name="shipping_address1" autocomplete="off" ngx-gp-autocomplete="" class="form-control m-input ng-untouched ng-pristine ng-valid pac-target-input" placeholder="Enter a location"></app-address-google-search></div>
                                                                                </div>
                                                                                <div class="col-md-12">
                                                                                    <div class="form-group"><label for="">Address Line 2</label><input type="text" id="sh_address2" formcontrolname="shipping_address2" autocomplete="shipping address-line2" class="col-md-12 mb-2 form-control ng-untouched ng-pristine ng-valid"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">City*</label><sup style="color: red;">*</sup><input type="text" id="sh_city" formcontrolname="shipping_city" autocomplete="address-level2" class="form-control ng-untouched ng-pristine ng-invalid"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">State*</label><sup style="color: red;">*</sup><input type="text" id="sh_state" formcontrolname="shipping_state" autocomplete="shipping address-level1" class="col-md-12 form-control ng-untouched ng-pristine ng-invalid"></div>
                                                                                </div>
                                                                                <div class="col-md-6">
                                                                                    <div class="form-group"><label for="">Zipcode*</label><sup style="color: red;">*</sup><input type="text" id="sh_zipcode" formcontrolname="shipping_zipcode" autocomplete="shipping postal-code" class="col-md-12 form-control ng-untouched ng-pristine ng-invalid"></div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                                            </div>
                                                            </app-overall-delivery-address>
                                                            <div class="row mt-0">
                                                                <div class="col-md-12">
                                                                    <div></div>
                                                                </div>
                                                            </div>
                                                        </app-delivery-checkout></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </app-checkout-fullfilment>
                                @endif

                            </div>
                        </div>
                        <div class="col-xl-5 col-lg-6 col-md-6 col-sm-12 mt-0"><app-order-summery>
                                <div class="custom-alert col-md-6 mb-5"></div>
                                <div class="checkout_right_wrapper custom_border">
                                    <div class="order_review_wrapper optional-service-content">
                                        <h2 id="order_review_heading"> Your Order Summary </h2>
                                        <div class="woocommerce-checkout-review-order">
                                            <div class="order_summery">

                                                <?php foreach ($cart['cart_items'] as $item): ?>
                                                    @php $imgURL = ( !empty($settings->location) && isset($item['product']['images'][0]['image_small']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->store_id . '/' . $item['product']['id'] . '/' . $item['product']['images'][0]['image_small'] : ''; @endphp
                                                    <div class="order-list row">
                                                        <div class="order-img col-md-4">
                                                            @if( !empty($imgURL) )
                                                            <img src="{{ $imgURL }}" class="img-fluid img-avatar img-thumbnail" style="width:100px;">
                                                            @else
                                                            <img src="../assets/img/home/<USER>" class="img-fluid img-avatar img-thumbnail">
                                                            @endif
                                                        </div>
                                                        <div class="order-product_details col-md-8">
                                                            <div class="name">{{ $item['product']['name'] }}</div>
                                                            <div class="bottom_info">
                                                                <p class="qty"> Quantity: {{ $item['quantity'] }} </p>
                                                                <p class="order-product_price"> Price: ${{ $item['price'] }} </p>
                                                            </div>
                                                        </div>
                                                        <div class="order-product_price"></div>
                                                    </div>
                                            </div>
                                        <?php endforeach; ?>
                                            <table class="shop_table checkout-ordertable" width="100%">
                                                <tfoot>
                                                    <tr class="cart-subtotal">
                                                        <th>Subtotal </th>
                                                        <td>
                                                            <span id="rentmy-cart-subtotal" class="woocommerce-Price-amount amount">
                                                                <span class="woocommerce-Price-currencySymbol"></span>
                                                                ${{ number_format($cart['sub_total'], 2) }}</span>
                                                        </td>
                                                    </tr>
                                                    <tr class="cart-subtotal">
                                                        <th>Taxes &amp; Fees</th>
                                                        <td><span id="rentmy-cart-tax" class="woocommerce-Price-amount amount"><span
                                                                    class="woocommerce-Price-currencySymbol"></span><b>
                                                                    ${{ number_format($cart['additional_charge'], 2) }}</b>
                                                            </span></td>
                                                    </tr>

                                                    <tr class="cart-subtotal">
                                                        <th>Deposit Amount</th>
                                                        <td><span class="woocommerce-Price-amount amount"><span
                                                                    class="woocommerce-Price-currencySymbol"></span>
                                                                ${{ number_format($cart['deposit_amount'], 2) }}</span>
                                                        </td>
                                                    </tr>
                                                    <tr class="cart-subtotal">
                                                        <th>Delivery Charge </th>
                                                        <td><span class="woocommerce-Price-amount amount"><span
                                                                    class="woocommerce-Price-currencySymbol"></span>${{ number_format($cart['delivery_charge'], 2) }}</span>
                                                        </td>
                                                    </tr>
                                                    <tr class="cart-subtotal">
                                                        <th> Delivery Tax </th>
                                                        <td><span class="woocommerce-Price-amount amount"><span
                                                                    class="woocommerce-Price-currencySymbol"></span>
                                                                ${{ number_format($cart['delivery_tax'], 2) }}</span>
                                                        </td>
                                                    </tr>

                                                    <tr class="order-total">
                                                        <th>Total </th>
                                                        <td>
                                                            <strong>
                                                                <span id="rentmy-cart-total" class="woocommerce-Price-amount amount">
                                                                <span class="woocommerce-Price-currencySymbol"></span>
                                                                    ${{ number_format($cart['total'], 2) }}</span>
                                                            </strong>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>

                                        </div>

                                        <br>

                                        <div class="RentMyOptionalService" additionalcharges="">
                                            <div class="RentMyRow">
                                                <div class="RentMyFullwidth">
                                                    <div class="RentMyAdditionalChargeTitle"> Optional Services </div>
                                                </div>
                                                <div id="additionalCharges"></div>
                                            </div>
                                        </div>


                                        <!--
                                        <div class="checkout-checkbox">
                                            <div class="CreateCustomerCheckbox" iscustomeraccountdiv=""><label class="RentMyCheckbox"><input type="checkbox"
                                                        iscustomeraccount=""> Create an account to make ordering faster in the future <span></span></label>
                                            </div>

                                        </div>
                                        -->


                                        <div class="row order_details mt-3 mb-2">
                                            <div class="col-md-12">
                                                <div class="custom-control custom-checkbox terms-condition-checkbox mr-sm-2"><input type="checkbox" id="customControlAutosizing" autocomplete="nope" name="termsCondition" class="custom-control-input ng-untouched ng-pristine ng-valid"><label for="customControlAutosizing" class="custom-control-label mr-2">I have read and agree with the</label><a style="cursor: pointer;">terms &amp; conditions</a></div>
                                            </div>
                                        </div>
                                        <div class="form-group pt-3 mb-0"><a class="btn btn-md backtocart-btn" href="/cart/{{ $cart['token'] }}"><i class="fa fa-backward"></i> &nbsp;Back to Cart</a><button type="button" class="btn btn-md placeorder-btn float-right" onclick="pay_online()"> Place Order </button></div>
                                    </div>
                                </div>
                            </app-order-summery></div>
                    </div>
                </div>
            </form>
        </section>




    </div>

@endsection

@section('scripts')
<script>

function shipping_rate() {

    let shippingFirstName = $('#shippingFirstName').val();
    let shippingLastName = $('#shippingLastName').val();
    let shippingEmail = $('#shippingEmail').val();
    let shippingAddress1 = $('#address_line1').val();
    let shippingAddress2 = $('#address_line2').val();
    let shippingCity = $('#sh_city').val();
    let shippingState = $('#sh_state').val();
    let shippingCountry = $('#country_online').val();
    let shippingZipcode = $('#sh_zipcode').val();

    if( shippingFirstName == '' ) {
        alert('Please enter first name');
        $('#shippingFirstName').focus();
        return;
    }

    if( shippingLastName == '' ) {
        alert('Please enter last name');
        $('#shippingLastName').focus();
        return;
    }

    if( shippingEmail == '' ) {
        alert('Please enter email');
        $('#shippingEmail').focus();
        return;
    }

    if( shippingAddress1 == '' ) {
        alert('Please enter address');
        $('#address_line1').focus();
        return;
    }

    if( shippingCity == '' ) {
        alert('Please enter city');
        $('#sh_city').focus();
        return;
    }

    if( shippingState == '' ) {
        alert('Please enter state');
        $('#sh_state').focus();
        return;
    }

    if( shippingCountry == '' ) {
        alert('Please enter country');
        $('#country_online').focus();
        return;
    }

    if( shippingZipcode == '' ) {
        alert('Please enter zipcode');
        $('#sh_zipcode').focus();
        return;
    }

    $.ajax({
        url: '/rentmy/shipping-rate',
        type: 'POST',
        data: {
            "_token": "{{ csrf_token() }}",
            "address": {
                "shipping_first_name": shippingFirstName,
                "shipping_last_name": shippingLastName,
                "shipping_mobile": "",
                "shipping_email": shippingEmail,
                "shipping_address1": shippingAddress1,
                "combinedDeliveryAddress": "",
                "shipping_address2": shippingAddress2,
                "shipping_city": shippingCity,
                "shipping_state": shippingState,
                "shipping_country": shippingCountry,
                "shipping_zipcode": shippingZipcode
            },
            "pickup": 474,
            "token": 1748041546893
        },
        success: function(response) {
            // console.log('response', response);
            if (response.status == 'OK') {
                for(const key of Object.keys(response.result)) {
                    if (key == 'standard') {
                        let html = `
                        <div class="service-part shipping-radio-btn">
                            <div class="form-group">
                                <div class="m-radio-inline">
                                    <label class="m-radio">
                                        <input onclick="cart_delivery()" formcontrolname="header" type="radio" value="${response.result[key].rate_id}" name="main" class="ng-valid ng-dirty ng-touched">
                                        <span></span>
                                        <div class="flex2" style="flex: 2;">
                                            <h6>${response.result[key].carrier_code}</h6>
                                        </div>
                                        <div class="text-right flex1" style="flex: 1; padding-top: 10px;">
                                            <h6>$${response.result[key].charge.toFixed(2)}</h6>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>`;
                        $('.shipping-list-wrapper').append(html);
                    } else if(typeof response.result[key] === 'object' ) {
                        for(const item of response.result[key]) {
                            item.method = 4;
                            let html = `
                            <div class="service-part shipping-radio-btn">
                                <div class="form-group">
                                    <div class="m-radio-inline">
                                        <label class="m-radio">
                                            <input onclick="cart_delivery()" formcontrolname="header" type="radio" value='${JSON.stringify(item)}' name="delivery" class="">
                                            <span></span>
                                            <div class="flex2" style="flex: 2;">
                                                <h6>${item.service_name}</h6>
                                            </div>
                                            <div class="text-right flex1" style="flex: 1; padding-top: 10px;">
                                                <h6>$${item.charge}</h6>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>`;
                            $('.shipping-list-wrapper').append(html);
                        }
                    }
                }
            } else {
                // console.error('Failed to fetch shipping rate:', response.result.error);
            }
        },
        error: function(xhr, status, error) {
            // console.error('Error fetching shipping rate:', error);
        }
    });
}



function cart_delivery() {
    let shipping_method = $('input[name="delivery"]:checked').val();
    if( !shipping_method ) {
        alert('Please select shipping method');
        return;
    }

    shipping_method = JSON.parse(shipping_method);

    let shipping_cost = shipping_method.charge;
    let shipping_method_id = shipping_method.method;

    let shipping_country = $('#country_online').val();
    let shipping_city = $('#sh_city').val();
    let shipping_state = $('#sh_state').val();
    let shipping_zipcode = $('#sh_zipcode').val();

    let billing_country = $('#country_online').val();
    let billing_city = '';
    let billing_state = '';
    let billing_zipcode = '';

    $.ajax({
        url: '/rentmy/cart-delivery',
        type: 'POST',
        data: {
            "_token": "{{ csrf_token() }}",
            "shipping_method": shipping_method_id,
            "token": "{{ $cart['token'] }}",
            "shipping_cost": shipping_cost,
            "ship_date": "{{ $cart['rent_start'] }}",
            "address": {
                "shipping_country": shipping_country,
                "shipping_city": shipping_city,
                "shipping_state": shipping_state,
                "shipping_zipcode": shipping_zipcode,
                "billing_country": billing_country,
                "billing_city": billing_city,
                "billing_state": billing_state,
                "billing_zipcode": billing_zipcode
            }
        },
        success: function(response) {
            // console.log('response', response);
            if (response.status == 'OK') {
                                
            } else {
                // console.error('Failed to update cart:', response.result.error);
            }
        },
        error: function(xhr, status, error) {
            // console.error('Error updating cart:', error);
        }
    });
}


function pay_online() {

    // shipping_method = JSON.parse(shipping_method);

    // let shipping_cost = shipping_method.charge;
    // let shipping_method_id = shipping_method.method;

    let termsChecked = $('#customControlAutosizing').is(':checked');
    if (!termsChecked) {
        alert('Please accept terms & conditions');
        $('#customControlAutosizing').focus();
        return;
    }

    let first_name = $('#billing_first_name').val();
    if ( first_name == '' ) {
        alert('Please enter first name');
        $('#billing_first_name').focus();
        return;
    }

    let last_name = $('#billing_last_name').val(); 
    if ( last_name == '' ) {
        alert('Please enter last name');
        $('#billing_last_name').focus();
        return;
    }

    let email = $('#billing_email').val();
    if ( email == '' ) {
        alert('Please enter email');
        $('#billing_email').focus();
        return;
    }

    let address_line1 = $('#billing_address_line1').val();
    if ( address_line1 == '' ) {
        alert('Please enter address');
        $('#billing_address_line1').focus();
        return;
    }

    let country = $('#billing_country').val();
    if( country == '' ) {
        alert('Please enter country');
        $('#billing_country').focus();
        return;
    }

    let city = $('#billing_city').val();
    if ( city == '' ) {
        alert('Please enter city');
        $('#billing_city').focus();
        return;
    }
    let state = $('#billing_state').val();
    if ( state == '' ) {
        alert('Please enter state');
        $('#billing_state').focus();
        return;
    }
    let zipcode = $('#billing_zipcode').val();
    if ( zipcode == '' ) {
        alert('Please enter zipcode');
        $('#billing_zipcode').focus();
        return;
    }


    let address_line2 = $('#billing_address_line2').val();
    let mobile = $('#billing_mobile').val();



    let custom_fields = [];
    let custom_values = [];
    $('.checkout-custom-fields input, .checkout-custom-fields select option:selected').each(function() {
        let id = $(this).data('id');
        let type = $(this).data('type');
        let name = $(this).data('name');
        let label = $(this).data('label');
        let value = $(this).val();
        custom_fields.push({
            [name]: value
        });
        custom_values.push({
            id: id,
            field_name: name,
            field_label: label,
            type: type,
            field_values: value
        });
    });
    

    $.ajax({
        url: 'https://clientapi.rentmy.co/api/orders/online',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            "type": 2,
            "order_source": "Online", 
            "token": "{{ $cart['token'] }}",
            "address_id": null,
            "special_requests": null,
            "special_instructions": null,
            "driving_license": null,
            "pickup": {{ $settings->location }},
            "is_customer_account": false,
            "delivery": {},
            "shipping_method": 1,
            "currency": "USD",
            "location_id": {{ $settings->location }},
            "payment_amount": 0,
            "first_name": first_name,
            "last_name": last_name,
            "mobile": mobile,
            "email": email,
            "company": "",
            "country": country,
            "address_line1": address_line1,
            "address_line2": address_line2,
            "city": city,
            "state": state,
            "zipcode": zipcode,
            "shipping_first_name": first_name,
            "shipping_last_name": last_name,
            "shipping_mobile": mobile,
            "shipping_email": email,
            "shipping_company": "",
            "shipping_country": country,
            "shipping_address1": address_line1,
            "shipping_address2": address_line2,
            "shipping_city": city,
            "shipping_state": state,
            "shipping_zipcode": zipcode,
            "combinedDeliveryAddress": null,
            "additional_charges": [],
            "custom_checkout": {
                "fields": custom_fields
            },
            "custom_values": custom_values
        }),
        headers: {
            'Authorization': 'Bearer {{ $settings->access_token }}',
            'Content-Type': 'application/json'
        },
        success: function(response) {

            // console.log('response', response);

                let data = response?.result?.data ?? {};
                let order_id = data?.payment?.order_id ?? 0;

                if( order_id == 0 ) {
                    clearCart();
                    alert('Invalid Cart. Please add items to cart.');
                    window.location.href = "{{ route('rentmy.index') }}";
                    return;
                }

                const customer_id = 0;
                const location_id = {{ $settings->location }};
                const store_slug = "{{ $settings->store_name }}";
                const payment_source = "online_checkout";

                try {
                    encodedData = btoa(unescape(encodeURIComponent(`o=${order_id}&s=${store_slug}&l=${location_id}&c=${customer_id || ""}&p=${payment_source}`)));
                } catch (error) {
                    encodedData = `o=${order_id}&s=${store_slug}&l=${location_id}&c=${customer_id || ""}&p=${payment_source}`
                }
                const PAYMENT_DOMAIN = "https://payment.rentmy.co/";
                const successUrl =  "{{ request()->getScheme() . '://' . request()->getHost() . '/' }}thank-you?uid=6b740b4e48a511f0b6c9028fcd6e9595";
                const cancelUrl = "{{ request()->getScheme() . '://' . request()->getHost() . '/' }}checkout?paymentStatus=failed";
                const paymentURL = `${PAYMENT_DOMAIN}payments/${encodedData}/?success=${encodeURI(successUrl)}&cancel=${encodeURI(cancelUrl)}`;
                clearCart();
                setTimeout(function() {
                    window.open(paymentURL, "_self");
                }, 2000);
        },
        error: function(xhr, status, error) {
            // console.error('Error updating cart:', error);
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    checkCartSesion();
    addServiceCharge();
    ordersAdditionalCharges();
});

function clearCart() {
    $.ajax({
        url: '/rentmy/clear-cart',
        type: 'POST',
        data: {
            "_token": "{{ csrf_token() }}"
        },
        success: function(response) {
            // console.log('response', response);
        },
        error: function(xhr, status, error) {
            // console.error('Error updating cart:', error);
        }
    });
                    
}

function checkCartSesion() {

    $.ajax({
        url: '/rentmy/check-cart-session',
        type: 'POST',
        data: {
            "_token": "{{ csrf_token() }}"
        },
        success: function(response) {
            // console.log('response', response);
            if( !response.success ) {
                alert('Invalid Cart. Please add items to cart.');
                window.location.href = "{{ route('rentmy.index') }}";
            }
        },
        error: function(xhr, status, error) {
            // console.error('Error updating cart:', error);
        }
    });

}

function ordersAdditionalCharges() {

    $.ajax({
        url: '{{ $settings->api_base_url }}settings/orders/additional-charges?type=active&cart_token={{ $cart['token'] }}',
        type: 'GET',
        headers: {
            'Authorization': 'Bearer {{ $settings->access_token }}',
            "location": "{{ $settings->location_id }}",
        },
        success: function(response) {

            // console.log('response2', response);

            if( response.status == 'OK' ) {
                let data = response.result.data;
                let html = '';
                for(const item of data) {
                    html += `
                    <div class="RentMyFullwidth" singlecharge="">
                        <div class="RentMyRow">
                            <div class="RentMyCheckboxInline">
                                <label class="RentMyCheckbox" title="">
                                    <input data-existing-id="${item.existing?.id ?? ''}" onchange="additionalChargeCheck(this)" type="checkbox" data-amount="${item.fee.amounts[0]}" value="${item.id}" ${item.is_selected ? 'checked' : ''}> ${item.description}<span> ${(item.fee.type == 'percentage') ? '' : '$'}${item.fee.amounts[0]} ${(item.fee.type == 'percentage') ? '%' : ''}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    `;
                }
                $('#additionalCharges').html(html);
            }
        },
        error: function(xhr, status, error) {
            // console.error('Error2:', error);
        }
    });


}


function additionalChargeCheck(el) {
    // console.log('el', el);

    $.ajax({
        url: "{{ $settings->api_base_url }}orders/additional-charges/create",
        type: "POST",
        headers: {
            "Authorization": "Bearer {{ $settings->access_token }}",
            "Content-Type": "application/json",
            "Location": "{{ $settings->location }}"
        },
        data: JSON.stringify({
            "order": null,
            "cart_token": "{{ $cart['token'] }}",
            "additional_charges": [{
                "id": el.value,
                "is_selected": el.checked,
                "value": $(el).data('amount'),
                "selected_option": "",
                "order_additional_charge_id": $(el).data('existing-id')
            }]
        }),
        success: function(response) {
            console.log('Additional charge response:', response);
            addServiceCharge();
            setTimeout(() => {
                ordersAdditionalCharges();
            }, 2000);
            
        },
        error: function(xhr, status, error) {
            // console.error('Error adding additional charge:', error);
        }
    });

}


function addServiceCharge() {
    $.ajax({
        url: "{{ $settings->api_base_url }}carts/{{ $cart['token'] }}",
        type: "GET",
        data: {
            action: "add_service_charge",
            billing_country: "US"
        },
        headers: {
            "authorization": "Bearer {{ $settings->access_token }}",
            "location": "{{ $settings->location }}"
        },
        success: function(response) {
            // console.log('Service charge added:', response);
            $('#rentmy-cart-subtotal').text('$' + response.result.data.sub_total.toFixed(2));
            $('#rentmy-cart-tax').text('$' + response.result.data.additional_charge.toFixed(2));
            $('#rentmy-cart-service-charge').text('$' + response.result.data.additional_charge);
            $('#rentmy-cart-deposit-amount').text('$' + response.result.data.deposit_amount);
            $('#rentmy-cart-shipping-charge').text('$' + response.result.data.delivery_charge);
            $('#rentmy-cart-shipping-tax').text('$' + response.result.data.delivery_tax);
            $('#rentmy-cart-total').text('$' + response.result.data.total);

        },
        error: function(xhr, status, error) {
            // console.error('Error adding service charge:', error);
        }
    });
}

</script>

@endsection