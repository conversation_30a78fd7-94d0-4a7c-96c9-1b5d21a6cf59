@extends('rentmy.layout')

@section('title', $product['seo']['meta_title'] ?? $product['name'])

@section('metas')
    <meta name="description" content="{{ trim($product['seo']['meta_description']) ?? '' }}" /> {{-- Meta description from product SEO data --}}

    <meta itemprop="name" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Item name for rich snippets --}}
    <meta itemprop="description" content="{{ trim($product['seo']['meta_description']) ?? '' }}" /> {{-- Item description for rich snippets --}}
    {{--
        PHP block to dynamically generate the product image URL for meta tags.
        Checks if location and large image exist and constructs the URL using S3 bucket and location.
    --}}
    @php $imgURL = ( !empty($settings->store_id) && isset($product['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->store_id . '/' . $product['id'] . '/' . $product['images'][0]['image_large'] : ''; @endphp
    <meta itemprop="image" content="{{ $imgURL }}" /> {{-- Item image URL for rich snippets --}}

    <meta property="og:url" content="{{ Request::url() }}" /> {{-- Canonical URL of the page --}}
    <meta property="og:type" content="website" /> {{-- Type of content --}}
    <meta property="og:title" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Title for social media sharing --}}
    <meta property="og:description" content="{{ trim($product['seo']['meta_description']) ?? '' }}" /> {{-- Description for social media sharing --}}
    <meta property="og:image" content="{{ $imgURL }}" /> {{-- Image URL for social media sharing --}}

    <meta name="twitter:card" content="summary_large_image" /> {{-- Specifies the type of Twitter card --}}
    <meta name="twitter:title" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Title for Twitter sharing --}}
    <meta name="twitter:description" content="{{ trim($product['seo']['meta_description']) ?? '' }}" /> {{-- Description for Twitter sharing --}}
    <meta name="twitter:image" content="{{ $imgURL }}" /> {{-- Image URL for Twitter sharing --}}

	<script id="structured-data" type="application/ld+json">
	{
		"@context": "https://schema.org/",
		"@type": "Product",
		"name": "{{ $product['seo']['meta_title'] ?? $product['name'] }}",
		"image": "{{ $imgURL }}",
		"url": "{{ Request::url() }}",
		"description": "{{ trim($product['seo']['meta_description']) ?? $product['description'] }}",
		"sku": "{{ $product['id'] }}",
		"brand": {
			"@type": "Brand",
			"name": "{{ $settings->store_slug }}"
		},
		"review": {
			"@type": "Review",
			"reviewRating": {
				"@type": "Rating",
				"ratingValue": "4.5",
				"bestRating": "5"
			},
			"author": {
				"@type": "Person",
				"name": "{{ $settings->store_slug }}"
			},
			"reviewBody": "",
			"datePublished": ""
		},
		"aggregateRating": {
			"@type": "AggregateRating",
			"ratingValue": "4.5",
			"reviewCount": "123"
		},
        "offers": {
            "@type": "Offer",
            "price": 0,
            "priceCurrency": "USD",
            "shippingDetails": {
                "@type": "OfferShippingDetails",
                "shippingRate": {
                    "@type": "MonetaryAmount",
                    "value": "",
                    "currency": ""
                },
                "shippingDestination": {
                    "@type": "DefinedRegion",
                    "addressCountry": ""
                },
                "deliveryTime": {
                    "@type": "ShippingDeliveryTime",
                    "handlingTime": {
                    "@type": "QuantitativeValue",
                    "minValue": "",
                    "maxValue": "",
                    "unitCode": "DAY"
                    },
                    "transitTime": {
                    "@type": "QuantitativeValue",
                    "minValue": "",
                    "maxValue": "",
                    "unitCode": "DAY"
                    }
                }
            }
        }            
	}
    </script>


@endsection

@section('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    {{--
        Inline CSS styles for specific elements.
        Defines styles for image containers, aspect ratios, and theme buttons.
    --}}
    <style>
        .image-container {
            width: 100%;
            padding-bottom: 56.25%;
            /* 16:9 aspect ratio */
            position: relative;
            overflow: hidden;
        }

        .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain; /* Ensures the entire image is visible without distortion */
        }

        a.btn,
        .theme-btn {
            background-color: rgb(97, 175, 175); /* Custom background color for buttons */
            color: #ffffff; /* White text color for buttons */
        }

        label.rentmy-radio {
            background: rgb(255, 255, 255);
            display: inline-block;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: center;
            justify-content: center;
            padding: 4px 8px;
            padding-left: 8px !important;
            border: 1px solid rgb(0, 0, 0);
            position: relative;
            margin-bottom: 12px;
            cursor: pointer;
        }
        label.rentmy-radio.active {
            background: rgb(0, 0, 0); /* Background color when radio button is active */
            color: #fff !important; /* Text color when radio button is active */
        }
        label.rentmy-radio input[type="radio"] {
            position: fixed; /* Hides the default radio button */
            opacity: 0;
            pointer-events: none;
        }

        div.rentmy-radio-inline.price-options label.rentmy-radio {
            font-size: 20px;
            font-weight: 700;
        }

        div.rentmy-modal-product-variants,
        div.rentmy-modal-starting-from {
            margin-bottom: 16px;
        }
        div.rentmy-modal-product-variants h5,
        div.rentmy-pricing-options h5,
        div.rentmy-modal-starting-from h5 {
            margin-bottom: 8px;
        }
        div.rentmy-modal-product-variants ul li,
        div.rentmy-modal-starting-from ul li {
            display: inline-block; /* Displays list items horizontally */
        }

        .rentmy-product-price .title-price {
            font-size: 48px; /* Large font size for the main price display */
        }
        .rentmy-product-price .title-duration {
            font-size: 26px; /* Font size for the duration text */
        }
        div.rentmy-pricing-options ul li {
            width: 32%; /* Sets width for rental option buttons */
            display:  inline-block;
        }
        div.rentmy-pricing-options label.rentmy-radio {
            width: 100%; /* Makes the radio button label fill the list item width */
        }
        p.available-error,
        p.not-available {
            color: red; /* Red color for availability messages */
            font-weight: 700 !important;
        }

        .rentmy-button.rentmy-modal-cartbtn.disabled,
        .rentmy-button.rentmy-modal-cartbtn.disabled:hover,
        .rentmy-button.rentmy-modal-cartbtn.disabled:before {
            background-color: #d2d1d1!important; /* Grey background for disabled button */
            border-color: #d2d1d1!important;
        }

        .flatpickr-apply-button {
            margin-left: 10px;
        }

        .rentmy-minus,
        .rentmy-plus {
            cursor: pointer;
        }

        ul {
            margin: 0;
            padding: 0;
        }

        div.rentmy-pricing-options ul li {
            width: 100%;
        }

        .btn {
            display: inline-block;
            font-weight: 400;
            color: #212529;
            text-align: center;
            vertical-align: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: transparent;
            border: 1px solid transparent;
            padding: .375rem .75rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: .25rem;
            transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        }

        .btn-dark {
            color: #fff;
            background-color: #343a40;
            border-color: #343a40;
        }

        .rentmy-in-num {
            width: 50px;
            text-align: center;
            padding: 4px;
        }


    </style>

@endsection

@section('content')

    {{-- Main container for product details --}}
    <div class="container mt-4">

    @if(session('error'))
        <div class="alert alert-danger" role="alert">
            {{ session('error') }}
        </div>
    @endif

    <?php       
    $haveRentalOptions = ( isset($product['prices'][0]['daily']) || isset($product['prices'][0]['monthly']) || isset($product['prices'][0]['weekly']) ) ? true : false;
    $haveBuy = ( isset($product['buy_price']) && $product['buy_price'] > 0 ) ? true : false;
    
    $showStartDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_start_date']) && $contents['result']['data'][0]['contents']['confg']['show_start_date'] == 1 ) ? true : false;
    $showStartTime = ( isset($contents['result']['data'][0]['contents']['confg']['show_start_time']) && $contents['result']['data'][0]['contents']['confg']['show_start_time'] == 1 ) ? true : false;
    $showEndDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_end_date']) && $contents['result']['data'][0]['contents']['confg']['show_end_date'] == 1 ) ? true : false;
    $showEndTime = ( isset($contents['result']['data'][0]['contents']['confg']['show_end_time']) && $contents['result']['data'][0]['contents']['confg']['show_end_time'] == 1 ) ? true : false;
    $rentalPriceOption = ( isset($contents['result']['data'][0]['contents']['confg']['rental_price_option']) && $contents['result']['data'][0]['contents']['confg']['rental_price_option'] == 1 ) ? true : false;
    $showEarliestStartDate = ( isset($contents['result']['data'][0]['contents']['confg']['show_earliest_start_date']) && $contents['result']['data'][0]['contents']['confg']['show_earliest_start_date'] == 1 ) ? true : false;
    $cartToken = isset($cart['cart_items'][0]) ? $cart['token'] : null; 
    ?>

    <h3>Details</h3>    

        <div class="row">
            {{-- Product Image Section --}}
            <div class="col-md-6">
                {{-- Check if an image URL exists --}}
                @if( !empty($imgURL) )
                {{-- Display the product image within an aspect ratio container --}}
                <div class="image-container">
                    <img src="{{ $imgURL }}" class="img-fluid">
                </div>
                @else
                {{-- Display a placeholder if no image is available --}}
                <div class="bg-secondary" style="height: 300px; display: flex; justify-content: center; align-items: center; color: white;">No Images</div>
                @endif
            </div>
            {{-- Product Details and Add to Cart Form Section --}}
            <div class="col-md-6">

                {{-- Container for product payment and form details --}}
                <div class="col-lg-12 col-md-12 col-sm-12 col-12 product-payment-details">

                    {{-- Form to add the product to the cart --}}
                    <form action="{{ route('rentmy.addToCart') }}" method="POST">

                        @csrf {{-- CSRF token for form submission security --}}
                        {{-- Hidden inputs for various product and rental/buy details --}}
                        <input type="hidden" name="deposit_amount" value="0">
                        <input type="hidden" name="deposite_tax" value="">
                        <input type="hidden" name="driving_license_required" value="false">
                        <input type="hidden" name="product_id" value="{{ $product['id'] }}">
                        <input type="hidden" name="variants_products_id" value="{{ $product['default_variant']['variants_products_id'] }}">
                        <input type="hidden" name="location" value="{{ $settings->location }}">
                        <input type="hidden" name="fullfilment_option" value="">
                        <input type="hidden" name="rental_duration" value="0">
                        <input type="hidden" name="rental_type" value="rent">
                        <input type="hidden" name="sales_tax" value="">
                        <input type="hidden" name="term" value="">
                        <input type="hidden" name="token" value="{{ $cartToken }}">
                        <input type="hidden" name="rent_start" value="<?php echo date('Y-m-d 12:00'); ?>">
                        <input id="rent_end_input" type="hidden" name="rent_end" value="<?php echo date('Y-m-d 12:00', strtotime('+1 day')); ?>">
                        {{--
                            PHP logic to set the initial 'price_id' and 'price' hidden inputs in the form.
                            This determines the default price and price identifier passed when the form is submitted
                            (before any user interaction changes the selected price/rental option).
                            It checks for different pricing structures in the product data.
                        --}}
                        <?php $havePrice = false; ?>
                        <?php if( $product['rent_price'] <= 0 && $product['buy_price'] <= 0 && isset($product['prices'][0]['base']['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If no rental or buy price, but a base price exists, use the base price --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['base']['id'] }}"> {{-- Set price_id to the base price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['base']['price'] }}"> {{-- Set price to the base price value --}}

                        <?php elseif( isset($product['prices'][0]['hourly'][0]['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If hourly rental price exists (and is the first option), use it as the default --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['hourly'][0]['id'] }}"> {{-- Set price_id to the first hourly price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['hourly'][0]['price'] }}"> {{-- Set price to the first hourly price value --}}

                        <?php elseif( isset($product['prices'][0]['daily'][0]['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If daily rental price exists (and is the first option), use it as the default --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['daily'][0]['id'] }}"> {{-- Set price_id to the first daily price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['daily'][0]['price'] }}"> {{-- Set price to the first daily price value --}}

                        <?php elseif( isset($product['prices'][0]['weekly'][0]['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If weekly rental price exists (and is the first option), use it as the default --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['weekly'][0]['id'] }}"> {{-- Set price_id to the first weekly price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['weekly'][0]['price'] }}"> {{-- Set price to the first weekly price value --}}

                        <?php elseif( isset($product['prices'][0]['monthly'][0]['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If monthly rental price exists (and is the first option), use it as the default --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['monthly'][0]['id'] }}"> {{-- Set price_id to the first monthly price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['monthly'][0]['price'] }}"> {{-- Set price to the first monthly price value --}}

                        <?php elseif( isset($product['prices'][0]['base']['price']) ): ?>
                            <?php $havePrice = true; ?>
                            {{-- If monthly rental price exists (and is the first option), use it as the default --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['base']['id'] }}"> {{-- Set price_id to the first monthly price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['base']['price'] }}"> {{-- Set price to the first monthly price value --}}
                        <?php endif ?>
                        <input type="hidden" name="custom_fields" value="[]">
                        <input type="hidden" name="is_apply" value="false">
                        <input type="hidden" name="zone" value="-180">
                        <input type="hidden" name="slug" value="{{ $product['slug'] }}">
                        {{--
                            PHP block to determine if rental options or buy option are available.
                            Used to control the display of Buy/Rent radio buttons.
                        --}}


                    <div class="rentmy-product-rightside">
                        {{-- Product Title --}}
                        <h1 class="rentmy-product-title">
                            <?= $product['name'] ?> {{-- Displays the product name --}}
                        </h1>

                        {{--
                            PHP logic to display the product price.
                            Checks for base price, daily/monthly rental starting prices, or buy price.
                        --}}
                        <?php if( $product['rent_price'] <= 0 && $product['buy_price'] <= 0 && isset($product['prices'][0]['base']['price']) ): ?>
                            <h2 class="rentmy-product-price">
                                <span class="title-price">$<?= number_format($product['prices'][0]['base']['price'], 2) ?></span> {{-- Displays base price --}}
                            </h2>
                        <?php elseif( isset($product['prices'][0]['hourly'][0]['price']) ): ?>
                            <p class="rentmy-product-price">
                                <span class="title-price"></span>
                                @php /* <span class="title-duration">Starting at <b>$<?= number_format($product['prices'][0]['hourly'][0]['price'], 2) ?></b> per hour</span> {{-- Displays starting hourly price --}} */ @endphp
                            </p>                            
                        <?php elseif( isset($product['prices'][0]['daily'][0]['price']) ): ?>
                            <p class="rentmy-product-price">
                                <span class="title-price"></span>
                                @php /* <span class="title-duration">Starting at <b>$<?= number_format($product['prices'][0]['daily'][0]['price'], 2) ?></b> per day</span> {{-- Displays starting daily price --}} */ @endphp
                            </p>
                        <?php elseif( isset($product['prices'][0]['weekly'][0]['price']) ): ?>
                            <p class="rentmy-product-price">
                                <span class="title-price"></span>
                                @php /* <span class="title-duration">Starting at <b>$<?= number_format($product['prices'][0]['weekly'][0]['price'], 2) ?></b> per week</span> {{-- Displays starting weekly price --}} */ @endphp
                            </p>                            
                        <?php elseif( isset($product['prices'][0]['monthly'][0]['price']) ): ?>
                            <p class="rentmy-product-price">
                                <span class="title-price"></span>
                                @php /* <span class="title-duration">Starting at <b>$<?= number_format($product['prices'][0]['monthly'][0]['price'], 2) ?></b> per month</span> {{-- Displays starting monthly price --}} */ @endphp
                            </p>
                        <?php endif ?>

                        {{-- Section for selecting Buy or Rent option --}}
                        <div class="rentmy-modal-rentbuy">

                            <div class="rentmy-radio-inline price-options">

                                {{-- Display Rent radio button if rental options are available --}}
                                <?php if( $haveRentalOptions && $haveBuy ): ?>
                                    <input type="hidden" id="rentmy-rent" name="rentmy-package" value="rent" />
                                <?php else: ?>
                                    {{-- Hidden input if only Rent is available --}}
                                    <input type="hidden" id="rentmy-rent-hidden" name="rentmy-package" value="rent" />
                                <?php endif ?>

                            </div>

                            {{-- Section for rental start date options, only shown if rental options exist --}}
                            <?php if( $haveRentalOptions ): ?>

                            <div class="rentmy-modal-starting-from">


                                    <?php if( $showStartDate ): ?>
                                    <h5>Starting from</h5>
                                    <ul>
                                        <?php if($showEarliestStartDate): ?>
                                            {{-- Option to start today --}}
                                            <li>
                                            <label class="rentmy-radio today active" for="rentmy-starting-from-today">
                                                <input type="radio" id="rentmy-starting-from-today" name="rentmy-starting"  value="<?= date('Y-m-d 12:00') ?>" checked /> Today
                                            </label>
                                            </li>
                                            {{-- Option to start tomorrow --}}
                                            <li>
                                            <label class="rentmy-radio tomorrow" for="rentmy-starting-from-tomorrow">
                                                <input type="radio" id="rentmy-starting-from-tomorrow" name="rentmy-starting"  value="<?= date('Y-m-d 12:00', strtotime('+1 day')) ?>" /> Tomorrow
                                            </label>
                                            </li>
                                        <?php endif; ?>
                                        </li>
                                        {{-- Option to pick a specific start date (calendar input) --}}
                                        <li>
                                        <label id="rentmy-pick-start-date-label" class="rentmy-radio pick" for="rentmy-pick-start-date">
                                            <input type="radio" id="rentmy-pick-start-date" name="rentmy-starting"  value="<?= date('Y-m-d 12:00') ?>" />
                                            <?= $showEndDate ? "Pick Range Date" : "Pick Your Delivery Date" ?>
                                        </label>
                                        </li>
                                </ul>

                                <p class="rent_start_to_rent_end">{{ isset($cart['cart_items'][0]) ? $cart['rent_start'] : 'Today' }}</p>
                                {{-- Hidden input for the calendar date picker (likely used by Mobiscroll) --}}
                                <input id="calendar" type="hidden" />


                                {{-- Hidden input to store the selected start date --}}
                                <input type="hidden" class="date_start_detail" name="rent_start" value="<?= date('Y-m-d') ?>" />


                                    {{-- Section for rental end date options --}}
                                    <ul>

                                        {{-- Option to pick a specific end date (calendar input) --}}
                                        <li>
                                        {{--
                                        <label class="rentmy-radio pick2" for="rentmy-pick-end-date">
                                        --}}                                         
                                            <input type="hidden" id="rentmy-pick-end-date" name="rentmy-starting"  value="<?= date('Y-m-d 12:00') ?>" />
                                        {{--
                                        </label>
                                        --}}
                                        {{-- Hidden input for the calendar date picker (likely used by Mobiscroll) --}}
                                        <input id="calendar2" type="hidden" />
                                        </li>

                                </ul>
                                <?php endif ?>


                                <?php if( $showEndDate ): ?>
                                    {{-- Hidden input to store the selected end date --}}
                                    <input type="text" class="date_end_detail" name="rent_end" value="<?= date('Y-m-d', strtotime('+1 day')) ?>" />
                                <?php endif ?>


                            </div>

                                <?php if($rentalPriceOption): ?>
                                {{-- Section for displaying rental price options (daily/monthly) --}}
                                <div class="rentmy-pricing-options">
                                    <h5>Rental Options</h5>
                                    <ul>
                                        {{-- Loop through product prices --}}
                                        <?php foreach($product['prices'] as $price): ?>
                                            {{-- Display daily rental options if available --}}
                                            <?php if( isset($price['daily']) && !empty($price['daily']) ): ?>
                                                <?php foreach($price['daily'] as $i => $priceD): ?>
                                                    <?php
                                                        if( isset($price['daily'][$i - 1])
                                                            && $price['daily'][$i - 1]['price'] == $priceD['price']
                                                            && $price['daily'][$i - 1]['duration'] == $priceD['duration']
                                                            && $price['daily'][$i - 1]['label'] == $priceD['label']
                                                        ) continue;
                                                    ?>
                                                    <?php
                                                        // Calculate tomorrow's start and end times for the rental option
                                                        $tomorrowStart = strtotime('+1 day', strtotime($priceD['rent_start']));
                                                        $tomorrowEnd = strtotime('+1 day', strtotime($priceD['rent_end']));
                                                    ?>
                                                    <li>
                                                    {{-- Radio button for a daily rental option --}}
                                                    <label class="rentmy-radio <?= ($product['rent_price'] == $priceD['price']) ? 'active' : ''; ?>" for="rentmy-priceD<?= $i ?>"
                                                            data-title-price="$<?= number_format($priceD['price'], 2) ?>" {{-- Data attribute for price display --}}
                                                            data-title-duration="for <?= $priceD['duration'] ?> <?= $priceD['label'] ?>" {{-- Data attribute for duration display --}}
                                                            data-rent_start="<?= $priceD['rent_start'] ?>" {{-- Data attribute for rental start time --}}
                                                            data-rent_end="<?= $priceD['rent_end'] ?>" {{-- Data attribute for rental end time --}}
                                                            data-tomorrow_start="<?= date( 'Y-m-d H:i', $tomorrowStart) ?>" {{-- Data attribute for tomorrow's start time --}}
                                                            data-tomorrow_end="<?= date( 'Y-m-d H:i', $tomorrowEnd) ?>"> {{-- Data attribute for tomorrow's end time --}}
                                                        <input type="radio" id="rentmy-priceD<?= $i ?>" name="rentmy-price" <?= ($product['rent_price'] == $priceD['price']) ? 'checked' : ''; ?> /> $<?= number_format($priceD['price'], 2) ?> / <?= $priceD['duration'] ?> <?= $priceD['label'] ?> {{-- Radio button label with price and duration --}}
                                                    </label>
                                                    </li>
                                                <?php endforeach ?>
                                            <?php endif ?>

                                            {{-- Display weekly rental options if available --}}
                                            <?php if( isset($price['weekly']) && !empty($price['weekly']) ): ?>
                                                <?php foreach($price['weekly'] as $i => $priceW): ?>
                                                    <?php
                                                        if( isset($price['weekly'][$i - 1])
                                                            && $price['weekly'][$i - 1]['price'] == $priceW['price']
                                                            && $price['weekly'][$i - 1]['duration'] == $priceW['duration']
                                                            && $price['weekly'][$i - 1]['label'] == $priceW['label']
                                                        ) continue;
                                                    ?>                                                
                                                    <?php
                                                        // Calculate tomorrow's start and end times for the rental option
                                                        $tomorrowStart = strtotime('+1 day', strtotime($priceW['rent_start']));
                                                        $tomorrowEnd = strtotime('+1 day', strtotime($priceW['rent_end']));
                                                    ?>
                                                    <li>
                                                    {{-- Radio button for a weekly rental option --}}
                                                    <label class="rentmy-radio <?= ($product['rent_price'] == $priceW['price']) ? 'active' : ''; ?>" for="rentmy-priceW<?= $i ?>"
                                                            data-title-price="$<?= number_format($priceW['price'], 2) ?>" {{-- Data attribute for price display --}}
                                                            data-title-duration="for <?= $priceW['duration'] ?> <?= $priceW['label'] ?>" {{-- Data attribute for duration display --}}
                                                            data-rent_start="<?= $priceW['rent_start'] ?>" {{-- Data attribute for rental start time --}}
                                                            data-rent_end="<?= $priceW['rent_end'] ?>" {{-- Data attribute for rental end time --}}
                                                            data-tomorrow_start="<?= date( 'Y-m-d H:i', $tomorrowStart) ?>" {{-- Data attribute for tomorrow's start time --}}
                                                            data-tomorrow_end="<?= date( 'Y-m-d H:i', $tomorrowEnd) ?>" {{-- Data attribute for tomorrow's end time --}}
                                                            data-price="<?= $priceW['price'] ?>"
                                                            data-price_id="<?= $priceW['id'] ?>">
                                                        <input type="radio" id="rentmy-priceW<?= $i ?>" name="rentmy-price" <?= ($product['rent_price'] == $priceW['price']) ? 'checked' : ''; ?> /> $<?= number_format($priceW['price'], 2) ?> / <?= $priceW['duration'] ?> <?= $priceW['label'] ?> {{-- Radio button label with price and duration --}}
                                                    </label>
                                                    </li>
                                                <?php endforeach ?>
                                            <?php endif ?>


                                            {{-- Display monthly rental options if available --}}
                                            <?php if( isset($price['monthly']) && !empty($price['monthly']) ): ?>
                                                <?php foreach($price['monthly'] as $i => $priceM): ?>
                                                    <?php
                                                        if( isset($price['monthly'][$i - 1])
                                                            && $price['monthly'][$i - 1]['price'] == $priceM['price']
                                                            && $price['monthly'][$i - 1]['duration'] == $priceM['duration']
                                                            && $price['monthly'][$i - 1]['label'] == $priceM['label']
                                                        ) continue;
                                                    ?>                                                    
                                                    <?php
                                                        // Calculate tomorrow's start and end times for the rental option
                                                        $tomorrowStart = strtotime('+1 day', strtotime($priceM['rent_start']));
                                                        $tomorrowEnd = strtotime('+1 day', strtotime($priceM['rent_end']));
                                                    ?>
                                                    <li>
                                                    {{-- Radio button for a monthly rental option --}}
                                                    <label class="rentmy-radio <?= ($product['rent_price'] == $priceM['price']) ? 'active' : ''; ?>" for="rentmy-priceM<?= $i ?>"
                                                            data-title-price="$<?= number_format($priceM['price'], 2) ?>" {{-- Data attribute for price display --}}
                                                            data-title-duration="for <?= $priceM['duration'] ?> <?= $priceM['label'] ?>" {{-- Data attribute for duration display --}}
                                                            data-rent_start="<?= $priceM['rent_start'] ?>" {{-- Data attribute for rental start time --}}
                                                            data-rent_end="<?= $priceM['rent_end'] ?>" {{-- Data attribute for rental end time --}}
                                                            data-tomorrow_start="<?= date( 'Y-m-d H:i', $tomorrowStart) ?>" {{-- Data attribute for tomorrow's start time --}}
                                                            data-tomorrow_end="<?= date( 'Y-m-d H:i', $tomorrowEnd) ?>"> {{-- Data attribute for tomorrow's end time --}}
                                                        <input type="radio" id="rentmy-priceM<?= $i ?>" name="rentmy-price" <?= ($product['rent_price'] == $priceM['price']) ? 'checked' : ''; ?> /> $<?= number_format($priceM['price'], 2) ?> / <?= $priceM['duration'] ?> <?= $priceM['label'] ?> {{-- Radio button label with price and duration --}}
                                                    </label>
                                                    </li>
                                                <?php endforeach ?>
                                            <?php endif ?>

                                        <?php endforeach ?>
                                    </ul>
                                </div>
                                <?php endif // rentalPriceOption ?>


                            <?php endif ?>

                        </div>

                        {{-- Section for selecting product variants --}}
                        <div class="rentmy-modal-product-variants">

                            {{-- Loop through variant sets (e.g., Size, Color) --}}
                            <?php foreach($product['variant_set_list'] as $list): ?>
                                <h5><?= $list['name'] ?></h5> {{-- Display the variant set name --}}
                                <ul>
                                    {{-- Loop through individual variants --}}
                                    <?php foreach($product['variant_list'] as $i => $variant): ?>
                                    {{-- Skip variants that don't belong to the current set --}}
                                    <?php if( $variant['variant_set_id'] != $list['id'] ) continue; ?>
                                    {{-- Special handling for 'Price' variants (assuming variant name is the price) --}}
                                    <?php if( $list['name'] == 'Price' ): ?>
                                    <li>
                                    {{-- Radio button for a 'Price' variant --}}
                                    <label class="rentmy-radio <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'active' : ''; ?>" for="rentmy-variant<?= $i ?>">
                                        <input type="radio" id="rentmy-variant<?= $i ?>" name="rentmy-<?= $list['id'] ?>"  value="<?= $variant['id'] ?>" <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'checked' : ''; ?> data-price="<?= str_replace('$', '', $variant['name']) ?>" /> <?= $variant['name'] ?> {{-- Radio button label with variant name/price --}}
                                    </label>
                                    </li>
                                    <?php else: ?>
                                    <li>
                                    {{-- Radio button for a standard variant --}}
                                    <label class="rentmy-radio <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'active' : ''; ?>" for="rentmy-variant<?= $i ?>">
                                        <input type="radio" id="rentmy-variant<?= $i ?>" name="rentmy-<?= $list['id'] ?>"  value="<?= $variant['id'] ?>" <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'checked' : ''; ?> /> <?= $variant['name'] ?> {{-- Radio button label with variant name --}}
                                    </label>
                                    </li>
                                    <?php endif ?>
                                <?php endforeach ?>
                                </ul>
                            <?php endforeach ?>
                        </div>

                        {{-- Section for selecting quantity --}}
                        <div class="rentmy-modal-quantity">
                            <div class="rentmy-number-block">
                                <div class="rentmy-num-in">
                                    {{-- Quantity decrement button --}}
                                    <span class="rentmy-minus dis btn-dark btn" onclick="this.nextElementSibling.value = ( this.nextElementSibling.value > 1 ) ? parseInt(this.nextElementSibling.value) - 1 : 1">-</span>
                                    {{-- Quantity input field (readonly, updated via buttons) --}}
                                    <input name="quantity" type="text" class="rentmy-in-num" value="1" readonly="">
                                    {{-- Quantity increment button --}}
                                    <span class="rentmy-plus btn-dark btn" onclick="this.previousElementSibling.value = parseInt(this.previousElementSibling.value) + 1">+</span>
                                </div>
                                {{-- Displays the number of available items --}}
                                <p>Available : <span class="rentmy-product-available"><?= $product['available']?></span></p>
                                {{-- Display an error message if quantity exceeds availability (initially hidden) --}}
                                <?php if( $product['available'] > 0 ): ?>
                                    <p class="available-error"></p>
                                <?php endif ?>
                            </div>
                        </div>

                        {{-- Add to Cart button --}}
                        <?php if( $product['available'] > 0 && $showStartDate && $havePrice ): ?>
                            <button type="submit" class="rentmy-button rentmy-modal-cartbtn">Add to Cart</button>
                        <?php else: ?>
                            <button type="button" class="rentmy-button rentmy-modal-cartbtn" disabled>Add to Cart</button>
                        <?php endif ?>
                    </div>

                </form>

                <br/>
                <br/>

                {{-- Link back to the main products page --}}
                <a href="{{ route('rentmy.index') }}" class="btn btn-secondary">Back to Products</a>

            </div>
        </div>

        {{-- Related Products Section --}}
        <div class="row mt-5">
            <div class="col-12">
                <h2>Product Description</h2>
                <div class="rentmy-product-description">
                    {!! $product['description'] !!}
                </div>
            </div>        
            <div class="col-12 mt-5">
                <h2>Related Products</h2>
            </div>
            {{-- Loop through related products --}}
            @foreach($related as $rel)
            {{-- Generate image URL for the related product --}}
            @php $imgURL = ( !empty($settings->store_id) && isset($rel['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->store_id . '/' . $rel['id'] . '/' . $rel['images'][0]['image_large'] : ''; @endphp
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    {{-- Display related product image or a placeholder --}}
                    @if( !empty($imgURL) )
                    <div class="image-container">
                        <img src="{{ $imgURL }}" class="img-fluid">
                    </div>
                    @else
                    <div class="card-img-top bg-secondary" style="height: 200px; display: flex; justify-content: center; align-items: center; color: white;">No Image</div>
                    @endif
                    <div class="card-body">
                        {{-- Display related product name --}}
                        <h5 class="card-title">{{ $rel['name'] }}</h5>
                        {{-- Display related product price (checks different price structures) --}}
                        @if( isset(current($rel['prices'][0])['price']) )
                        <p class="card-text">Price: ${{ current($rel['prices'][0])['price'] }}</p>
                        @elseif( isset(current($rel['prices'][0])[0]['price']) )
                        <p class="card-text">Price: ${{ current($rel['prices'][0])[0]['price'] }}</p>
                        @endif
                        {{-- Link to the related product's detail page --}}
                        <a href="{{ route('rentmy.detail', $rel['url']) }}" class="btn btn-primary">View Details</a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

    </div>

@endsection

@section('scripts')

    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script type="text/javascript">
let calendarOptions = {
    minDate: "today",
    altInput: true,
    clickOpens: false,
    enableTime: false,
    dateFormat: "Y-m-d",
    time_24hr: true,
    closeOnSelect: false, // Keep calendar open until Apply/Cancel is clicked
    // mode: "range",    

    onValueUpdate: function(selectedDates, dateStr, instance) {

        instance.input.value = instance._initialValue;
    },

    // Store the initial value when the calendar opens
    // This is useful for reverting on "Cancel"
    onOpen: function(selectedDates, dateStr, instance) {
        instance._initialValue = instance.input.value;
    },

    onReady: function(selectedDates, dateStr, instance) {
        // Create a div to hold the buttons
        const buttonContainer = document.createElement("div");
        buttonContainer.className = "flatpickr-button-container"; // Add a class for styling

        // Create the Apply button
        const applyButton = document.createElement("button");
        applyButton.innerHTML = "Apply";
        applyButton.className = "flatpickr-apply-button";

        // Create the Cancel button
        const cancelButton = document.createElement("button");
        cancelButton.innerHTML = "Cancel";
        cancelButton.className = "flatpickr-cancel-button";

        // Append buttons to the container
        buttonContainer.appendChild(cancelButton); // Add Cancel first to appear on the left
        buttonContainer.appendChild(applyButton);

        // Append the button container to the calendar container
        // You might want to adjust the exact placement based on Flatpickr's structure.
        // A common place is just before the time or day picker.
        // instance.calendarContainer.appendChild(buttonContainer); // Appends at the very end
        // A more common placement for action buttons is at the bottom before any close button.
        // Let's try to append it before the time/date picker elements if they exist.
        const flatpickrDays = instance.calendarContainer.querySelector('.flatpickr-days');
        const flatpickrTime = instance.calendarContainer.querySelector('.flatpickr-time');

        instance.calendarContainer.appendChild(buttonContainer);

        // Add event listener to the "Apply" button
        applyButton.addEventListener("click", function() {

            if( instance.selectedDates[1] != undefined ) {
                const startDate = instance.selectedDates[0];
                const endDate = instance.selectedDates[1];
                $('.date_start_detail').val( instance.formatDate(startDate, instance.config.dateFormat) )
                $('.date_end_detail').val( instance.formatDate(endDate, instance.config.dateFormat) )
                $('.rent_start_to_rent_end').text( instance.formatDate(startDate, instance.config.dateFormat) + ' to ' + instance.formatDate(endDate, instance.config.dateFormat) )
                instance.close(); // Manually close the calendar
                setTimeout(() => {
                    getDatesPriceDuration();
                }, 100);
                return;
            } else {
                const selectedDate = instance.selectedDates[0];
                instance.input.value = instance.formatDate(selectedDate, instance.config.dateFormat);

                $('.date_start_detail').val( instance.formatDate(selectedDate, instance.config.dateFormat) )
                $('.rent_start_to_rent_end').text( instance.formatDate(selectedDate, instance.config.dateFormat) )
                setTimeout(() => {
                    getDatesPriceDuration();
                }, 100);


            }
            const selectedDate = instance.selectedDates[0];
            if (selectedDate) {
                // The input is already updated by flatpickr's selection.
                // If you had custom display logic, you'd apply it here.
                console.log("Applied Date:", instance.formatDate(selectedDate, instance.config.dateFormat));
            } else {
                console.log("No date selected to apply.");
            }
            instance.close(); // Manually close the calendar
        });

        // Add event listener to the "Cancel" button
        cancelButton.addEventListener("click", function() {
            // Revert the input field to its value before the calendar opened
            // instance.input.value = instance._initialValue;
            // Optionally, you might want to clear selectedDates internally if needed,
            // but simply reverting the input value and closing is usually enough for "Cancel".
            // instance.clear(); // Use with caution, as it also clears the internal selectedDates.
            instance.close(); // Manually close the calendar
        });
    },

    // Optional: on-close logic
    onClose: function(selectedDates, dateStr, instance) {
        // This hook fires regardless of how the calendar closes (Apply, Cancel, clicking outside).
        // If you need specific logic for when "Cancel" is used versus "Apply" or external close,
        // you'd need a flag set by the button clicks.
        // For simple cancel, the onOpen and onCancel logic above handles it.
    }

}

@if( $showStartDate && $showStartTime )
    calendarOptions.enableTime = true;
    calendarOptions.dateFormat = "Y-m-d H:i";
@endif

@if( $showEndDate )
    calendarOptions.mode = 'range';
@endif

@if( isset($cart['cart_items'][0]) && isset($cart['rent_start']) )
    calendarOptions.defaultDate = new Date("{{ $cart['rent_start'] . ' 12:00' }}");
@else
    calendarOptions.defaultDate = new Date();
@endif
const fp1 = flatpickr(".date_start_detail", calendarOptions);

@if( isset($cart['cart_items'][0]) && isset($cart['rent_end']) )
    calendarOptions.defaultDate = new Date("{{ $cart['rent_end'] . ' 12:00' }}");
@else
    calendarOptions.defaultDate.setDate(calendarOptions.defaultDate.getDate() + 1);
@endif
const fp2 = flatpickr(".date_end_detail", calendarOptions);

// Get all elements with the class 'flatpickr-input'
let flatpickrInputs = document.querySelectorAll('.flatpickr-input');
// Iterate over each element in the NodeList
flatpickrInputs.forEach(input => {
 input.style.display = 'none';
});
// Get all elements with the class 'flatpickr-input'
flatpickrInputs = document.querySelectorAll('.date_start_detail.form-control');
// Iterate over each element in the NodeList
flatpickrInputs.forEach(input => {
    input.style.visibility = 'hidden';
    input.style.height = 0;
});
// Get all elements with the class 'flatpickr-input'
flatpickrInputs = document.querySelectorAll('.date_end_detail.form-control');
// Iterate over each element in the NodeList
flatpickrInputs.forEach(input => {
input.style.display = 'none';
});



jQuery(document).ready(function($) {

    let gettingPrice = false;

    $('#rentmy-pick-start-date-label').on('click', function() {
        @if( isset($cart['cart_items'][0]) && isset($cart['rent_start']) )
            return;
        @endif
        fp1.toggle()
    });


    // Event listener for clicking on rental pricing options (daily/monthly)
    $('.rentmy-pricing-options label.rentmy-radio').on('click', function() {
        let me = this;
        // Remove 'active' class from all rental options and add it to the clicked one
        $('.rentmy-pricing-options label.rentmy-radio').removeClass('active');
        $(me).addClass('active');

        if( gettingPrice ) return;
        gettingPrice = true;

        setRentDate();
        setTimeout(() => {
            setRentPrice();
        }, 100);



    });

    // Function to set the displayed price based on the selected rental type (Buy or Rent)
    function setPrice() {
        let rental_type = $('.rental_type_detail').val(); // Get the selected rental type

        console.log('rental_type', rental_type); // Log the selected type

        if( rental_type == 'buy' ) {
            setBuyPrice(); // If 'buy', set the buy price
        } else {
            setRentPrice(); // If 'rent', set the rental price and date
            setRentDate();
        }
    }

    // Function to calculate and display the buy price based on quantity
    function setBuyPrice() {
        let buy_price = $('.rental_type_detail').data('buy_price'); // Get the base buy price

        console.log('buy_price', buy_price); // Log the base buy price

        let qty = $('.rentmy-modal-quantity .rentmy-in-num').val(); // Get the selected quantity
        if( buy_price ) {
            buy_price = buy_price * qty; // Calculate total price
            buy_price = "$" + buy_price.toFixed(2); // Format as currency
            $('.rentmy-product-price .title-price').html(buy_price); // Update the main price display
            $('.rentmy-product-price .title-duration').html(''); // Clear the duration text
        }
    }

    // Function to calculate and display the rental price based on the selected option and quantity
    function setRentPrice() {
        let titlePrice = $('.rentmy-pricing-options label.rentmy-radio.active').data('title-price'); // Get price from active rental option
        let titleDuration = $('.rentmy-pricing-options label.rentmy-radio.active').data('title-duration'); // Get duration from active rental option
        let qty = $('.rentmy-modal-quantity .rentmy-in-num').val(); // Get the selected quantity
        let price_id = $('.rentmy-pricing-options label.rentmy-radio.active').data('price_id') || 0; // Get the price ID from the active rental option
        let price = $('.rentmy-pricing-options label.rentmy-radio.active').data('price') || 0; // Get the price from the active rental option

        let date_start = $('.date_start_detail').val();
        let date_end = $('.date_end_detail').val();

        if( !date_start ) return;

        $.ajax({
            url: '/rentmy/get-price-value',
            type: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                "deposit_amount": {{ $product['deposit_amount'] }},
                "driving_license_required": false,
                "price": price,
                "product_id": {{ $product['id'] }},
                "quantity": qty,
                "variants_products_id": {{ $product['default_variant']['variants_products_id'] }},
                "location": {{ $settings->location }},
                "rent_start": date_start,
                "rent_end": date_end,
                "fullfilment_option": "",
                "rental_duration": 1,
                "rental_type": "rent",
                "sales_tax": {{ $product['sales_tax'] ?? 0 }},
                "term": 2,
                "token": "{{ $cartToken }}",
                "price_id": price_id,
                "custom_fields": ""
            },            
            success: function(response) {

                if(response?.data) {
                    price = response.data;
                    titlePrice = response.data;
                    $('.date_start_detail').val(response.start_date)
                    $('.date_end_detail').val(response.end_date)
                
                    if( price_id ) {
                        $('input[name="price_id"]').val(price_id);
                        $('input[name="price"]').val(price);
                    }
                    if( titlePrice ) {
                        titlePrice = titlePrice * qty; // Calculate total rental price
                        titlePrice = "$" + titlePrice.toFixed(2); // Format as currency
                        $('.rentmy-product-price .title-price').html(titlePrice); // Update the main price display
                        $('.rentmy-product-price .title-duration').html(titleDuration); // Update the duration text
                    }                
                
                }
                gettingPrice = false;
            },
            error: function(xhr, status, error) {
                console.error('Error getting price value:', error);
                gettingPrice = false;
            }
        });



    }

    // Function to set the rental start and end dates based on the selected option (Today, Tomorrow, Pick Date)
    function setRentDate(openCalendar = false) {
        let rent_start = $('.rentmy-pricing-options label.rentmy-radio.active').data('rent_start'); // Get start time from active rental option
        let rent_end = $('.rentmy-pricing-options label.rentmy-radio.active').data('rent_end'); // Get end time from active rental option
        let tomorrow_start = $('.rentmy-pricing-options label.rentmy-radio.active').data('tomorrow_start'); // Get tomorrow's start time
        let tomorrow_end = $('.rentmy-pricing-options label.rentmy-radio.active').data('tomorrow_end'); // Get tomorrow's end time
        let tomorrow = $('.rentmy-radio.tomorrow.active').text(); // Check if 'Tomorrow' option is active
        let pick = $('.rentmy-radio.pick.active').text(); // Check if 'Pick Start Date' option is active
        let pick2 = $('.rentmy-radio.pick2.active').text(); // Check if 'Pick End Date' option is active

        if( rent_start ) {
            if( openCalendar && pick ) {
                // If calendar should be opened and 'Pick Start Date' is selected, open the start date calendar (Mobiscroll)
                // picker = $('#calendar').mobiscroll('getInst'); // Commented out Mobiscroll line
                // picker.open(); // Commented out Mobiscroll line
                return false; // Prevent further date setting here
            } else if( openCalendar && pick2 ) {
                // If calendar should be opened and 'Pick End Date' is selected, open the end date calendar (Mobiscroll)
                // picker2 = $('#calendar2').mobiscroll('getInst'); // Commented out Mobiscroll line
                // picker2.open(); // Commented out Mobiscroll line
                return false; // Prevent further date setting here
            } else if( tomorrow ) {
                // If 'Tomorrow' is selected, set the dates to tomorrow's start and end times
                $('.date_start_detail').val(tomorrow_start);
                $('.date_end_detail').val(tomorrow_end);
                $('.rent_start_to_rent_end').html(tomorrow_start);
            } else {
                // Otherwise (if 'Today' is selected), set the dates to the rental option's start and end times
                $('.date_start_detail').val(rent_start);
                $('.date_end_detail').val(rent_end);
                $('.rent_start_to_rent_end').html(rent_start);
            }
        }
        setTimeout(() => {
            getDatesPriceDuration();
        }, 500);        
    }

    // Initial call to set the price when the page loads
    setPrice();

    // Event listener for clicking on product variant options
    $('.rentmy-modal-product-variants label.rentmy-radio').on('click', function() {
        let me = this;
        let price = $(me).find('input').data('price'); // Get the price associated with the variant

        console.log('price', price); // Log the variant price

        // Remove 'active' class from all variants and add it to the clicked one
        $('.rentmy-modal-product-variants label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        // If a price is associated with the variant, update the buy price data and refresh the price display
        if( price ) {
            $('.rental_type_detail').data('buy_price', price);
            setPrice();
        }
    });

    // Event listener for clicking on rental starting date options (Today, Tomorrow, Pick Date)
    $('.rentmy-modal-starting-from label.rentmy-radio').on('click', function() {
        let me = this;
        let forAttr = $(me).attr('for'); // Get the 'for' attribute to identify the selected option

        console.log('forAttr', forAttr); // Log the selected option's 'for' attribute

        // Remove 'active' class from all starting date options and add it to the clicked one
        $('.rentmy-modal-starting-from label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        // Refresh the rental price and potentially open the calendar if 'Pick Date' was selected
        setRentPrice();
        setRentDate(true); // Pass true to indicate a potential calendar opening
    });

    // Event listener for clicking on Buy or Rent radio buttons
    $('.rentmy-radio-inline.price-options label.rentmy-radio').on('click', function() {
        let me = this;
        // Remove 'active' class from Buy/Rent options and add it to the clicked one
        $('.rentmy-radio-inline.price-options label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        let buttonText = $(me).text().trim(); // Get the text of the selected button

        // Toggle the visibility of rental options based on the selection
        if( buttonText == 'Buy' ) {
            $('.rentmy-pricing-options').hide(); // Hide rental options if 'Buy' is selected
            $('.rental_type_detail').val('buy'); // Set the rental type hidden input to 'buy'
        } else if( buttonText == 'Rent' ) {
            $('.rentmy-pricing-options').show(); // Show rental options if 'Rent' is selected
            $('.rental_type_detail').val('rent'); // Set the rental type hidden input to 'rent'
        }
        setPrice(); // Update the displayed price based on the new rental type
    });

    // Event listener for changes in the quantity input field
    $('.rentmy-modal-quantity .rentmy-in-num').on('change', function() {
        var value = $(this).val(); // Get the current quantity value
        var available = $('.rentmy-product-available').text().trim(); // Get the available quantity
        value = parseInt(value); // Convert quantity to integer
        available = parseInt(available); // Convert availability to integer

        // Check if the selected quantity exceeds the available quantity
        if( value > available ) {
            $('.available-error').html('Selected quantity is not available'); // Display an error message
            $('.rentmy-button.rentmy-modal-cartbtn').attr('disabled', true).addClass('disabled'); // Disable the Add to Cart button
        } else {
            $('.available-error').html(''); // Clear the error message
            $('.rentmy-button.rentmy-modal-cartbtn').attr('disabled', false).removeClass('disabled'); // Enable the Add to Cart button
        }
        setPrice(); // Update the displayed price based on the new quantity
    });

});


function getDatesPriceDuration() {

    let  start_date = $('.date_start_detail').val();
    if( start_date == undefined || start_date == '' ) {
        start_date = $('.rentmy-pricing-options label.rentmy-radio.active').data('rent_start');
    }
    if( start_date == undefined || start_date == '' ) {
        start_date = $('.rentmy-pricing-options label.rentmy-radio').first().data('rent_start');
    }
    if( start_date == undefined || start_date == '' ) {
        return;
    }
    let  price_id = $('.rentmy-pricing-options label.rentmy-radio.active').data('price_id');
    if( price_id == undefined ) {
        price_id = $('.rentmy-pricing-options label.rentmy-radio').first().data('price_id');
    }
    let  location = "{{ $settings->location_id }}";

    $.ajax({
        url: '/rentmy/get-dates-price-duration',
        type: 'GET',
        data: {
            "start_date": start_date,
            "start_time": "12:00",
            "price_id": price_id,
            "location": location
        },
        success: function(response) {
            console.log('response', response);
            if(response?.data?.end_date) {
                $('#rent_end_input').val(response?.data?.end_date);
                $('.date_start_detail').val(response?.data?.start_date);
                fp1.selectedDates = [new Date(response?.data?.start_date)];
            }
        },
        error: function(xhr, status, error) {
            console.error('Error getting dates, price, and duration:', error);
        }
    });
}
setTimeout(() => {
    getDatesPriceDuration();
}, 500);


</script>

@endsection