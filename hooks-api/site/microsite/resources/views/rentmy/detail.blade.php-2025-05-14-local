{{-- resources/views/products/show.blade.php --}}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- HTML Meta Tags -->
    <title>{{ $product['seo']['meta_title'] ?? '' }}</title>
    <meta name="description" content="{{ $product['seo']['meta_description'] ?? '' }}" />

    <!-- Google / Search Engine Tags -->
    <meta itemprop="name" content="{{ $product['seo']['meta_title'] ?? '' }}" />
    <meta itemprop="description" content="{{ $product['seo']['meta_description'] ?? '' }}" />
    @php $imgURL = ( !empty($settings->location) && isset($product['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->location . '/' . $product['id'] . '/' . $product['images'][0]['image_large'] : ''; @endphp
    <meta itemprop="image" content="{{ $imgURL }}" />

    <!-- Facebook Meta Tags -->
    <meta property="og:url" content="{{ Request::url() }}" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{{ $product['seo']['meta_title'] ?? '' }}" />
    <meta property="og:description" content="{{ $product['seo']['meta_description'] ?? '' }}" />
    <meta property="og:image" content="{{ $imgURL }}" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{ $product['seo']['meta_title'] ?? '' }}" />
    <meta name="twitter:description" content="{{ $product['seo']['meta_description'] ?? '' }}" />
    <meta name="twitter:image" content="{{ $imgURL }}" />

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>

    <style>
        .image-container {
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            position: relative;
            overflow: hidden;
        }

        .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>

    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav">
                @foreach($navigation as $nav)
                <li class="nav-item">
                    <a class="nav-link" href="#">{{ $nav['label'] }}</a>
                </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-6">
                @if( !empty($imgURL) )
                     <div class="image-container">
                        <img src="{{ $imgURL }}" class="img-fluid">
                    </div>
                @else
                    <div class="bg-secondary" style="height: 300px; display: flex; justify-content: center; align-items: center; color: white;">No Images</div>
                @endif
            </div>
            <div class="col-md-6">
                <h1>{{ $product['name'] }}</h1>
                {!!  $product['description'] !!}
                <p>Buy Price: ${{ $product['buy_price'] }}</p>
                <p>Rent Price: ${{ $product['rent_price'] }}</p>
                <p>Sales Tax: {{ $product['sales_tax'] }}</p>
                <p>Deposit Amount: {{ $product['deposit_amount'] }}</p>
                <p>Keyword: {{ $product['keyword'] }}</p>
                <p>Store ID: {{ $product['store_id'] }}</p>
                <p>Status: {{ $product['status'] }}</p>
                <p>Cart Available: {{ $product['cart_available'] }}</p>
                <p>Available: {{ $product['available'] }}</p>


                <a href="{{ route('rentmy.index') }}" class="btn btn-secondary">Back to Products</a>
            </div>
        </div>


        <div class="row mt-5">
            <div class="col-12">
                <h2>Related Products</h2>
            </div>
            @foreach($related as $rel)
                @php $imgURL = ( !empty($settings->location) && isset($rel['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->location . '/' . $rel['id'] . '/' . $rel['images'][0]['image_large'] : ''; @endphp
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        @if( !empty($imgURL) )
                             <div class="image-container">
                                <img src="{{ $imgURL }}" class="img-fluid">
                            </div>
                        @else
                            <div class="card-img-top bg-secondary" style="height: 200px; display: flex; justify-content: center; align-items: center; color: white;">No Image</div>
                        @endif
                        <div class="card-body">
                            <h5 class="card-title">{{ $rel['name'] }}</h5>
                            @if( isset(current($rel['prices'][0])['price']) )
                                <p class="card-text">Price: ${{ current($rel['prices'][0])['price'] }}</p>
                            @elseif( isset(current($rel['prices'][0])[0]['price']) )
                                <p class="card-text">Price: ${{ current($rel['prices'][0])[0]['price'] }}</p>
                            @endif
                            <a href="{{ route('rentmy.detail', $rel['url']) }}" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
