{{--
    resources/views/products/show.blade.php

    This Blade template displays the details of a single product.
    It includes SEO meta tags, product information (image, price, variants, quantity),
    a form to add the product to the cart, related products, and navigation.
--}}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $product['seo']['meta_title'] ?? '' }}</title> {{-- Page title dynamically set from product SEO data --}}
    <meta name="description" content="{{ $product['seo']['meta_description'] ?? '' }}" /> {{-- Meta description from product SEO data --}}

    <meta itemprop="name" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Item name for rich snippets --}}
    <meta itemprop="description" content="{{ $product['seo']['meta_description'] ?? '' }}" /> {{-- Item description for rich snippets --}}
    {{--
        P<PERSON> block to dynamically generate the product image URL for meta tags.
        Checks if location and large image exist and constructs the URL using S3 bucket and location.
    --}}
    @php $imgURL = ( !empty($settings->location) && isset($product['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->location . '/' . $product['id'] . '/' . $product['images'][0]['image_large'] : ''; @endphp
    <meta itemprop="image" content="{{ $imgURL }}" /> {{-- Item image URL for rich snippets --}}

    <meta property="og:url" content="{{ Request::url() }}" /> {{-- Canonical URL of the page --}}
    <meta property="og:type" content="website" /> {{-- Type of content --}}
    <meta property="og:title" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Title for social media sharing --}}
    <meta property="og:description" content="{{ $product['seo']['meta_description'] ?? '' }}" /> {{-- Description for social media sharing --}}
    <meta property="og:image" content="{{ $imgURL }}" /> {{-- Image URL for social media sharing --}}

    <meta name="twitter:card" content="summary_large_image" /> {{-- Specifies the type of Twitter card --}}
    <meta name="twitter:title" content="{{ $product['seo']['meta_title'] ?? '' }}" /> {{-- Title for Twitter sharing --}}
    <meta name="twitter:description" content="{{ $product['seo']['meta_description'] ?? '' }}" /> {{-- Description for Twitter sharing --}}
    <meta name="twitter:image" content="{{ $imgURL }}" /> {{-- Image URL for Twitter sharing --}}

    {{-- Link to Bootstrap CSS for basic styling --}}
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>

{{-- Body tag with a custom class --}}
<body class="rentmy-main-wrapper">
    {{--
        Inline CSS styles for specific elements.
        Defines styles for image containers, aspect ratios, and theme buttons.
    --}}
    <style>
        .image-container {
            width: 100%;
            padding-bottom: 56.25%;
            /* 16:9 aspect ratio */
            position: relative;
            overflow: hidden;
        }

        .image-container img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain; /* Ensures the entire image is visible without distortion */
        }

        a.btn,
        .theme-btn {
            background-color: rgb(97, 175, 175); /* Custom background color for buttons */
            color: #ffffff; /* White text color for buttons */
        }

    </style>

    {{--
        Inline CSS styles for specific RentMy components.
        Defines styles for custom radio buttons, spacing, pricing display, etc.
    --}}
    <style>
        label.rentmy-radio {
            background: rgb(255, 255, 255);
            display: inline-block;
            -webkit-box-align: center;
            align-items: center;
            -webkit-box-pack: center;
            justify-content: center;
            padding: 4px 8px;
            padding-left: 8px !important;
            border: 1px solid rgb(0, 0, 0);
            position: relative;
            margin-bottom: 12px;
            cursor: pointer;
        }
        label.rentmy-radio.active {
            background: rgb(0, 0, 0); /* Background color when radio button is active */
            color: #fff !important; /* Text color when radio button is active */
        }
        label.rentmy-radio input[type="radio"] {
            position: fixed; /* Hides the default radio button */
            opacity: 0;
            pointer-events: none;
        }

        div.rentmy-radio-inline.price-options label.rentmy-radio {
            font-size: 20px;
            font-weight: 700;
        }

        div.rentmy-modal-product-variants,
        div.rentmy-modal-starting-from {
            margin-bottom: 16px;
        }
        div.rentmy-modal-product-variants h5,
        div.rentmy-pricing-options h5,
        div.rentmy-modal-starting-from h5 {
            margin-bottom: 8px;
        }
        div.rentmy-modal-product-variants ul li,
        div.rentmy-modal-starting-from ul li {
            display: inline-block; /* Displays list items horizontally */
        }

        .rentmy-product-price .title-price {
            font-size: 48px; /* Large font size for the main price display */
        }
        .rentmy-product-price .title-duration {
            font-size: 26px; /* Font size for the duration text */
        }
        div.rentmy-pricing-options ul li {
            width: 32%; /* Sets width for rental option buttons */
            display:  inline-block;
        }
        div.rentmy-pricing-options label.rentmy-radio {
            width: 100%; /* Makes the radio button label fill the list item width */
        }
        p.available-error,
        p.not-available {
            color: red; /* Red color for availability messages */
            font-weight: 700 !important;
        }

        .rentmy-button.rentmy-modal-cartbtn.disabled,
        .rentmy-button.rentmy-modal-cartbtn.disabled:hover,
        .rentmy-button.rentmy-modal-cartbtn.disabled:before {
            background-color: #d2d1d1!important; /* Grey background for disabled button */
            border-color: #d2d1d1!important;
        }

        .rentmy-minus,
        .rentmy-plus {
            cursor: pointer;
        }

    </style>

    {{-- Bootstrap Navbar for navigation --}}
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        {{-- Navbar toggler button for mobile view --}}
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        {{-- Navbar links, collapsed on small screens --}}
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav">
                {{-- Loop through navigation items and display them --}}
                @foreach($navigation as $nav)
                <li class="nav-item">
                    {{-- Link label from navigation data --}}
                    <a class="nav-link" href="#">{{ $nav['label'] }}</a>
                </li>
                @endforeach
            </ul>
        </div>
    </nav>




    {{-- Main container for product details --}}
    <div class="container mt-4">

    @if(session('error'))
        <div class="alert alert-danger" role="alert">
            {{ session('error') }}
        </div>
    @endif

    <?php                        
    $haveRentalOptions = ( isset($product['prices'][0]['daily']) || isset($product['prices'][0]['monthly']) ) ? true : false;
    $haveBuy = ( $product['buy_price'] > 0 ) ? true : false;
    ?>

    <h3>Details</h3>    

        <div class="row">
            {{-- Product Image Section --}}
            <div class="col-md-6">
                {{-- Check if an image URL exists --}}
                @if( !empty($imgURL) )
                {{-- Display the product image within an aspect ratio container --}}
                <div class="image-container">
                    <img src="{{ $imgURL }}" class="img-fluid">
                </div>
                @else
                {{-- Display a placeholder if no image is available --}}
                <div class="bg-secondary" style="height: 300px; display: flex; justify-content: center; align-items: center; color: white;">No Images</div>
                @endif
            </div>
            {{-- Product Details and Add to Cart Form Section --}}
            <div class="col-md-6">

                {{-- Container for product payment and form details --}}
                <div class="col-lg-6 col-md-12 col-sm-12 col-12 product-payment-details">

                    {{-- Form to add the product to the cart --}}
                    <form action="{{ route('rentmy.addToCart') }}" method="POST">

                        @csrf {{-- CSRF token for form submission security --}}
                        {{-- Hidden inputs for various product and rental/buy details --}}
                        <input type="hidden" name="deposit_amount" value="0">
                        <input type="hidden" name="deposite_tax" value="">
                        <input type="hidden" name="driving_license_required" value="false">
                        <input type="hidden" name="product_id" value="{{ $product['id'] }}">
                        <input type="hidden" name="variants_products_id" value="{{ $product['default_variant']['variants_products_id'] }}">
                        <input type="hidden" name="location" value="474">
                        <input type="hidden" name="rent_start" value="">
                        <input type="hidden" name="rent_end" value="">
                        <input type="hidden" name="fullfilment_option" value="">
                        <input type="hidden" name="rental_duration" value="0">
                        <input type="hidden" name="rental_type" value="buy">
                        <input type="hidden" name="sales_tax" value="">
                        <input type="hidden" name="term" value="">
                        <input type="hidden" name="token" value="{{ session('rentmy_cart.token') }}">
                        {{--
                           PHP logic to set the initial 'price_id' and 'price' hidden inputs in the form.
                           This determines the default price and price identifier passed when the form is submitted
                           (before any user interaction changes the selected price/rental option).
                           It checks for different pricing structures in the product data.
                       --}}
                       <?php if( $product['buy_price'] <= 0 && isset($product['prices'][0]['base']['price']) ): ?>
                           {{-- If no rental or buy price, but a base price exists, use the base price --}}
                            <input type="hidden" name="price_id" value="{{ $product['prices'][0]['base']['id'] }}"> {{-- Set price_id to the base price's ID --}}
                            <input type="hidden" name="price" value="{{ $product['prices'][0]['base']['price'] }}"> {{-- Set price to the base price value --}}
                        <?php elseif( $haveBuy ): ?>
                            {{-- If only a buy option exists, use the buy price --}}
                            <input type="hidden" name="price_id" value=""> {{-- price_id is empty for buy price --}}
                            <input type="hidden" name="price" value="{{ $product['buy_price'] }}"> {{-- Set price to the buy price value --}}
                        <?php endif ?>
                        <input type="hidden" name="custom_fields" value="[]">
                        <input type="hidden" name="is_apply" value="false">
                        <input type="hidden" name="zone" value="-180">
                        <input type="hidden" name="slug" value="{{ $product['slug'] }}">

                        {{--
                            PHP block to determine if rental options or buy option are available.
                            Used to control the display of Buy/Rent radio buttons.
                        --}}


                    <div class="rentmy-product-rightside">
                        {{-- Product Title --}}
                        <h1 class="rentmy-product-title">
                            <?= $product['name'] ?> {{-- Displays the product name --}}
                        </h1>

                        {{--
                            PHP logic to display the product price.
                            Checks for base price, daily/monthly rental starting prices, or buy price.
                        --}}
                        <?php if( $product['buy_price'] <= 0 && isset($product['prices'][0]['base']['price']) ): ?>
                            <h2 class="rentmy-product-price">
                                <span class="title-price">$<?= number_format($product['prices'][0]['base']['price'], 2) ?></span> {{-- Displays base price --}}
                            </h2>
                        <?php elseif( $haveBuy ): ?>
                            <p class="rentmy-product-price">
                                <span class="title-price">$<?= number_format($product['buy_price'], 2) ?></span> {{-- Displays buy price --}}
                            </p>
                        <?php endif ?>

                        {{-- Section for selecting Buy or Rent option --}}
                        <div class="rentmy-modal-rentbuy">

                            <div class="rentmy-radio-inline price-options">

                                {{-- Hidden input if only Buy is available --}}
                                <input type="hidden" id="rentmy-rent-hidden" name="rentmy-package" value="buy" />

                                <input type="hidden" class="rental_type_detail" name="rental_type_detail" value="rent" data-buy_price="<?= $product['buy_price'] ?>" />

                            </div>

                        </div>

                        {{-- Section for selecting product variants --}}
                        <div class="rentmy-modal-product-variants">

                            {{-- Loop through variant sets (e.g., Size, Color) --}}
                            <?php foreach($product['variant_set_list'] as $list): ?>
                                <h5><?= $list['name'] ?></h5> {{-- Display the variant set name --}}
                                <ul>
                                    {{-- Loop through individual variants --}}
                                    <?php foreach($product['variant_list'] as $i => $variant): ?>
                                    {{-- Skip variants that don't belong to the current set --}}
                                    <?php if( $variant['variant_set_id'] != $list['id'] ) continue; ?>
                                    {{-- Special handling for 'Price' variants (assuming variant name is the price) --}}
                                    <?php if( $list['name'] == 'Price' ): ?>
                                    <li>
                                     {{-- Radio button for a 'Price' variant --}}
                                     <label class="rentmy-radio <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'active' : ''; ?>" for="rentmy-variant<?= $i ?>">
                                        <input type="radio" id="rentmy-variant<?= $i ?>" name="rentmy-<?= $list['id'] ?>"  value="<?= $variant['id'] ?>" <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'checked' : ''; ?> data-price="<?= str_replace('$', '', $variant['name']) ?>" /> <?= $variant['name'] ?> {{-- Radio button label with variant name/price --}}
                                    </label>
                                    </li>
                                    <?php else: ?>
                                    <li>
                                     {{-- Radio button for a standard variant --}}
                                     <label class="rentmy-radio <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'active' : ''; ?>" for="rentmy-variant<?= $i ?>">
                                        <input type="radio" id="rentmy-variant<?= $i ?>" name="rentmy-<?= $list['id'] ?>"  value="<?= $variant['id'] ?>" <?= ($product['default_variant']['variant_chain_id'] == $variant['id']) ? 'checked' : ''; ?> /> <?= $variant['name'] ?> {{-- Radio button label with variant name --}}
                                    </label>
                                    </li>
                                    <?php endif ?>
                                <?php endforeach ?>
                                </ul>
                            <?php endforeach ?>
                        </div>

                        {{-- Section for selecting quantity --}}
                        <div class="rentmy-modal-quantity">
                            <div class="rentmy-number-block">
                                <div class="rentmy-num-in">
                                    {{-- Quantity decrement button --}}
                                    <span class="rentmy-minus" onclick="this.nextElementSibling.value = parseInt(this.nextElementSibling.value) - 1">-</span>
                                    {{-- Quantity input field (readonly, updated via buttons) --}}
                                    <input name="quantity" type="text" class="rentmy-in-num" value="1" readonly="">
                                    {{-- Quantity increment button --}}
                                    <span class="rentmy-plus" onclick="this.previousElementSibling.value = parseInt(this.previousElementSibling.value) + 1">+</span>
                                </div>
                                {{-- Displays the number of available items --}}
                                <p>Available : <span class="rentmy-product-available"><?= $product['available']?></span></p>
                                 {{-- Display an error message if quantity exceeds availability (initially hidden) --}}
                                 <?php if( $product['available'] > 0 ): ?>
                                    <p class="available-error"></p>
                                <?php endif ?>
                            </div>
                        </div>

                        {{-- Add to Cart button --}}
                        <?php if( $product['available'] > 0 && ( $product['buy_price'] > 0 || isset($product['prices'][0]['base']['price']) ) ): ?>
                            {{-- Submit button if product is available --}}
                            <button type="submit" class="rentmy-button rentmy-modal-cartbtn">Add to Cart</button>
                        <?php else: ?>
                            {{-- Message and disabled button if product is not available --}}
                            <p class="not-available">Product is not available to buy</p>
                            <button type="button" class="rentmy-button rentmy-modal-cartbtn" disabled>Add to Cart</button>
                        <?php endif ?>
                    </div>

                </form>

                <br/>
                <br/>

                {{-- Link back to the main products page --}}
                <a href="{{ route('rentmy.index') }}" class="btn btn-secondary">Back to Products</a>

            </div>
        </div>

        {{-- Related Products Section --}}
        <div class="row mt-5">
            <div class="col-12">
                <h2>Related Products</h2>
            </div>
            {{-- Loop through related products --}}
            @foreach($related as $rel)
            {{-- Generate image URL for the related product --}}
            @php $imgURL = ( !empty($settings->location) && isset($rel['images'][0]['image_large']) ) ? env('RENTMY_S3_URL') . '/products/' . $settings->location . '/' . $rel['id'] . '/' . $rel['images'][0]['image_large'] : ''; @endphp
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    {{-- Display related product image or a placeholder --}}
                    @if( !empty($imgURL) )
                    <div class="image-container">
                        <img src="{{ $imgURL }}" class="img-fluid">
                    </div>
                    @else
                    <div class="card-img-top bg-secondary" style="height: 200px; display: flex; justify-content: center; align-items: center; color: white;">No Image</div>
                    @endif
                    <div class="card-body">
                        {{-- Display related product name --}}
                        <h5 class="card-title">{{ $rel['name'] }}</h5>
                        {{-- Display related product price (checks different price structures) --}}
                        @if( isset(current($rel['prices'][0])['price']) )
                        <p class="card-text">Price: ${{ current($rel['prices'][0])['price'] }}</p>
                        @elseif( isset(current($rel['prices'][0])[0]['price']) )
                        <p class="card-text">Price: ${{ current($rel['prices'][0])[0]['price'] }}</p>
                        @endif
                        {{-- Link to the related product's detail page --}}
                        <a href="{{ route('rentmy.detail', $rel['url']) }}" class="btn btn-primary">View Details</a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

    </div>

    {{-- Include jQuery, Popper.js, and Bootstrap JS --}}
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    {{-- Uncomment the line below if you have a custom rentmy.js file --}}

<script type="text/javascript">
jQuery(document).ready(function($) {

    // Event listener for clicking on rental pricing options (daily/monthly)
    $('.rentmy-pricing-options label.rentmy-radio').on('click', function() {
        let me = this;
        // Remove 'active' class from all rental options and add it to the clicked one
        $('.rentmy-pricing-options label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        // Update the displayed price and date based on the selected rental option
        setRentPrice();
        setRentDate();
    });

    // Function to set the displayed price based on the selected rental type (Buy or Rent)
    function setPrice() {
        let rental_type = $('.rental_type_detail').val(); // Get the selected rental type

        console.log('rental_type', rental_type); // Log the selected type

        if( rental_type == 'buy' ) {
            setBuyPrice(); // If 'buy', set the buy price
        } else {
            setRentPrice(); // If 'rent', set the rental price and date
            setRentDate();
        }
    }

    // Function to calculate and display the buy price based on quantity
    function setBuyPrice() {
        let buy_price = $('.rental_type_detail').data('buy_price'); // Get the base buy price

        console.log('buy_price', buy_price); // Log the base buy price

        let qty = $('.rentmy-modal-quantity .rentmy-in-num').val(); // Get the selected quantity
        if( buy_price ) {
            buy_price = buy_price * qty; // Calculate total price
            buy_price = "$" + buy_price.toFixed(2); // Format as currency
            $('.rentmy-product-price .title-price').html(buy_price); // Update the main price display
            $('.rentmy-product-price .title-duration').html(''); // Clear the duration text
        }
    }

    // Function to calculate and display the rental price based on the selected option and quantity
    function setRentPrice() {
        let titlePrice = $('.rentmy-pricing-options label.rentmy-radio.active').data('title-price'); // Get price from active rental option
        let titleDuration = $('.rentmy-pricing-options label.rentmy-radio.active').data('title-duration'); // Get duration from active rental option
        let qty = $('.rentmy-modal-quantity .rentmy-in-num').val(); // Get the selected quantity
        if( titlePrice ) {
            titlePrice = titlePrice.replace('$', ''); // Remove dollar sign for calculation
            titlePrice = titlePrice * qty; // Calculate total rental price
            titlePrice = "$" + titlePrice.toFixed(2); // Format as currency
            $('.rentmy-product-price .title-price').html(titlePrice); // Update the main price display
            $('.rentmy-product-price .title-duration').html(titleDuration); // Update the duration text
        }
    }

    // Function to set the rental start and end dates based on the selected option (Today, Tomorrow, Pick Date)
    function setRentDate(openCalendar = false) {
        let rent_start = $('.rentmy-pricing-options label.rentmy-radio.active').data('rent_start'); // Get start time from active rental option
        let rent_end = $('.rentmy-pricing-options label.rentmy-radio.active').data('rent_end'); // Get end time from active rental option
        let tomorrow_start = $('.rentmy-pricing-options label.rentmy-radio.active').data('tomorrow_start'); // Get tomorrow's start time
        let tomorrow_end = $('.rentmy-pricing-options label.rentmy-radio.active').data('tomorrow_end'); // Get tomorrow's end time
        let tomorrow = $('.rentmy-radio.tomorrow.active').text(); // Check if 'Tomorrow' option is active
        let pick = $('.rentmy-radio.pick.active').text(); // Check if 'Pick Start Date' option is active
        let pick2 = $('.rentmy-radio.pick2.active').text(); // Check if 'Pick End Date' option is active

        if( rent_start ) {
            if( openCalendar && pick ) {
                // If calendar should be opened and 'Pick Start Date' is selected, open the start date calendar (Mobiscroll)
                picker = $('#calendar').mobiscroll('getInst'); // Commented out Mobiscroll line
                picker.open(); // Commented out Mobiscroll line
                return false; // Prevent further date setting here
            } else if( openCalendar && pick2 ) {
                // If calendar should be opened and 'Pick End Date' is selected, open the end date calendar (Mobiscroll)
                picker2 = $('#calendar2').mobiscroll('getInst'); // Commented out Mobiscroll line
                picker2.open(); // Commented out Mobiscroll line
                return false; // Prevent further date setting here
            } else if( tomorrow ) {
                // If 'Tomorrow' is selected, set the dates to tomorrow's start and end times
                $('.date_start_detail').val(tomorrow_start);
                $('.date_end_detail').val(tomorrow_end);
            } else {
                // Otherwise (if 'Today' is selected), set the dates to the rental option's start and end times
                $('.date_start_detail').val(rent_start);
                $('.date_end_detail').val(rent_end);
            }
        }
    }

    // Initial call to set the price when the page loads
    setPrice();

    // Event listener for clicking on product variant options
    $('.rentmy-modal-product-variants label.rentmy-radio').on('click', function() {
        let me = this;
        let price = $(me).find('input').data('price'); // Get the price associated with the variant

        console.log('price', price); // Log the variant price

        // Remove 'active' class from all variants and add it to the clicked one
        $('.rentmy-modal-product-variants label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        // If a price is associated with the variant, update the buy price data and refresh the price display
        if( price ) {
            $('.rental_type_detail').data('buy_price', price);
            setPrice();
        }
    });

    // Event listener for clicking on rental starting date options (Today, Tomorrow, Pick Date)
    $('.rentmy-modal-starting-from label.rentmy-radio').on('click', function() {
        let me = this;
        let forAttr = $(me).attr('for'); // Get the 'for' attribute to identify the selected option

        console.log('forAttr', forAttr); // Log the selected option's 'for' attribute

        // Remove 'active' class from all starting date options and add it to the clicked one
        $('.rentmy-modal-starting-from label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        // Refresh the rental price and potentially open the calendar if 'Pick Date' was selected
        setRentPrice();
        setRentDate(true); // Pass true to indicate a potential calendar opening
    });

    // Event listener for clicking on Buy or Rent radio buttons
    $('.rentmy-radio-inline.price-options label.rentmy-radio').on('click', function() {
        let me = this;
        // Remove 'active' class from Buy/Rent options and add it to the clicked one
        $('.rentmy-radio-inline.price-options label.rentmy-radio').removeClass('active');
        $(me).addClass('active');
        let buttonText = $(me).text().trim(); // Get the text of the selected button

        // Toggle the visibility of rental options based on the selection
        if( buttonText == 'Buy' ) {
            $('.rentmy-pricing-options').hide(); // Hide rental options if 'Buy' is selected
            $('.rental_type_detail').val('buy'); // Set the rental type hidden input to 'buy'
        } else if( buttonText == 'Rent' ) {
            $('.rentmy-pricing-options').show(); // Show rental options if 'Rent' is selected
            $('.rental_type_detail').val('rent'); // Set the rental type hidden input to 'rent'
        }
        setPrice(); // Update the displayed price based on the new rental type
    });

    // Event listener for changes in the quantity input field
    $('.rentmy-modal-quantity .rentmy-in-num').on('change', function() {
        var value = $(this).val(); // Get the current quantity value
        var available = $('.rentmy-product-available').text().trim(); // Get the available quantity
        value = parseInt(value); // Convert quantity to integer
        available = parseInt(available); // Convert availability to integer

        // Check if the selected quantity exceeds the available quantity
        if( value > available ) {
            $('.available-error').html('Selected quantity is not available'); // Display an error message
            $('.rentmy-button.rentmy-modal-cartbtn').attr('disabled', true).addClass('disabled'); // Disable the Add to Cart button
        } else {
            $('.available-error').html(''); // Clear the error message
            $('.rentmy-button.rentmy-modal-cartbtn').attr('disabled', false).removeClass('disabled'); // Enable the Add to Cart button
        }
        setPrice(); // Update the displayed price based on the new quantity
    });

});

</script>

</body>

</html>