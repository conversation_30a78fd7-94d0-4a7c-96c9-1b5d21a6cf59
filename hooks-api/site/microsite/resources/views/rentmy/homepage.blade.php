@extends('rentmy.layout')

@section('title', $site_specific['meta_contents']['home_page_title'] ?? $settings->store_slug)

@section('metas')

    <meta itemprop="name" content="{{ $site_specific['meta_contents']['home_page_title'] ?? $settings->store_slug }}" /> 
    <meta itemprop="description" content="{{ $site_specific['meta_contents']['home_page_description'] ?? '' }}" /> 
    <meta itemprop="keywords" content="{{ $site_specific['meta_contents']['home_page_keywords'] ?? '' }}" />
    
    <meta itemprop="image" content="{{ $site_specific['general']['favicon'] ?? '' }}" /> 

    <meta property="og:url" content="{{ Request::url() }}" /> 
    <meta property="og:type" content="website" /> 
    <meta property="og:title" content="{{ $site_specific['meta_contents']['home_page_title'] ?? $settings->store_slug }}" /> 
    <meta property="og:description" content="{{ $site_specific['meta_contents']['home_page_description'] ?? '' }}" /> 
    <meta property="og:image" content="{{ $site_specific['general']['favicon'] ?? '' }}" /> 

    <meta name="twitter:card" content="summary_large_image" /> 
    <meta name="twitter:title" content="{{ $site_specific['meta_contents']['home_page_title'] ?? $settings->store_slug }}" /> 
    <meta name="twitter:description" content="{{ $site_specific['meta_contents']['home_page_description'] ?? '' }}" /> 
    <meta name="twitter:image" content="{{ $site_specific['general']['favicon'] ?? '' }}" /> 

    <script id="structured-data" type="application/ld+json">{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "{{ $site_specific['meta_contents']['home_page_title'] ?? $settings->store_slug }}",
    "url": "{{ Request::url() }}",
    "description": "{{ $site_specific['meta_contents']['home_page_description'] ?? '' }}"
    }</script>

@endsection

@section('content')

@foreach( $contents as $content )
    @if($content['page_slug'] != 'home') 
        @continue
    @endif
    @if($content['status'] != 1) 
        @continue
    @endif    
    <div>{!! $content['content'] !!}</div>
@endforeach


@endsection
