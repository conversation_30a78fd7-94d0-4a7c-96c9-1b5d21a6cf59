{{-- resources/views/products/index.blade.php --}}
@extends('rentmy.layout')

@section('title', 'Products')

@section('styles')
<style>
    .image-container {
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        position: relative;
        overflow: hidden;
    }

    .image-container img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
</style>
@endsection

@section('content')
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <h4>Categories</h4>
                <ul class="list-group">
                    @foreach ($categories as $category)
                        <li class="list-group-item">
                            <a href="{{ route('rentmy.category', $category['uuid']) }}">{{ $category['name'] }}</a>
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="col-md-9">
                <h1>Products</h1>
                <div class="row">
                    @foreach ($products as $product)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                @if( !empty($settings->store_id) && isset($product['images'][0]['image_large']) )
                                    <div class="image-container">
                                        <img src="{{ env('RENTMY_S3_URL') . '/products/' . $settings->store_id . '/' . $product['id'] . '/' . $product['images'][0]['image_large'] }}" alt="{{ $product['name'] }}" class="img-fluid">
                                    </div>

                                @else
                                <div class="card-img-top bg-secondary" style="height: 200px; display: flex; justify-content: center; align-items: center; color: white;">No Image</div>
                                @endif
                                <div class="card-body">
                                    <h5 class="card-title">{{ $product['name'] }}</h5>
                                    @if( isset(current($product['prices'][0])[0]['price']) )
                                        <p class="card-text">Starting at ${{ number_format(current($product['prices'][0])[0]['price'], 2) }} per {{ current($product['prices'][0])[0]['label'] }}</p>
                                    @elseif( isset($product['prices'][0]['fixed']['price']) )
                                        <p class="card-text">Starting at ${{ number_format($product['prices'][0]['fixed']['price'], 2) }}</p>
                                    @elseif( isset(current($product['prices'][0])['price']) )
                                        <p class="card-text">Buy now for ${{ number_format(current($product['prices'][0])['price'], 2) }}</p>                                        
                                    @endif
                                    <a href="{{ $product['type'] == 2 ? route('rentmy.package', $product['url']) : route('rentmy.detail', $product['url']) }}" class="btn btn-primary">View Details</a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

@endsection
