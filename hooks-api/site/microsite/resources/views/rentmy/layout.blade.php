<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', '')</title>
    @yield('metas')
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<style>
.nav-menu ul li:nth-child(6)  a {
background-color: #ff0000;
    line-height: 42px;
    color: #fff !important;
    border-radius: 3px;
}
.nav-menu ul li:nth-child(6) a:hover {
    color: #fff !important;
}
.shrink .nav-menu ul li:nth-child(6)  a {
    line-height: 40px !important;
}
.ordernow-btn {
background-color: #f34f3f !important;
color: #fff !important;
}

.monstar-wrapper-content {
            padding: 50px 0;
        }

        .work-box .et_pb_module_header {
            font-weight: 700;
            font-size: 22px;
            color: #000;
            line-height: 40px;
            padding-bottom: 10px;
        }

        .work-box .et_pb_module_header span {
            font-weight: 700 !important;
        }

        .monstertote-devided-container {
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin: 0 !important;
            width: 100% !important;
            max-width: 100% !important;
        }

      
        .et_pb_module_header {
            font-weight: 700;
            font-size: 35px;
            color: #000000;
line-height: 40px;
    padding-bottom: 10px;
        }

        .et_pb_module_header span {
            font-weight: 700 !important;
        }

        .et_pb_button.et_pb_button_1 {
            position: relative;
            padding: 10px 25px 12px;
            border: 2px solid;
            -webkit-border-radius: 3px;
            -moz-border-radius: 3px;
            border-radius: 3px;
            font-weight: 500;
            line-height: 1.7em !important;
            -webkit-transition: all .2s;
            -moz-transition: all .2s;
            transition: all .2s;
            color: #ffffff !important;
            border-width: 0px !important;
            font-size: 19px;
            background-color: #2093d1;
        }

        .et_pb_button.et_pb_button_1 i {
            font-size: 12px;
            margin-left: 10px;
        }
        .work-box-body img {
            width: 60px !important;
        }
.work-box img {
max-width: 100%;
        }

        .paragraph-content iframe {
            width: 100%;
            height: 500px;
        }
        @media (max-width: 767px) {
            .work-box-inner {
                text-align: center;
            }
           .paragraph-content iframe {
                width: 100%;
                height: 400px;
            }
        }
        @media (max-width: 576px) {
           .paragraph-content iframe {
                width: 100%;
                height: 300px;
            }
        }
.monstertote-devided-container {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
}

.monstertote-devided {
    background-image: url("https://totes.rentmy.co/wp-content/uploads/2017/09/divider-small_right-1.png") !important;
    padding-top: 0px;
    padding-right: 0px;
    padding-bottom: 20px;
    padding-left: 0px;
    margin-top: 0px;
    background-color: rgba(32, 147, 209, 0) !important;
}
.et_pb_module_header {
    font-weight: 700;
    font-size: 35px;
    color: #000000;
    line-height: 40px;
    padding-bottom: 10px;
}
.et_pb_module_header span {
    font-weight: 700 !important;
}
.pricing-text {
    padding: 30px 0;
}
.pricing-text-body h1 {
    font-size: 45px;
    text-transform: uppercase;
    font-weight: 500;
    padding-bottom: 20px;
    color: #2393d1;
}
.pricing-text-body p {
    font-size: 23px;
    color: #888;
    line-height: 28px;
}
.pricing-text-body span i {
    font-size: 45px;
    color: #2393d1;
}



/*-- pricing css --*/
.checkout-content {
    background-color: #fff;
}
.checkout-accordion .card-header {
    padding: .30rem 1.25rem;
    margin-bottom: 0;
    background-color: #f2f3f8;
}
.card-header:first-child {
    border-radius: 0;
}
.checkout-accordion .card-header.active {
    background-color: #18a950;
}
.checkout-accordion .btn-link {
    font-weight: 400;
    color: #333;
    background-color: transparent;
    text-decoration: none !important;
    font-size: 25px;
    font-weight: 500;
    padding-left: 25px;
    text-align: center !important;
}
.card-header.active .btn.btn-link {
    color: #fff;
    font-weight: 400;
}
.pricing-area .card-body {
    padding: 0;
}
.theme-btn {
    background-color: #2393d1 !important;
    border: 1px solid #2393d1 !important;
}
.pricing-area h5 b {
    font-weight: bold !important;
}
.pricing-area p {
    color: #333;
    font-weight: 400;
}

.et_pb_contact_form_0.et_pb_contact_form_container {
    border: 2px solid #2093d1;
    padding: 2em;
}
.et_pb_contact_main_title {
    position: relative;
    padding-bottom: 24px;
    font-weight: 400;
    font-size: 22px;
}
p.et_pb_contact_field {
    margin-bottom: 3%;
    padding: 0 0 0 3%;
    background-position: center;
    background-size: cover;
}
.et_pb_contact .et_pb_contact_field_half {
    float: left;
    width: 50%;
}
.et_pb_contact p input, .et_pb_contact p textarea, .et_pb_subscribe .et_pb_contact_field input, .et_pb_subscribe .et_pb_contact_field textarea {
    width: 100%;
    padding: 16px;
    border-width: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    color: #999;
    background-color: #eee;
    font-size: 14px;
    -webkit-appearance: none;
}
.et_contact_bottom_container {
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    float: right;
    margin-top: -1.5%;
    text-align: right;
}
.et_pb_contact_right {
    display: inline-block;
    min-width: 105px;
    margin: 0;
    text-align: right;
}
.et_pb_contact_right .et_pb_contact_captcha_question {
    vertical-align: middle;
}
.et_pb_contact_right p input {
    max-width: 50px;
    padding: 16px;
}
.et_pb_contact_reset, .et_pb_contact_submit {
    display: inline-block;
    margin: 0 0 0 18px;
    font-family: inherit;
    cursor: pointer;
    border: 2px solid #14a751;
    color: #14a751;
    padding: 0 30px;
}

/*-- faq css --*/
.faq-content {
    background-color: #fff;
}
.faq-accordion .card-header {
    padding: .30rem 1.25rem;
    margin-bottom: 0;
    background-color: #f2f3f8;
}
.card-header:first-child {
    border-radius: 0;
}

.faq-accordion .btn-link {
    font-weight: 400;
    color: #333;
    background-color: transparent;
    text-decoration: none !important;
    font-size: 20px;
    font-weight: 500;
    padding-left: 0;
}
.card-header.active .btn.btn-link {
    color: #333;
    font-weight: 500;
}

/*-- customer reviews --*/
.monstercutout-comment-area .card {
    padding: 20px;
    border: 2px solid #2093d1;
}
.monstercutout-comment-area .card-img {
    width: unset;
    max-width: 100%;
    border-radius: 100%;
    border: 1px solid #eee;
}
.monstercutout-comment-area p small {
    font-weight: 700;
    font-size: 16px;
    margin-top: 15px;
}
.et_pb_blurb_description {
    font-size: 17px;
    color: #333;
}
.et_pb_blurb_description ul {
 list-style: disc;
 padding-left: 15px;
}
.et_pb_blurb_description ul li {
    text-align: left !important;
    color: #333;
    font-size: 17px;
    padding: 5px 0;
}
.et_pb_blurb_description ul li span {
    font-weight: 400;
}
.card-text {
    font-weight: 400;
}
.about-link a {
    color: #1276AF;
}
.about-social-icon {
 display: inline-block;
 margin-right: 10px;
}
.about-social-icon a {
 color: #aaa;
}

.nav-menu ul .main-submenu{width:240px !important;}
.nav-menu li .main-submenu ul li a:hover{border-left:2px solid #2392D1 !important;}

.card-header.active .btn.btn-link {
    color: #fff !important;
    font-weight: 500;
}

.checkout-accordion.pricing-accrordion .card-header {
    padding: 0;
    margin-bottom: 0;
    background-color: #f2f3f8;
}

.pricing-accrordion .card-header .btn.btn-link.btn-block.collapsed {
 background-color: #f2f3f8;
color: #333;
}
.pricing-accrordion .card-header .btn.btn-link.btn-block.active {
 background-color: #18a950;
border-radius: 0;
}
.pricing-accrordion .card-header .btn.btn-link.btn-block.collapsed.active {
 background-color: #f2f3f8;
}
.pricing-accrordion .card-header .btn.btn-link.btn-block {
 background-color: #18a950;
border-radius: 0;
color: #fff;
}

.elementor-heading-title {
    color: #20202087;
    -webkit-text-stroke-width: 2px;
    stroke-width: 2px;
    -webkit-text-stroke-color: #000;
    stroke: #000;
    text-shadow: 0px 0px 15px rgba(0,0,0,0.3);
        font-size: 59px;
    margin-top: 85px;
    padding-bottom: 40px;
        text-align: center;
}
.ordernow-btn-area {
    margin-top: 30px;
}
.header-bottom-img img {
    width: 100%;
    height: 10px;
    margin-top: -12px;
}
@media (max-width:992px) {
    .elementor-heading-title {
        font-size: 35px;
        line-height: 40px;
}
}
@media (max-width:767px) {
    .elementor-heading-title {
        font-size: 25px;
        line-height: 30px;
}
}


/*====================================== 
pricing css  
========================================*/
.monstertote-pricing-wrapper {
    font-family: 'Archivo', sans-serif !important;
}
.monstertote-pricing-wrapper h1,
.monstertote-pricing-wrapper h2,
.monstertote-pricing-wrapper h3,
.monstertote-pricing-wrapper h4,
.monstertote-pricing-wrapper h5,
.monstertote-pricing-wrapper h6,
.monstertote-pricing-wrapper span,
.monstertote-pricing-wrapper sub,
.monstertote-pricing-wrapper ul li,
.monstertote-pricing-wrapper {
    font-family: 'Archivo', sans-serif !important;
}
.monstertote-pricing-wrapper {
    background-color: #FFFDF8;
    padding-top: 60px;
}
.mt-select-button button,
.mt-pricing-show h2 {
    font-family: 'Roboto', sans-serif !important;
}
.monstertote-pricing-wrapper .nav-pills {
display: flex;
    margin-left: -7px;
    margin-right: -7px;
}
.monstertote-pricing-wrapper .nav-pills .nav-item {
width: 33.33333%;
    padding-left: 7px;
    padding-right: 7px;
}
.nav.nav-pills .nav-link, .nav.nav-tabs .nav-link {
    background-color: #F0F0F0;
}
.monstertote-pricing-wrapper .nav-pills .nav-link {
    width: 353px;
    height: 73px;
    border: 1px solid #000000;
    border-radius: 10px !important;
    font-weight: 500;
    font-size: 25px;
    line-height: 30px;
    text-align: center;
    color: #152C4C;
    outline: 0;
    cursor: pointer;
}
.monstertote-pricing-wrapper .nav-pills .nav-link.active,
.monstertote-pricing-wrapper .nav-pills .show.nav-link {
    background: linear-gradient(180deg, #107EBB 0%, #22A3EB 100%);
    border: 1px solid #107EBB;
    border-left: 1px solid #22A3EB;
    border-right: 1px solid #22A3EB;
    border-bottom: 1px solid #22A3EB;
    color: #fff;
}
.mt-offer-text {
    font-weight: 400;
    font-size: 13px;
    line-height: 16px;
    color: #696969;
    padding-left: 10px;
    padding-top: 5px;
}
.mt-pricing-box-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -10px;
    margin-right: -10px;
    padding-top: 22px;
}
.mt-pricing-box-column {
    flex: 0 0 25%;
    max-width: 25%;
    padding-left: 10px;
    padding-right: 10px;
}
.mt-pricing-box {
    background: #FBFDFF;
    border-radius: 10px;
    transition: all .5s;
}
.mt-pricing-box:hover {
    background: linear-gradient(180deg, #107EBB 0%, #22A3EB 100%);
}
.mt-pricing-box-active {
    background: linear-gradient(180deg, #107EBB 0%, #22A3EB 100%);
}
.mt-pricing-head {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 90px;
    border-bottom: 1px solid #000000;
    margin-bottom: 30px;
}
.mt-pricing-box-active .mt-pricing-head,
.mt-pricing-box:hover .mt-pricing-head {
    border-bottom: 1px solid #FBFDFF;
}
.mt-princing-headtitle {
    color: #575757;
    font-size: 28px;
    text-align: center;
    font-weight: 700;
    line-height: 38px;
    text-transform: uppercase;
    margin-bottom: 0;
}
.mt-pricing-show {
    text-align: center;
    margin-bottom: 30px;
}
.mt-pricing-show h2 {
    font-weight: 600;
    font-size: 32px;
    line-height: 35px;
    text-align: center;
    color: #575757;
    margin-bottom: 0;
}
.mt-pricing-show h2 sub {
    font-weight: 400;
    font-size: 18px;
    line-height: 18px;
    text-align: center;
    color: #575757;
    bottom: 0;
}
.mt-pricing-show span {
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    color: #575757;
}
.mt-select-button {
    text-align: center;
    margin-bottom: 40px;
}
.mt-select-button .btn {
    background-color: transparent;
    width: 225px;
    height: 55px;
    border: 1px solid #000000;
    border-radius: 30px;
    font-weight: 500;
    font-size: 18px;
    line-height: 18px;
    text-align: center;
    color: #5F5F5F;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.mt-totes,
.mt-labels,
.mt-wheel {
    margin-bottom: 40px;
}
.mt-totes h2,
.mt-labels h2,
.mt-wheel h2 {
    font-weight: 700;
    font-size: 30px;
    line-height: 30px;
    text-align: center;
    color: #494949;
}
.mt-totes ul,
.mt-labels ul,
.mt-wheel ul {
    padding: 0;
    text-align: center;
}
.mt-totes ul li,
.mt-labels ul li,
.mt-wheel ul li  {
    display: inline-block;
}
.mt-totes ul li img,
.mt-labels ul li img,
.mt-wheel ul li img  {
    width: 42px;
    height: 42px;
}
.mt-nonehover-img {
    display: block;
}
.mt-hover-img {
    display: none;
}
.mt-pricing-box-active .mt-nonehover-img,
.mt-pricing-box:hover .mt-nonehover-img {
    display: none;
}
.mt-pricing-box-active .mt-hover-img, 
.mt-pricing-box:hover .mt-hover-img {
    display: block;
}
.mt-wheel {
    padding-bottom: 15px;
}
.mt-pricing-needmoretime {
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    color: #5E5E5E;
    padding-top: 25px;
    padding-bottom: 45px;
    text-align: center;
}
.mt-pricing-box-active .mt-princing-headtitle, 
.mt-pricing-box-active .mt-pricing-show h2, 
.mt-pricing-box-active .mt-pricing-show h2 sub, 
.mt-pricing-box-active .mt-pricing-show span, 
.mt-pricing-box-active .mt-totes h2, 
.mt-pricing-box-active .mt-labels h2,
.mt-pricing-box-active .mt-wheel h2, 
.mt-pricing-box:hover .mt-princing-headtitle,
.mt-pricing-box:hover .mt-pricing-show h2,
.mt-pricing-box:hover .mt-pricing-show h2 sub,
.mt-pricing-box:hover .mt-pricing-show span,
.mt-pricing-box:hover .mt-totes h2,
.mt-pricing-box:hover .mt-labels h2,
.mt-pricing-box:hover .mt-wheel h2 {
    color: #fff;
}
.mt-pricing-box-active .mt-select-button .btn, 
.mt-pricing-box:hover .mt-select-button .btn {
    background-color: #fff;
    border: 1px solid #fff;
}
.monstertote-allow {
    color: #555555;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
}
.monstertote-size {
        font-size: 15px;
    font-weight: 500;
    line-height: 25px;
}
.pricing-gallery-image img {
    margin-bottom: 20px;
}
@media (max-width: 1199px) {
    .monstertote-pricing-wrapper .nav-pills .nav-link {
        width: 293px;
        height: 56px;
        font-size: 18px;
        line-height: 18px;
    }
    .mt-select-button button {
        width: 185px;
        height: 48px;
        font-size: 15px;
    }
}
@media (max-width: 992px) {
    .monstertote-pricing-wrapper .nav-pills .nav-item {
        width: 33%;
    }
    .monstertote-pricing-wrapper .nav-pills .nav-link {
        width: 100%;
    }
    .mt-pricing-box-column {
        flex: 0 0 50%;
        max-width: 50%;
    }
}
@media (max-width: 767px) {
    .monstertote-pricing-wrapper .nav-pills .nav-link {
        height: 45px;
        font-size: 16px;
        line-height: 16px;
    }
}
@media (max-width: 575px) {
    .monstertote-pricing-wrapper .nav-pills .nav-item {
        width: 33%;
    }
    .monstertote-pricing-wrapper .nav-pills .nav-link {
        width: 100%;
        height: 45px;
        font-size: 15px;
    }
    .mt-offer-text {
        font-size: 11px;
        padding-left: 0;
    }
    .mt-pricing-box-column {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .mt-totes, 
    .mt-labels, 
    .mt-wheel {
        margin-bottom: 15px;
    }
    .mt-totes h2,
    .mt-labels h2,
    .mt-wheel h2 {
        font-size: 25px;
        line-height: 25px;
    }
    .mt-pricing-head {
        height: 60px;
        margin-bottom: 20px;
    }
    .mt-princing-headtitle {
        font-size: 25px;
        line-height: 25px;
    }
    .mt-pricing-show {
        margin-bottom: 20px;
    }
    .mt-pricing-show h2 {
        font-size: 25px;
        line-height: 25px;
    }
    .mt-pricing-show h2 sub {
        font-size: 16px;
        line-height: 16px;
    }
    .mt-select-button {
        margin-bottom: 25px;
    }
    .mt-pricing-needmoretime {
        font-size: 15px;
        line-height: 15px;
    }
}

.monstertote-img img {
    width: unset !important;
}
.pricing-text-body.about-monstertote-text p {
    font-size: 23px !important;
    line-height: 30px !important;
}
.eco-friendly-img img {
    width: 100% !important;
}
.header {
    position: relative;
}
.header:before {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    background-image: url(https://s3.us-east-2.amazonaws.com/images.rentmy.co/content-image/213/media/1688395008_kpzcnot7_divider-small_right-1.png);
    width: 100%;
    height: 10px;
    background-size: 100%;
}

.checkout-accordion .accordion&gt;.card&gt;.card-header {
    margin-bottom: 0px !important;
}
.about-monstertote-affiliates img {
    width: 300px !important;
    height: 300px;
}
.monstertote-about-map iframe {
    width: 100%;
    height: 450px;
    border: none;
}
.terms-conditions-content span {
    color: #555;
}
.terms-conditions-content p {
    color: #555;
    font-weight: 400;
}
.terms-conditions-content ul li {
    color: #555;
    font-weight: 400;
}
.privacy-policy-content h3,
.rental-terms-condition h3 {
    font-size: 30px;
    padding-top: 20px;
    margin-bottom: 25px;
}
.privacy-policy-content p,
.rental-terms-condition p {
    color: #555;
    font-size: 16px;
}
.privacy-policy-content p b,
.rental-terms-condition p b {
    color: #555;
}
.message-form .form-control {
    min-height: 59px;
}
.message-form textarea.form-control {
    min-height: 100px;
}
.message-submit-btn {
    min-height: 47px;
    padding: 5px 40px 5px 40px;
    background-color: #2093D1;
}
.footer-contact h4, 
.footer-links h4 {
    color: #333 !important;
}
.contact-us li a {
    color: #666 !important;
    letter-spacing: 0 !important;
    font-weight: 500;
}
.contact-us li a span {
    color: #666 !important;
    font-weight: 500;
}
.footer-links ul li, 
.footer-links ul li a {
    color: #666 !important;
    font-weight: 500 !important;
}
.footer-links ul li a:hover {
    color: #2393D1 !important;
}
.bottom-footer p {
    color: #333 !important;
    font-weight: 500;
}
.bottom-footer a {
    color: #333 !important;
    font-weight: 600 !important;
}
.footer-devided {
    background-color: #f2f3f8!important;
}
.monstertote-pricing-wrapper .nav.nav-pills, 
.monstertote-pricing-wrapper .nav.nav-tabs {
    margin-bottom: 3px;
}
.customer-review iframe {
    width: 100%;
}
.nav-menu ul li a {
    color: #222 !important;
}
.nav-menu ul li a:hover {
    color: #2393D1 !important;
}
.description-list p span {
    font-size: 15px !important;
}
.remove-cart .btn.m-btn--hover-danger:not(.btn-secondary):not(.btn-outline-light) i {
    color: #fff !important;
}
@media (max-width: 575px) {
    .monstertote-img img {
        width: 100% !important;
    }
    .pricing-area iframe {
        width: 100% !important;
        height: 300px;
    }
    .pricing-text-body h1 {
    font-size: 35px;
    line-height: 40px;
    }
}
@media (max-width: 505px) {
.cart-btn-continue {
    margin-top: 0px !important;
}
}
.footer a {
    font-family: 'Roboto', sans-serif !important;
}

@media (min-width: 768px) {
    .video-wrapper video {
        object-fit: cover;
        width: 100%;
        height: 450px;
    }
    .note-video-clip {
        width: 100% !important;
        height: 450px !important;
    }
}
@media (max-width: 767px) {
    .video-wrapper video {
width: 100%;
        height: 300px;
    }
    .note-video-clip {
        width: 100% !important;
        height: 300px !important;
    }
    
}

.preferred-vendors-section p {
text-align: center;
    width: 85%;
    font-size: 15px;
    margin: auto;
    line-height: 22px;
    color: #777;
    font-weight: 400;
margin-bottom: 35px;
}
.preferred-vendors-section .work-box {
text-align: center;
}
.preferred-vendors-section .work-box a {
display: block;
}
.preferred-vendors-section .work-box img {
    width: 230px;
    margin-bottom: 25px;
    height: 70px;
    object-fit: contain;
}
.preferred-vendors-section .work-box .et_pb_module_header:hover {
color: #2393d1;
text-decoration: underline;
}
.preferred-vendors-section .work-box .et_pb_module_header {
font-size: 16px;
}
.preferred-bottom-text {
text-align: center;
    font-size: 15px;
    margin: auto;
    line-height: 22px;
    color: #777;
    font-weight: 400;
    margin-top: 35px;
}

.newslatter-modal {

    position: fixed;

    top: 0;

    left: 0;

    background-color: rgba(0, 0, 0, 0.4);

    width: 100%;

    height: 100%;

    display: flex;

    align-items: center;

    justify-content: center;

    z-index: 999999;

}

.newslatter-close {

    position: absolute;

    right: -5px;

    top: -12px;

    font-size: 15px;

    background-color: #fff;

    border: 1px solid #eee
 !important;

    display: block;

    width: 30px;

    height: 30px;

    display: flex;

    align-items: center;

    justify-content: center;

    border-radius: 100px;

    cursor: pointer;

}

.newslatter-close i {

    font-size: 13px;

    font-weight: bold;

}

.newslatter-modal-content {

    position: relative;

    width: 1000px;

    min-height: 300px;

    background-color: #fff;

    border-radius: 5px;

}

.modal-content-inner {

    display: flex;

    flex-wrap: wrap;

}

.newslatter-modal-leftside {

    width: 50%;

    padding: 0px;

}

.newslatter-modal-rightside {

    width: 50%;

    padding: 30px 50px 30px 50px;

}

.newslatter-modal-rightside h2 {

    text-align: center;

    margin-bottom: 25px;

    font-size: 35px;

    font-weight: 700;

    line-height: 35px;

}

.newslatter-modal-rightside img {

    width: 180px;

    margin-bottom: 25px;

}

.newsletter-form .form-control {

    background-color: #f2f3f8;

    height: 50px;

    border: 1px solid #eee;

    border-radius: 3px;

    text-align: center;

}

.newsletter-form .form-control:hover,

.newsletter-form .form-control:focus,

.newsletter-form .form-control:active {

    box-shadow: none;

    border-color: #14a751;

}

.submitfor-code-btn {

    background-color: #14a751;

    padding: 11px 20px;

    border-radius: 3px;

    color: #fff;

}

.newsletter-form .submitfor-code-btn:hover,

.newsletter-form .submitfor-code-btn:focus,

.newsletter-form .submitfor-code-btn:active {

    box-shadow: none;

    color: #fff;

}

@media (max-width: 767px) {

    .newslatter-modal-leftside {

        display: none;

    }

    .newslatter-modal-rightside {

        width: 100%;

        padding: 30px;

    }

    .newslatter-modal {

        padding-left: 15px;

        padding-right: 15px;

    }

    .newslatter-modal-content {

        width: 100%;

    }

}


/* services*/

.monster-services .work-box {
display: flex;
align-items: center;
} 
.monster-services .work-box img {
width: 200px;
    padding: 10px;
    border: 1px solid #7dcea0;
    border-radius: 100px 0 100px;
    height: 200px;
} 
.monster-services .work-box-body {
text-align: left !important;
padding-left: 20px;
}
.monster-services .work-box .et_pb_module_header {
    line-height: 25px;
}

.monster-services .et_pb_module_header {
    font-weight: 700 !important;
    font-size: 40px !important;
    line-height: 40px !important;
    padding-bottom: 10px !important;
    color: #2393d1 !important;
}


.monster-services .work-box .et_pb_module_header {
    font-weight: 700 !important;
    font-size: 22px !important;
    line-height: 25px !important;
    color: #222 !important;
}
</style>

    @yield('styles')
</head>
<body>

    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="/">Home</a>
                </li>
                @foreach($navigation as $nav)
                    @php if($nav['type'] != 'header') continue; @endphp
                    @php if($nav['status'] != 1) continue; @endphp
                    <li class="nav-item">
                        <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                        @if( !empty($nav['children']) )
                            <ul>
                                @foreach($nav['children'] as $child)
                                    <li><a href="{{ '/' . $child['content_url'] }}">{{ $child['label'] }}</a></li>
                                @endforeach
                            </ul>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        @yield('content')
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    @yield('scripts')

    <footer class="footer mt-5 bg-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    COL1
                </div>
                <div class="col-md-3">
                    <ul class="nav flex-column">
                        @foreach($navigation as $nav)
                            @php if($nav['type'] != 'footer_quick_links') continue; @endphp
                            @php if($nav['status'] != 1) continue; @endphp
                            <li class="nav-item">
                                <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-md-3">
                    <ul class="nav flex-column">
                        @foreach($navigation as $nav)
                            @php if($nav['type'] != 'footer_more_links') continue; @endphp
                            @php if($nav['status'] != 1) continue; @endphp
                            <li class="nav-item">
                                <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-md-3">
                    COL4
                </div>                                
            </div>
        </div>
    </footer>

</body>
</html>
