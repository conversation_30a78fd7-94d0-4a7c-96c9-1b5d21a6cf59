<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', '')</title>
    @yield('metas')
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    @yield('styles')
</head>
<body>

    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="/">Home</a>
                </li>
                @foreach($navigation as $nav)
                    @php if($nav['type'] != 'header') continue; @endphp
                    @php if($nav['status'] != 1) continue; @endphp
                    <li class="nav-item">
                        <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                        @if( !empty($nav['children']) )
                            <ul>
                                @foreach($nav['children'] as $child)
                                    <li><a href="{{ '/' . $child['content_url'] }}">{{ $child['label'] }}</a></li>
                                @endforeach
                            </ul>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <div class="container mt-4">
        @yield('content')
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    @yield('scripts')

    <footer class="footer mt-5 bg-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    COL1
                </div>
                <div class="col-md-3">
                    <ul class="nav flex-column">
                        @foreach($navigation as $nav)
                            @php if($nav['type'] != 'footer_quick_links') continue; @endphp
                            @php if($nav['status'] != 1) continue; @endphp
                            <li class="nav-item">
                                <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-md-3">
                    <ul class="nav flex-column">
                        @foreach($navigation as $nav)
                            @php if($nav['type'] != 'footer_more_links') continue; @endphp
                            @php if($nav['status'] != 1) continue; @endphp
                            <li class="nav-item">
                                <a class="nav-link" href="{{ '/' . $nav['content_url'] }}">{{ $nav['label'] }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="col-md-3">
                    COL4
                </div>                                
            </div>
        </div>
    </footer>

</body>
</html>
