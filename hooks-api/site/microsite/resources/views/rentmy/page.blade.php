@extends('rentmy.layout')

@section('title', $page->meta_title ?? $page->name ?? '')

@section('metas')
    <meta itemprop="name" content="{{ $page->meta_title ?? $page->name ?? $settings->store_slug }}" /> 
    <meta itemprop="description" content="{{ $page->meta_description ?? '' }}" />
    <meta itemprop="keywords" content="{{ $page->meta_keyword ?? '' }}" />
    
    <meta itemprop="image" content="{{ $site_specific['general']['favicon'] ?? '' }}" /> 

    <meta property="og:url" content="{{ Request::url() }}" /> 
    <meta property="og:type" content="website" /> 
    <meta property="og:title" content="{{ $page->meta_title ?? $page->name ?? $settings->store_slug }}" /> 
    <meta property="og:description" content="{{ $page->meta_description ?? '' }}" /> 
    <meta property="og:image" content="{{ $site_specific['general']['favicon'] ?? '' }}" /> 

    <meta name="twitter:card" content="summary_large_image" /> 
    <meta name="twitter:title" content="{{ $page->meta_title ?? $page->name ?? $settings->store_slug }}" /> 
    <meta name="twitter:description" content="{{ $page->meta_description ?? '' }}" /> 

    <script id="structured-data" type="application/ld+json">{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "{{ $page->meta_title ?? $page->name ?? $settings->store_slug }}",
    "url": "{{ Request::url() }}",
    "description": "{{ $page->meta_description ?? '' }}"
    }</script>





@endsection


@section('content')

<div>{!! $page->contents['content'] !!}</div>

@endsection
