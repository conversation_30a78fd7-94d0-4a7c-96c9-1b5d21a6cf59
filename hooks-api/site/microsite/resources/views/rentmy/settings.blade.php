<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Setting</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Edit Setting</h1>

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @if ( isset($success) )
            <div class="alert alert-success">
                <ul>
                    <li>{{ $success }}</li>
                </ul>
            </div>
        @endif

        <form action="{{ route('rentmy.settings-update') }}" method="POST">
            @csrf
            @method('PUT')

            <div class="form-group">
                <label for="store_name">Store Name</label>
                <input type="text" name="store_slug" id="store_slug" class="form-control" value="{{ $setting->store_slug }}" required>
            </div>

            <div class="form-group">
                <label for="store_name">Store Slug</label>
                <input type="text" name="store_name" id="store_name" class="form-control" value="{{ $setting->store_name }}" required>
            </div>

            <div class="form-group">
                <label for="store_id">Store Id</label>
                <input type="number" name="store_id" id="store_id" class="form-control" value="{{ $setting->store_id }}" required>
            </div>

            <div class="form-group">
                <label for="location_id">Location Id</label>
                <input type="number" name="location_id" id="location_id" class="form-control" value="{{ $setting->location_id }}" required>
            </div>

            <div class="form-group">
                <label for="access_token">Access Token</label>
                <textarea name="access_token" id="access_token" class="form-control" rows="4" required>{{ $setting->access_token }}</textarea>
            </div>

            <button type="submit" class="btn btn-primary">Update Setting</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.3/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>
