<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\GeniusController;
use App\Http\Controllers\RentmyController;
use App\Http\Controllers\SettingsController;

// Route::get('/', function () {
//     return view('rentmy.homepage');
// });

// Route::get('/genius',[GeniusController::class,'index'])->name('genius.index');
// Route::get('/genius-cache',[GeniusController::class,'indexWithCache'])->name('genius.indexWithCache');

Route::get('/',[RentmyController::class,'homepage'])->name('rentmy.homepage');

Route::post('/rentmy/get-price-value',[RentmyController::class,'getPriceValue'])->name('rentmy.getPriceValue');
Route::post('/rentmy/get-package-price',[RentmyController::class,'getPackagePrice'])->name('rentmy.getPackagePrice');
Route::post('/rentmy/delete-cart-item',[RentmyController::class,'deleteCartItem'])->name('rentmy.deleteCartItem');
Route::post('/rentmy/update-cart-item',[RentmyController::class,'updateCartItem'])->name('rentmy.updateCartItem');
Route::post('/rentmy/shipping-rate',[RentmyController::class,'shippingRate'])->name('rentmy.shippingRate');
Route::post('/rentmy/cart-delivery',[RentmyController::class,'cartDelivery'])->name('rentmy.cartDelivery');
Route::post('/rentmy/clear-cart',[RentmyController::class,'clearCart'])->name('rentmy.clearCart');
Route::post('/rentmy/check-cart-session',[RentmyController::class,'checkCartSession'])->name('rentmy.checkCartSession');
Route::post('/rentmy/update-cart',[RentmyController::class,'updateCart'])->name('rentmy.updateCart');
Route::get('/rentmy/get-dates-price-duration',[RentmyController::class,'getDatesPriceDuration'])->name('rentmy.getDatesPriceDuration');
Route::post('/rentmy/carts-apply-coupon',[RentmyController::class,'cartsApplyCoupon'])->name('rentmy.cartsApplyCoupon');

Route::get('/rentmy',[RentmyController::class,'index'])->name('rentmy.index');
Route::get('/products-list',[RentmyController::class,'index'])->name('rentmy.products-list');
Route::get('/about',[RentmyController::class,'about'])->name('rentmy.about');
Route::get('/contact',[RentmyController::class,'contact'])->name('rentmy.contact');
Route::get('/terms-and-conditions',[RentmyController::class,'termsAndConditions'])->name('rentmy.terms_and_conditions');
Route::get('/blog/{slug}',[RentmyController::class,'blogPost'])->name('rentmy.blog_post');
Route::get('/blog',[RentmyController::class,'blog'])->name('rentmy.blog');


Route::get('/page/{slug}',[RentmyController::class,'page'])->name('rentmy.page');


Route::get('/rentmy/{id}',[RentmyController::class,'category'])->name('rentmy.category');
Route::get('/rentmy-cache',[RentmyController::class,'index'])->name('rentmy.indexWithCache');

// Route::get('/rentmy-detail/{id}',[RentmyController::class,'detail'])->name('rentmy.detail');
Route::get('/products/{slug}',[RentmyController::class,'detail'])->name('rentmy.detail');
Route::get('/package/{slug}',[RentmyController::class,'package'])->name('rentmy.package');


Route::get('/rentmy-settings',[SettingsController::class,'edit'])->name('rentmy.settings');
Route::put('/rentmy-settings',[SettingsController::class,'update'])->name('rentmy.settings-update');

Route::post('/add-to-cart',[RentmyController::class,'addToCart'])->name('rentmy.addToCart');
Route::post('/add-to-cart-package',[RentmyController::class,'addToCartPackage'])->name('rentmy.addToCartPackage');
Route::get('/cart/{token}',[RentmyController::class,'cart'])->name('rentmy.cart');
Route::get('/checkout',[RentmyController::class,'checkout'])->name('rentmy.checkout');
Route::post('/checkout',[RentmyController::class,'placeOrder'])->name('rentmy.placeOrder');
Route::get('/thank-you',[RentmyController::class,'thankYou'])->name('rentmy.thank_you');


