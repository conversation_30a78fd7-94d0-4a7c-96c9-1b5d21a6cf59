<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\GeniusController;
use App\Http\Controllers\RentmyController;
use App\Http\Controllers\SettingsController;

// Route::get('/', function () {
//     return view('rentmy.homepage');
// });

// Route::get('/genius',[GeniusController::class,'index'])->name('genius.index');
// Route::get('/genius-cache',[GeniusController::class,'indexWithCache'])->name('genius.indexWithCache');

Route::get('/',[RentmyController::class,'homepage'])->name('rentmy.homepage');

Route::get('/rentmy',[RentmyController::class,'index'])->name('rentmy.index');
Route::get('/products-list',[RentmyController::class,'index'])->name('rentmy.products-list');
Route::get('/about',[RentmyController::class,'index'])->name('rentmy.about');
Route::get('/contact',[RentmyController::class,'index'])->name('rentmy.contact');

Route::get('/rentmy/{id}',[RentmyController::class,'category'])->name('rentmy.category');
Route::get('/rentmy-cache',[RentmyController::class,'index'])->name('rentmy.indexWithCache');

// Route::get('/rentmy-detail/{id}',[RentmyController::class,'detail'])->name('rentmy.detail');
Route::get('/products/{slug}',[RentmyController::class,'detail'])->name('rentmy.detail');
Route::get('/package/{slug}',[RentmyController::class,'package'])->name('rentmy.package');


Route::get('/rentmy-settings',[SettingsController::class,'edit'])->name('rentmy.settings');
Route::put('/rentmy-settings',[SettingsController::class,'update'])->name('rentmy.settings-update');

Route::post('/add-to-cart',[RentmyController::class,'addToCart'])->name('rentmy.addToCart');
Route::get('/cart/{token}',[RentmyController::class,'cart'])->name('rentmy.cart');
Route::get('/checkout',[RentmyController::class,'checkout'])->name('rentmy.checkout');
Route::post('/checkout',[RentmyController::class,'placeOrder'])->name('rentmy.placeOrder');
Route::get('/thank-you',[RentmyController::class,'thankYou'])->name('rentmy.thank_you');

Route::post('/rentmy/get-price-value',[RentmyController::class,'getPriceValue'])->name('rentmy.getPriceValue');
Route::post('/rentmy/get-package-price',[RentmyController::class,'getPackagePrice'])->name('rentmy.getPackagePrice');
Route::post('/rentmy/delete-cart-item',[RentmyController::class,'deleteCartItem'])->name('rentmy.deleteCartItem');
Route::post('/rentmy/update-cart-item',[RentmyController::class,'updateCartItem'])->name('rentmy.updateCartItem');
Route::post('/rentmy/shipping-rate',[RentmyController::class,'shippingRate'])->name('rentmy.shippingRate');
Route::post('/rentmy/cart-delivery',[RentmyController::class,'cartDelivery'])->name('rentmy.cartDelivery');
Route::post('/rentmy/clear-cart',[RentmyController::class,'clearCart'])->name('rentmy.clearCart');
Route::post('/rentmy/check-cart-session',[RentmyController::class,'checkCartSession'])->name('rentmy.checkCartSession');
Route::post('/rentmy/update-cart',[RentmyController::class,'updateCart'])->name('rentmy.updateCart');


Route::get('/rsk',[RentmyController::class,'rsk_index'])->name('rsk.index');
Route::get('/rsk/products-list',[RentmyController::class,'rsk_product_list'])->name('rsk.product_list');
Route::get('/rsk/products/{slug}',[RentmyController::class,'rsk_product_details'])->name('rsk.product_details');
Route::get('/rsk/package/{slug}',[RentmyController::class,'rsk_package_details'])->name('rsk.package_details');
Route::get('/rsk/cart',[RentmyController::class,'rsk_cart'])->name('rsk.cart');
Route::get('/rsk/checkout',[RentmyController::class,'rsk_checkout'])->name('rsk.checkout');
Route::get('/rsk/settings',[RentmyController::class,'rsk_settings'])->name('rsk.settings');
Route::put('/rsk/settings',[RentmyController::class,'rsk_update_settings'])->name('rsk.settings-update');
Route::get('/rsk_assets/js/config.js',[RentmyController::class,'rsk_config_js'])->name('rsk.rsk_config_js');
Route::get('/rsk/order-complete',[RentmyController::class,'rsk_order_complete'])->name('rsk.order_complete');







